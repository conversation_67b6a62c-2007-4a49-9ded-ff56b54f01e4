#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
职位分析增强模块
利用MCP工具来增强职位分析功能
"""

import asyncio
import re
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from loguru import logger

try:
    from .mcp_tools_integration import get_tools_manager, fetch_webpage_content, get_library_documentation
    from .mcp_config_manager import get_job_analysis_config
except ImportError:
    logger.warning("无法导入MCP工具集成模块")
    get_tools_manager = None
    fetch_webpage_content = None
    get_library_documentation = None
    get_job_analysis_config = None

@dataclass
class TechStackAnalysis:
    """技术栈分析结果"""
    languages: List[str]
    frameworks: List[str]
    databases: List[str]
    tools: List[str]
    cloud_platforms: List[str]
    methodologies: List[str]
    confidence_score: float
    detected_keywords: List[str]

@dataclass
class CompanyAnalysis:
    """公司分析结果"""
    company_name: str
    industry: str
    company_size: str
    location: str
    company_culture: List[str]
    benefits: List[str]
    growth_stage: str
    reputation_score: float

@dataclass
class SalaryAnalysis:
    """薪资分析结果"""
    salary_range: Tuple[int, int]
    currency: str
    salary_type: str  # hourly, monthly, yearly
    benefits_value: float
    market_competitiveness: float
    negotiation_potential: float

@dataclass
class SkillsMatching:
    """技能匹配分析"""
    required_skills: List[str]
    preferred_skills: List[str]
    matched_skills: List[str]
    missing_skills: List[str]
    skill_gap_score: float
    learning_recommendations: List[str]

@dataclass
class JobCompatibilityScore:
    """职位兼容性评分"""
    overall_score: float
    tech_stack_match: float
    experience_match: float
    location_match: float
    salary_satisfaction: float
    company_culture_fit: float
    growth_potential: float
    detailed_breakdown: Dict[str, Any]

@dataclass
class EnhancedJobAnalysis:
    """增强的职位分析结果"""
    job_id: str
    job_title: str
    company_name: str
    job_url: str
    analysis_timestamp: datetime
    tech_stack: TechStackAnalysis
    company_info: CompanyAnalysis
    salary_info: SalaryAnalysis
    skills_matching: SkillsMatching
    compatibility_score: JobCompatibilityScore
    raw_content: str
    analysis_metadata: Dict[str, Any]

class JobAnalysisEngine:
    """职位分析引擎"""
    
    def __init__(self):
        self.tools_manager = get_tools_manager() if get_tools_manager else None
        self.config = get_job_analysis_config() if get_job_analysis_config else None
        
        # 技术栈关键词库
        self.tech_keywords = {
            "languages": [
                "python", "javascript", "java", "c++", "c#", "go", "rust", "typescript",
                "php", "ruby", "swift", "kotlin", "scala", "r", "matlab", "sql"
            ],
            "frameworks": [
                "react", "vue", "angular", "django", "flask", "fastapi", "express",
                "spring", "laravel", "rails", "asp.net", "node.js", "next.js", "nuxt.js"
            ],
            "databases": [
                "mysql", "postgresql", "mongodb", "redis", "elasticsearch", "cassandra",
                "oracle", "sqlite", "dynamodb", "neo4j", "influxdb"
            ],
            "tools": [
                "docker", "kubernetes", "jenkins", "git", "gitlab", "github", "jira",
                "confluence", "slack", "teams", "figma", "sketch", "postman"
            ],
            "cloud_platforms": [
                "aws", "azure", "gcp", "google cloud", "alibaba cloud", "digitalocean",
                "heroku", "vercel", "netlify"
            ],
            "methodologies": [
                "agile", "scrum", "kanban", "devops", "ci/cd", "tdd", "bdd",
                "microservices", "serverless", "rest", "graphql", "grpc"
            ]
        }
        
        # 用户技能档案（可以从配置文件加载）
        self.user_skills = {
            "languages": ["python", "javascript", "sql"],
            "frameworks": ["django", "react", "fastapi"],
            "databases": ["postgresql", "mongodb", "redis"],
            "tools": ["docker", "git", "jenkins"],
            "cloud_platforms": ["aws", "azure"],
            "methodologies": ["agile", "devops", "ci/cd"]
        }
        
        logger.info("职位分析引擎初始化完成")
    
    async def analyze_job(self, job_url: str, job_data: Dict[str, Any] = None) -> EnhancedJobAnalysis:
        """分析职位"""
        try:
            logger.info(f"开始分析职位: {job_url}")
            
            # 获取职位内容
            if job_data and job_data.get("description"):
                content = job_data["description"]
                logger.info("使用提供的职位描述")
            else:
                content = await self._fetch_job_content(job_url)
            
            if not content:
                raise ValueError("无法获取职位内容")
            
            # 执行各项分析
            tech_stack = await self._analyze_tech_stack(content)
            company_info = await self._analyze_company(content, job_data)
            salary_info = await self._analyze_salary(content)
            skills_matching = await self._analyze_skills_matching(content)
            
            # 计算兼容性评分
            compatibility_score = await self._calculate_compatibility_score(
                tech_stack, company_info, salary_info, skills_matching
            )
            
            # 构建分析结果
            analysis = EnhancedJobAnalysis(
                job_id=self._generate_job_id(job_url),
                job_title=job_data.get("title", "Unknown") if job_data else "Unknown",
                company_name=job_data.get("company", "Unknown") if job_data else "Unknown",
                job_url=job_url,
                analysis_timestamp=datetime.now(),
                tech_stack=tech_stack,
                company_info=company_info,
                salary_info=salary_info,
                skills_matching=skills_matching,
                compatibility_score=compatibility_score,
                raw_content=content,
                analysis_metadata={
                    "content_length": len(content),
                    "analysis_version": "1.0",
                    "mcp_enabled": self.tools_manager is not None
                }
            )
            
            logger.info(f"职位分析完成 - 兼容性评分: {compatibility_score.overall_score:.2f}")
            return analysis
            
        except Exception as e:
            logger.error(f"职位分析失败: {e}")
            raise
    
    async def _fetch_job_content(self, job_url: str) -> str:
        """获取职位内容"""
        if not self.tools_manager or not fetch_webpage_content:
            logger.warning("MCP工具不可用，无法获取职位内容")
            return ""
        
        try:
            result = await fetch_webpage_content(job_url, "plaintext")
            if result.success:
                return result.data.get("content", "")
            else:
                logger.error(f"获取职位内容失败: {result.error}")
                return ""
        except Exception as e:
            logger.error(f"获取职位内容异常: {e}")
            return ""
    
    async def _analyze_tech_stack(self, content: str) -> TechStackAnalysis:
        """分析技术栈"""
        content_lower = content.lower()
        detected_keywords = []
        
        languages = []
        frameworks = []
        databases = []
        tools = []
        cloud_platforms = []
        methodologies = []
        
        # 检测各类技术关键词
        for category, keywords in self.tech_keywords.items():
            for keyword in keywords:
                if keyword.lower() in content_lower:
                    detected_keywords.append(keyword)
                    if category == "languages":
                        languages.append(keyword)
                    elif category == "frameworks":
                        frameworks.append(keyword)
                    elif category == "databases":
                        databases.append(keyword)
                    elif category == "tools":
                        tools.append(keyword)
                    elif category == "cloud_platforms":
                        cloud_platforms.append(keyword)
                    elif category == "methodologies":
                        methodologies.append(keyword)
        
        # 计算置信度
        total_keywords = sum(len(keywords) for keywords in self.tech_keywords.values())
        confidence_score = len(detected_keywords) / total_keywords if total_keywords > 0 else 0
        
        return TechStackAnalysis(
            languages=languages,
            frameworks=frameworks,
            databases=databases,
            tools=tools,
            cloud_platforms=cloud_platforms,
            methodologies=methodologies,
            confidence_score=min(confidence_score * 2, 1.0),  # 调整置信度
            detected_keywords=detected_keywords
        )
    
    async def _analyze_company(self, content: str, job_data: Dict = None) -> CompanyAnalysis:
        """分析公司信息"""
        company_name = job_data.get("company", "Unknown") if job_data else "Unknown"
        location = job_data.get("location", "Unknown") if job_data else "Unknown"
        
        # 从内容中提取公司信息
        industry = self._extract_industry(content)
        company_size = self._extract_company_size(content)
        company_culture = self._extract_company_culture(content)
        benefits = self._extract_benefits(content)
        growth_stage = self._extract_growth_stage(content)
        
        # 计算声誉评分（基于关键词）
        reputation_keywords = [
            "award", "recognized", "leader", "innovative", "growth", "successful",
            "established", "trusted", "industry leader", "market leader"
        ]
        reputation_score = sum(1 for keyword in reputation_keywords if keyword in content.lower()) / len(reputation_keywords)
        
        return CompanyAnalysis(
            company_name=company_name,
            industry=industry,
            company_size=company_size,
            location=location,
            company_culture=company_culture,
            benefits=benefits,
            growth_stage=growth_stage,
            reputation_score=reputation_score
        )
    
    async def _analyze_salary(self, content: str) -> SalaryAnalysis:
        """分析薪资信息"""
        # 薪资正则表达式
        salary_patterns = [
            r'\$([0-9,]+)\s*-\s*\$([0-9,]+)',  # $50,000 - $80,000
            r'([0-9,]+)\s*-\s*([0-9,]+)\s*USD',  # 50,000 - 80,000 USD
            r'([0-9,]+)k\s*-\s*([0-9,]+)k',  # 50k - 80k
        ]
        
        salary_range = (0, 0)
        currency = "USD"
        salary_type = "yearly"
        
        for pattern in salary_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                try:
                    min_sal = int(matches[0][0].replace(',', ''))
                    max_sal = int(matches[0][1].replace(',', ''))
                    
                    # 处理k表示法
                    if 'k' in pattern:
                        min_sal *= 1000
                        max_sal *= 1000
                    
                    salary_range = (min_sal, max_sal)
                    break
                except (ValueError, IndexError):
                    continue
        
        # 分析福利价值
        benefits_keywords = [
            "health insurance", "dental", "vision", "401k", "retirement",
            "vacation", "pto", "flexible", "remote", "bonus", "stock options"
        ]
        benefits_value = sum(1 for keyword in benefits_keywords if keyword in content.lower()) / len(benefits_keywords)
        
        # 市场竞争力（基于薪资范围）
        market_competitiveness = 0.7  # 默认值，可以基于市场数据调整
        if salary_range[1] > 100000:
            market_competitiveness = 0.9
        elif salary_range[1] > 80000:
            market_competitiveness = 0.8
        
        # 谈判潜力
        negotiation_keywords = ["negotiable", "competitive", "commensurate", "DOE"]
        negotiation_potential = 0.5 + (sum(1 for keyword in negotiation_keywords if keyword in content.lower()) * 0.1)
        
        return SalaryAnalysis(
            salary_range=salary_range,
            currency=currency,
            salary_type=salary_type,
            benefits_value=benefits_value,
            market_competitiveness=market_competitiveness,
            negotiation_potential=min(negotiation_potential, 1.0)
        )
    
    async def _analyze_skills_matching(self, content: str) -> SkillsMatching:
        """分析技能匹配"""
        content_lower = content.lower()
        
        # 提取要求的技能
        required_skills = []
        preferred_skills = []
        
        # 从所有技能类别中查找
        all_skills = []
        for category_skills in self.tech_keywords.values():
            all_skills.extend(category_skills)
        
        for skill in all_skills:
            if skill.lower() in content_lower:
                # 简单的启发式规则来区分必需和优选技能
                if any(req_word in content_lower for req_word in ["required", "must", "essential"]):
                    required_skills.append(skill)
                else:
                    preferred_skills.append(skill)
        
        # 计算匹配的技能
        user_all_skills = []
        for category_skills in self.user_skills.values():
            user_all_skills.extend(category_skills)
        
        matched_skills = list(set(required_skills + preferred_skills) & set(user_all_skills))
        missing_skills = list(set(required_skills) - set(user_all_skills))
        
        # 计算技能差距评分
        total_required = len(required_skills) if required_skills else 1
        matched_required = len(set(required_skills) & set(user_all_skills))
        skill_gap_score = matched_required / total_required
        
        # 生成学习建议
        learning_recommendations = []
        for skill in missing_skills[:5]:  # 限制建议数量
            learning_recommendations.append(f"学习 {skill}")
        
        return SkillsMatching(
            required_skills=required_skills,
            preferred_skills=preferred_skills,
            matched_skills=matched_skills,
            missing_skills=missing_skills,
            skill_gap_score=skill_gap_score,
            learning_recommendations=learning_recommendations
        )
    
    async def _calculate_compatibility_score(self, tech_stack: TechStackAnalysis, 
                                           company_info: CompanyAnalysis,
                                           salary_info: SalaryAnalysis,
                                           skills_matching: SkillsMatching) -> JobCompatibilityScore:
        """计算兼容性评分"""
        
        # 技术栈匹配评分
        tech_stack_match = skills_matching.skill_gap_score
        
        # 经验匹配评分（基于技能匹配）
        experience_match = len(skills_matching.matched_skills) / max(len(skills_matching.required_skills + skills_matching.preferred_skills), 1)
        
        # 位置匹配评分（简化处理）
        location_match = 0.8  # 默认值，可以基于用户偏好调整
        
        # 薪资满意度
        salary_satisfaction = salary_info.market_competitiveness
        
        # 公司文化匹配
        company_culture_fit = company_info.reputation_score
        
        # 成长潜力
        growth_potential = 0.7  # 默认值，可以基于公司阶段和行业调整
        if "startup" in company_info.growth_stage.lower():
            growth_potential = 0.9
        elif "enterprise" in company_info.growth_stage.lower():
            growth_potential = 0.6
        
        # 权重配置
        weights = {
            "tech_stack": 0.3,
            "experience": 0.25,
            "location": 0.1,
            "salary": 0.2,
            "culture": 0.1,
            "growth": 0.05
        }
        
        # 计算总体评分
        overall_score = (
            tech_stack_match * weights["tech_stack"] +
            experience_match * weights["experience"] +
            location_match * weights["location"] +
            salary_satisfaction * weights["salary"] +
            company_culture_fit * weights["culture"] +
            growth_potential * weights["growth"]
        )
        
        detailed_breakdown = {
            "weights": weights,
            "raw_scores": {
                "tech_stack_match": tech_stack_match,
                "experience_match": experience_match,
                "location_match": location_match,
                "salary_satisfaction": salary_satisfaction,
                "company_culture_fit": company_culture_fit,
                "growth_potential": growth_potential
            },
            "weighted_scores": {
                "tech_stack": tech_stack_match * weights["tech_stack"],
                "experience": experience_match * weights["experience"],
                "location": location_match * weights["location"],
                "salary": salary_satisfaction * weights["salary"],
                "culture": company_culture_fit * weights["culture"],
                "growth": growth_potential * weights["growth"]
            }
        }
        
        return JobCompatibilityScore(
            overall_score=overall_score,
            tech_stack_match=tech_stack_match,
            experience_match=experience_match,
            location_match=location_match,
            salary_satisfaction=salary_satisfaction,
            company_culture_fit=company_culture_fit,
            growth_potential=growth_potential,
            detailed_breakdown=detailed_breakdown
        )
    
    def _generate_job_id(self, job_url: str) -> str:
        """生成职位ID"""
        import hashlib
        return hashlib.md5(job_url.encode()).hexdigest()[:12]
    
    def _extract_industry(self, content: str) -> str:
        """提取行业信息"""
        industries = [
            "technology", "fintech", "healthcare", "education", "e-commerce",
            "gaming", "media", "consulting", "finance", "manufacturing"
        ]
        
        content_lower = content.lower()
        for industry in industries:
            if industry in content_lower:
                return industry.title()
        
        return "Technology"  # 默认值
    
    def _extract_company_size(self, content: str) -> str:
        """提取公司规模"""
        size_indicators = {
            "startup": ["startup", "early stage", "seed"],
            "small": ["small", "10-50", "< 50"],
            "medium": ["medium", "50-200", "100-500"],
            "large": ["large", "500+", "enterprise", "fortune"]
        }
        
        content_lower = content.lower()
        for size, indicators in size_indicators.items():
            if any(indicator in content_lower for indicator in indicators):
                return size.title()
        
        return "Medium"  # 默认值
    
    def _extract_company_culture(self, content: str) -> List[str]:
        """提取公司文化关键词"""
        culture_keywords = [
            "collaborative", "innovative", "fast-paced", "flexible", "remote-friendly",
            "diverse", "inclusive", "work-life balance", "learning", "growth"
        ]
        
        content_lower = content.lower()
        return [keyword for keyword in culture_keywords if keyword in content_lower]
    
    def _extract_benefits(self, content: str) -> List[str]:
        """提取福利信息"""
        benefits_keywords = [
            "health insurance", "dental", "vision", "401k", "retirement",
            "vacation", "pto", "flexible hours", "remote work", "bonus",
            "stock options", "gym", "free lunch", "learning budget"
        ]
        
        content_lower = content.lower()
        return [benefit for benefit in benefits_keywords if benefit in content_lower]
    
    def _extract_growth_stage(self, content: str) -> str:
        """提取公司发展阶段"""
        stage_indicators = {
            "startup": ["startup", "early stage", "seed", "series a"],
            "growth": ["growth", "scale", "expanding", "series b", "series c"],
            "mature": ["established", "mature", "stable", "public"],
            "enterprise": ["enterprise", "fortune", "multinational", "global"]
        }
        
        content_lower = content.lower()
        for stage, indicators in stage_indicators.items():
            if any(indicator in content_lower for indicator in indicators):
                return stage.title()
        
        return "Growth"  # 默认值

# 便捷函数
async def analyze_job_posting(job_url: str, job_data: Dict[str, Any] = None) -> EnhancedJobAnalysis:
    """分析职位发布"""
    engine = JobAnalysisEngine()
    return await engine.analyze_job(job_url, job_data)

async def batch_analyze_jobs(jobs: List[Dict[str, Any]], max_concurrent: int = 5) -> List[EnhancedJobAnalysis]:
    """批量分析职位"""
    engine = JobAnalysisEngine()
    
    async def analyze_single_job(job):
        try:
            return await engine.analyze_job(job.get("url", ""), job)
        except Exception as e:
            logger.error(f"分析职位失败 {job.get('url', '')}: {e}")
            return None
    
    # 使用信号量限制并发数
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def analyze_with_semaphore(job):
        async with semaphore:
            return await analyze_single_job(job)
    
    tasks = [analyze_with_semaphore(job) for job in jobs]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 过滤掉失败的结果
    return [result for result in results if isinstance(result, EnhancedJobAnalysis)]

def export_analysis_to_json(analysis: EnhancedJobAnalysis, file_path: str = None) -> str:
    """导出分析结果为JSON"""
    if file_path is None:
        file_path = f"job_analysis_{analysis.job_id}.json"
    
    # 转换为可序列化的字典
    analysis_dict = asdict(analysis)
    analysis_dict["analysis_timestamp"] = analysis.analysis_timestamp.isoformat()
    
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(analysis_dict, f, indent=2, ensure_ascii=False)
    
    logger.info(f"分析结果已导出到: {file_path}")
    return file_path