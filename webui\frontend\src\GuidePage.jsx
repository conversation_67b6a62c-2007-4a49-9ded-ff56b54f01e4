import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemButton, // 导入ListItemButton
  ListItemText,
  Paper,
  CircularProgress,
  Chip,
  Container, // 引入Container
  Divider, // 导入Divider
  Alert, // 导入Alert
} from '@mui/material';
import {
  Description as DescriptionIcon,
  Code as CodeIcon,
  Build as BuildIcon,
  Settings as SettingsIcon,
  BugReport as BugReportIcon,
  Assessment as AssessmentIcon,
  MenuBook as MenuBookIcon
} from '@mui/icons-material';

const GuidePage = () => {
  const [selectedDoc, setSelectedDoc] = useState('overview');
  const [docContent, setDocContent] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // 文档分类和结构
  const docCategories = [
    {
      title: '快速入门',
      icon: <MenuBookIcon />,
      items: [
        { id: 'overview', title: '项目概述', file: 'README.md', description: '项目介绍和主要功能' },
        { id: 'installation', title: '安装指南', file: 'README.md', description: '环境配置和依赖安装' },
        { id: 'quickstart', title: '快速开始', file: 'README.md', description: '快速上手使用教程' }
      ]
    },
    {
      title: '技术增强',
      icon: <CodeIcon />,
      items: [
        { id: 'mcp-guide', title: 'MCP增强功能', file: 'MCP_ENHANCEMENT_GUIDE.md', description: 'Model Context Protocol增强指南' },
        { id: 'linkedin-enhancements', title: 'LinkedIn功能增强', file: 'LINKEDIN_ENHANCEMENTS_SUMMARY.md', description: 'LinkedIn自动化功能优化' },
        { id: 'optimization', title: '系统优化总结', file: 'OPTIMIZATION_SUMMARY.md', description: '性能优化和改进措施' }
      ]
    },
    {
      title: '配置指南',
      icon: <SettingsIcon />,
      items: [
        { id: 'chromedriver', title: 'ChromeDriver配置', file: 'UNDETECTED_CHROMEDRIVER_GUIDE.md', description: '反检测浏览器驱动配置' },
        { id: 'loading-detection', title: '加载模式检测', file: 'LOADING_MODE_DETECTION.md', description: '页面加载状态检测机制' }
      ]
    },
    {
      title: '问题修复',
      icon: <BugReportIcon />,
      items: [
        { id: 'pagination-deadloop', title: '分页死循环修复', file: 'PAGINATION_DEADLOOP_FIX.md', description: '解决分页无限循环问题' },
        { id: 'pagination-flip', title: '分页翻转修复', file: 'PAGINATION_FLIP_FIX.md', description: '修复分页方向错误' },
        { id: 'scroll-position', title: '滚动位置修复', file: 'SCROLL_POSITION_FIX_COMPLETE.md', description: '页面滚动定位优化' },
        { id: 'multi-page', title: '多页分页完善', file: 'MULTI_PAGE_PAGINATION_COMPLETE.md', description: '多页面分页机制改进' }
      ]
    },
    {
      title: '测试报告',
      icon: <AssessmentIcon />,
      items: [
        { id: 'pagination-analysis', title: '分页分析报告', file: 'linkedin_pagination_analysis_report.md', description: 'LinkedIn分页机制分析' },
        { id: 'multi-page-test', title: '多页支持测试', file: 'multi_page_support_test_report.md', description: '多页面功能测试结果' },
        { id: 'pagination-test', title: '分页修复测试', file: 'pagination_fix_test_report.md', description: '分页功能修复验证' }
      ]
    },
    {
      title: '开发贡献',
      icon: <BuildIcon />,
      items: [
        { id: 'contributing', title: '贡献指南', file: 'CONTRIBUTING.md', description: '项目贡献规范和流程' },
        { id: 'import-fixes', title: '导入修复总结', file: 'IMPORT_FIXES_SUMMARY.md', description: '模块导入问题解决方案' }
      ]
    }
  ];

  // 加载文档内容
  const loadDocContent = async (filename) => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch(`http://localhost:8003/api/docs/${filename}`);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const content = await response.text();
      setDocContent(content);
    } catch (err) {
      console.error('Error loading document:', err);
      setError('文档加载失败，请稍后重试');
      // 设置默认内容
      setDocContent(getDefaultContent(selectedDoc));
    } finally {
      setLoading(false);
    }
  };

  // 获取默认内容
  const getDefaultContent = (docId) => {
    const defaultContents = {
      'overview': `# 🎯 Jobs Applier AI Agent - 项目概述

## 简介

Jobs Applier AI Agent 是一个基于人工智能的智能求职助手平台，集成了简历优化、求职信生成和LinkedIn自动化等功能。

## 🚀 核心功能

### 🎨 智能简历优化
- **AI驱动定制化**：基于Google Gemini 2.5，智能分析职位要求并优化简历内容
- **PDF上传支持**：上传现有简历进行AI分析和针对性优化
- **专业模板**：多种优雅的简历模板适用于不同行业
- **实时预览**：HTML预览配合高质量PDF导出
- **多语言支持**：完美支持中英文简历

### 💌 智能求职信生成
- **个性化内容**：基于公司信息和职位要求生成定制化求职信
- **公司Logo集成**：自动获取目标公司Logo提升专业度
- **双语支持**：支持中英文求职信生成
- **匹配度分析**：可视化图表展示候选人-职位匹配度

### 🤖 LinkedIn自动化
- **智能职位搜索**：基于关键词、地点等条件精准搜索
- **批量申请**：支持Easy Apply批量申请提升效率
- **多种自动化模式**：Playwright异步/同步、Selenium多种选择
- **智能过滤**：自动筛选合格职位，避免重复申请
- **申请追踪**：详细记录申请历史和成功率

## 🛠️ 技术架构

- **前端**：React + Material-UI
- **后端**：Python + FastAPI
- **AI引擎**：Google Gemini 2.5
- **自动化**：Playwright + Selenium
- **数据存储**：YAML配置文件

## 📦 快速开始

1. 克隆项目仓库
2. 安装Python和Node.js依赖
3. 配置API密钥和用户信息
4. 启动后端和前端服务
5. 访问Web界面开始使用

详细安装步骤请参考安装指南。`,
      'installation': `# 📋 安装指南

## 系统要求

- Python 3.8+
- Node.js 16+
- Chrome浏览器
- 8GB+ RAM推荐

## 安装步骤

### 1. 克隆项目
\`\`\`bash
git clone https://github.com/your-repo/jobs-applier-ai-agent.git
cd jobs-applier-ai-agent
\`\`\`

### 2. 安装Python依赖
\`\`\`bash
pip install -r requirements.txt
\`\`\`

### 3. 安装Node.js依赖
\`\`\`bash
cd webui/frontend
npm install
\`\`\`

### 4. 配置环境
1. 复制配置文件模板
2. 填写API密钥和个人信息
3. 配置LinkedIn账户信息

### 5. 启动服务
\`\`\`bash
# 启动后端
python webui/backend/main.py

# 启动前端
cd webui/frontend
npm run dev
\`\`\`

## 配置说明

详细配置选项请参考配置指南部分。`,
      'quickstart': `# 🚀 快速开始

## 第一次使用

### 1. 访问Web界面
打开浏览器访问 http://localhost:3000

### 2. 配置个人信息
- 进入设置页面
- 填写基本信息和工作偏好
- 配置LinkedIn账户

### 3. 生成简历
- 点击"简历生成"
- 填写个人经历和技能
- 选择合适的模板
- 预览并导出PDF

### 4. 开始自动化申请
- 进入"LinkedIn自动化"
- 设置搜索条件
- 启动自动申请流程
- 监控申请进度

## 常用功能

### 简历优化
1. 上传现有简历或从头创建
2. AI会根据目标职位自动优化内容
3. 实时预览优化效果
4. 导出高质量PDF

### 求职信生成
1. 输入目标公司和职位信息
2. AI生成个性化求职信
3. 查看匹配度分析
4. 下载多种格式

### LinkedIn自动化
1. 配置搜索关键词和筛选条件
2. 选择自动化模式
3. 启动申请流程
4. 实时监控申请状态

## 注意事项

- 首次使用建议先测试少量申请
- 定期检查申请历史和成功率
- 根据反馈调整搜索条件
- 保持LinkedIn账户活跃状态`
    };
    return defaultContents[docId] || '# 文档内容加载中...';
  };

  // 渲染Markdown内容
  const renderMarkdown = (content) => {
    const lines = content.split('\n');
    const elements = [];
    let inCodeBlock = false;
    let codeBlockContent = [];
    let codeBlockLanguage = '';
    let inList = false;
    let listItems = [];

    const flushList = () => {
      if (listItems.length > 0) {
        elements.push(
          <Box 
            key={`list-${elements.length}`} 
            component="ul" 
            sx={{ 
              pl: 3, 
              my: 2, 
              '& li': {
                mb: 1,
                lineHeight: 1.6
              }
            }}
          >
            {listItems.map((item, idx) => (
              <Typography key={idx} component="li" sx={{ mb: 1 }}>
                {item}
              </Typography>
            ))}
          </Box>
        );
        listItems = [];
        inList = false;
      }
    };

    const flushCodeBlock = () => {
      if (codeBlockContent.length > 0) {
        elements.push(
          <Box 
            key={`code-${elements.length}`} 
            sx={{ 
              bgcolor: '#10151a', 
              color: '#f1f1f1',
              p: 2, 
              borderRadius: '12px', 
              my: 3, 
              overflow: 'auto',
              fontFamily: 'Consolas, Monaco, "Courier New", monospace',
              fontSize: '0.9rem',
              border: '1px solid',
              borderColor: 'rgba(100, 100, 120, 0.3)',
              maxWidth: '100%'
            }}
          >
            {codeBlockLanguage && (
              <Typography 
                variant="caption" 
                sx={{ 
                  color: 'grey.400', 
                  display: 'block', 
                  mb: 1,
                  fontWeight: 'bold',
                  textTransform: 'uppercase',
                  letterSpacing: '0.5px'
                }}
              >
                {codeBlockLanguage}
              </Typography>
            )}
            <Box 
              component="pre" 
              sx={{ 
                m: 0, 
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
                overflowX: 'auto'
              }}
            >
              <code>{codeBlockContent.join('\n')}</code>
            </Box>
          </Box>
        );
        codeBlockContent = [];
        codeBlockLanguage = '';
        inCodeBlock = false;
      }
    };

    lines.forEach((line, index) => {
      // 处理代码块
      if (line.startsWith('```')) {
        if (inCodeBlock) {
          flushCodeBlock();
        } else {
          flushList();
          inCodeBlock = true;
          codeBlockLanguage = line.substring(3).trim();
        }
        return;
      }

      if (inCodeBlock) {
        codeBlockContent.push(line);
        return;
      }

      // 处理列表项
      if (line.startsWith('- ') || line.match(/^\d+\. /)) {
        if (!inList) {
          inList = true;
        }
        listItems.push(line.replace(/^[-\d+.] /, ''));
        return;
      } else if (inList && line.trim() !== '') {
        // 继续列表项（缩进内容）
        if (line.startsWith('  ')) {
          listItems[listItems.length - 1] += '\n' + line.trim();
          return;
        } else {
          flushList();
        }
      } else if (inList) {
        flushList();
      }

      // 处理标题
      if (line.startsWith('# ')) {
        flushList();
        elements.push(
          <Typography 
            key={index} 
            variant="h4" 
            component="h1" 
            sx={{ 
              mt: 4, 
              mb: 3, 
              fontWeight: 'bold',
              color: 'primary.main',
              borderBottom: '3px solid',
              borderColor: 'primary.main',
              pb: 1.5,
              fontSize: '1.75rem'
            }}
          >
            {line.substring(2)}
          </Typography>
        );
      } else if (line.startsWith('## ')) {
        flushList();
        elements.push(
          <Typography 
            key={index} 
            variant="h5" 
            component="h2" 
            sx={{ 
              mt: 3.5, 
              mb: 2, 
              fontWeight: 'bold',
              color: 'text.primary',
              fontSize: '1.5rem',
              borderLeft: '4px solid',
              borderColor: 'primary.main',
              pl: 2
            }}
          >
            {line.substring(3)}
          </Typography>
        );
      } else if (line.startsWith('### ')) {
        flushList();
        elements.push(
          <Typography 
            key={index} 
            variant="h6" 
            component="h3" 
            sx={{ 
              mt: 3, 
              mb: 1.5, 
              fontWeight: 'bold',
              color: 'text.primary',
              fontSize: '1.25rem'
            }}
          >
            {line.substring(4)}
          </Typography>
        );
      } else if (line.trim() === '') {
        elements.push(<Box key={index} sx={{ height: 12 }} />);
      } else if (line.trim()) {
        // 处理内联代码
        const processInlineCode = (text) => {
          const parts = text.split(/(`[^`]+`)/);
          return parts.map((part, idx) => {
            if (part.startsWith('`') && part.endsWith('`')) {
              return (
                <Box 
                  key={idx}
                  component="code" 
                  sx={{ 
                    bgcolor: 'grey.100', 
                    color: 'error.main',
                    px: 0.5, 
                    py: 0.25,
                    borderRadius: 0.5,
                    fontFamily: 'monospace',
                    fontSize: '0.875em'
                  }}
                >
                  {part.slice(1, -1)}
                </Box>
              );
            }
            return part;
          });
        };

        elements.push(
          <Typography 
            key={index} 
            paragraph 
            sx={{ 
              mb: 2,
              lineHeight: 1.8,
              fontSize: '1rem',
              color: 'text.primary'
            }}
          >
            {processInlineCode(line)}
          </Typography>
        );
      }
    });

    // 清理剩余内容
    flushList();
    flushCodeBlock();

    return elements;
  };

  // 初始加载
  useEffect(() => {
    const currentItem = docCategories.flatMap(cat => cat.items).find(item => item.id === selectedDoc);
    if (currentItem) {
      loadDocContent(currentItem.file);
    }
  }, [selectedDoc]);

  return (
    <Container maxWidth="xl" sx={{ pt: 4, pb: 4 }}>
      <Box sx={{
        display: 'flex',
        gap: 3,
        alignItems: 'flex-start',
        position: 'relative',
        zIndex: 1,
        ml: 'calc(10cm - 2cm)', // 整体左移2cm
      }}>
        {/* 左侧导航栏 */}
        <Paper
          elevation={4}
          sx={{
            minWidth: '320px',
            width: '25%',
            maxWidth: '360px',
            height: 'calc(100vh - 120px)',
            maxHeight: '800px',
            overflowY: 'auto',
            position: 'sticky',
            top: '80px',
            bgcolor: '#10151a', // 统一背景色
            borderRadius: '16px', // 统一圆角
            boxShadow: 'none',
            zIndex: 1001,
            p: 2.5,
            '&::-webkit-scrollbar': {
              width: '8px',
            },
          }}
          >
            <Box sx={{ p: 2, borderBottom: '1.5px solid', borderColor: '#00eaff' }}>
              <Typography variant="h5" sx={{ fontWeight: 'bold', display: 'flex', alignItems: 'center', gap: 1, color: '#00eaff', letterSpacing: 1 }}>
                <DescriptionIcon sx={{ color: '#00eaff' }} />
                使用指南
              </Typography>
              <Typography variant="body1" sx={{ mt: 0.5, color: '#00eaff', opacity: 0.8 }}>
                项目文档和技术指南
              </Typography>
            </Box>
            {docCategories.map((category, categoryIndex) => (
              <Box key={categoryIndex}>
                <Box sx={{ p: 2, bgcolor: 'grey.900' }}>
                  <Typography 
                    variant="subtitle1" 
                    sx={{ fontWeight: 'bold', color: '#00eaff', display: 'flex', alignItems: 'center', gap: 1, letterSpacing: 1 }}>
                    {category.icon}
                    {category.title}
                  </Typography>
                </Box>
                <List dense>
                  {category.items.map((item) => (
                    <ListItem key={item.id} disablePadding>
                      <ListItemButton
                        selected={selectedDoc === item.id}
                        onClick={() => setSelectedDoc(item.id)}
                        sx={{
                          pl: 3,
                          borderRadius: '8px',
                          mb: 1,
                          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                          color: selectedDoc === item.id ? '#ffffff' : '#e0e0e0',
                          backgroundColor: selectedDoc === item.id ? 'rgba(0, 212, 255, 0.12)' : 'transparent',
                          fontWeight: selectedDoc === item.id ? 600 : 500,
                          '&:hover': {
                            color: '#ffffff',
                            backgroundColor: 'rgba(255, 255, 255, 0.05)',
                            '&::after': {
                              content: '""',
                              position: 'absolute',
                              bottom: 0,
                              left: '50%',
                              transform: 'translateX(-50%)',
                              width: '60%',
                              height: '2px',
                              backgroundColor: '#00d4ff',
                              borderRadius: '1px',
                              opacity: 0.8
                            }
                          }
                        }}
                      >
                        <ListItemText
                          primary={item.title}
                          secondary={item.description}
                          primaryTypographyProps={{ fontSize: '1.1rem', fontWeight: selectedDoc === item.id ? 'bold' : 'normal' }}
                          secondaryTypographyProps={{ fontSize: '0.9rem' }}
                        />
                      </ListItemButton>
                    </ListItem>
                  ))}
                </List>
                {categoryIndex < docCategories.length - 1 && <Divider />}
              </Box>
            ))}
          </Paper>
          {/* 右侧内容区域 */}
          <Paper
            elevation={0}
            sx={{
              flex: 1,
              minWidth: 0,
              maxWidth: 'calc(100vw - 480px - 8.5cm)',
              height: 'calc(100vh - 120px)',
              maxHeight: '800px',
              overflowY: 'auto',
              bgcolor: 'transparent',
              borderRadius: '16px',
              boxShadow: 'none',
              zIndex: 1000,
              p: 0,
              '&::-webkit-scrollbar': {
                width: '8px'
              }
            }}>
              {(() => {
                if (loading) {
                  return (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                      <CircularProgress color="info" />
                    </Box>
                  );
                }
                if (error) {
                  return (
                    <Alert severity="error" sx={{ m: 3 }}>
                      {error}
                    </Alert>
                  );
                }
                const currentDoc = docCategories.flatMap(cat => cat.items).find(item => item.id === selectedDoc);
                return (
                  <>
                    {/* 内容头部 */}
                    <Box
                      sx={{
                        p: 3,
                        bgcolor: '#10151a',
                        borderBottom: '2px solid',
                        borderColor: '#00eaff',
                        borderRadius: '16px 16px 0 0',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 2,
                        justifyContent: 'space-between',
                        minHeight: 64,
                        position: 'sticky',
                        top: 0,
                        zIndex: 10,
                      }}
                    >
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', flex: 1 }}>
                        <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#fff', mb: 0.5, letterSpacing: 1, fontSize: '2rem', lineHeight: 1.1 }}>
                          {currentDoc?.title}
                        </Typography>
                        <Typography variant="body2" sx={{ color: '#fff', opacity: 0.8, fontSize: '1rem', mt: 0.5 }}>
                          {currentDoc?.description}
                        </Typography>
                      </Box>
                      <Chip
                        label={currentDoc?.file}
                        size="small"
                        variant="outlined"
                        sx={{ color: '#00eaff', borderColor: '#00eaff', fontWeight: 700, fontSize: 15, px: 1.5, background: '#181a1b', height: 32 }}
                      />
                    </Box>
                    {/* 文档内容 */}
                    <Box sx={{ p: 4, flex: 1, overflowY: 'auto', bgcolor: 'grey.900', fontSize: 15, color: '#fff', lineHeight: 1.85, borderRadius: '0 0 16px 16px', paddingTop: 2 }}>
                      {renderMarkdown(docContent)}
                    </Box>
                  </>
                );
              })()}
            </Paper>
          </Box>
        </Container>
      );
    };

    export default GuidePage;