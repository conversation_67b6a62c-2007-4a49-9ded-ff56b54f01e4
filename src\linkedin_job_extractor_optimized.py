# -*- coding: utf-8 -*-
"""
LinkedIn职位提取器 - 优化版
基于用户反馈和实际测试结果的优化实现

主要优化点：
1. 信任准确的选择器(.scaffold-layout__list-item)
2. 简化HTML处理，直接提取职位卡片
3. 增强职位信息提取（包含更多元数据）
4. 优化滚动策略（基于25个职位/页的规律）
5. 保留LLM智能过滤功能
"""

import time
import json
import re
from typing import List, Dict, Optional, Union, Tuple
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class LinkedInJobExtractorOptimized:
    """
    LinkedIn职位提取器 - 优化版
    专注于高效准确的职位提取和信息解析
    """
    
    def __init__(self, driver, llm_parser=None):
        self.driver = driver
        self.wait = WebDriverWait(driver, 10)
        self.llm_parser = llm_parser
        
        # 主要选择器 - 基于实际测试结果优化
        self.primary_job_selector = ".scaffold-layout__list-item"  # 最准确的选择器
        
        # 备用选择器（按优先级排序）
        self.backup_selectors = [
            "[data-job-id]",
            ".job-card-container",
            ".jobs-search-results__list-item",
            ".base-search-card"
        ]
        
        # 分页相关配置
        self.jobs_per_page = 25  # LinkedIn标准分页
        self.max_pages = 40  # 最大页数限制
        
        # 推荐职位检测关键词
        self.recommendation_keywords = [
            "Top job picks for you",
            "Based on your profile", 
            "Your job alert",
            "Remote opportunities",
            "Hybrid opportunities",
            "Because you expressed interest",
            "More jobs for you",
            "Recommended for you",
            "推荐职位",
            "为您推荐"
        ]
        
        # 职位状态标识符
        self.job_status_indicators = {
            'promoted': ['Promoted', '推广', 'Sponsored'],
            'viewed': ['Viewed', '已查看'],
            'actively_reviewing': ['Actively reviewing applicants', '正在积极审核申请者'],
            'easy_apply': ['Easy Apply', '轻松申请', '抢先申请'],
            'recently_posted': ['hours ago', 'hour ago', 'day ago', 'days ago', 'week ago', 'weeks ago', '小时前', '天前', '周前']
        }
    
    def extract_jobs_from_current_page(self) -> Dict[str, Union[List[Dict], int, str]]:
        """
        从当前页面提取所有职位信息
        
        Returns:
            Dict: 包含职位列表、统计信息和元数据
        """
        try:
            logger.info("🔍 开始从当前页面提取职位信息...")
            
            # 等待页面加载完成
            self._wait_for_page_load()
            
            # 获取职位元素
            job_elements = self._get_job_elements()
            
            if not job_elements:
                logger.warning("未找到任何职位元素")
                return self._create_empty_result()
            
            logger.info(f"找到 {len(job_elements)} 个职位元素")
            
            # 提取职位信息
            jobs = []
            for i, element in enumerate(job_elements):
                try:
                    job_info = self._extract_enhanced_job_info(element, i)
                    if job_info:
                        jobs.append(job_info)
                except Exception as e:
                    logger.debug(f"跳过职位元素 {i}: {e}")
                    continue
            
            # 过滤推荐职位（如果启用LLM）
            if self.llm_parser:
                jobs = self._filter_recommended_jobs(jobs)
            
            result = {
                'jobs': jobs,
                'total_found': len(jobs),
                'page_info': self._get_page_info(),
                'extraction_time': datetime.now().isoformat(),
                'selector_used': self.primary_job_selector
            }
            
            logger.info(f"✅ 职位提取完成: {len(jobs)} 个有效职位")
            return result
            
        except Exception as e:
            logger.error(f"❌ 职位提取失败: {e}")
            return self._create_empty_result(error=str(e))
    
    def _wait_for_page_load(self, timeout: int = 10):
        """等待页面加载完成"""
        try:
            # 等待主要容器加载
            self.wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".scaffold-layout__list"))
            )
            
            # 额外等待确保动态内容加载
            time.sleep(2)
            
        except TimeoutException:
            logger.warning("页面加载超时，继续尝试提取")
    
    def _get_job_elements(self) -> List:
        """
        获取职位元素列表
        优先使用主要选择器，失败时使用备用选择器
        """
        # 首先尝试主要选择器
        try:
            elements = self.driver.find_elements(By.CSS_SELECTOR, self.primary_job_selector)
            if elements:
                logger.info(f"使用主要选择器 {self.primary_job_selector} 找到 {len(elements)} 个元素")
                return elements
        except Exception as e:
            logger.debug(f"主要选择器失败: {e}")
        
        # 尝试备用选择器
        for selector in self.backup_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    logger.info(f"使用备用选择器 {selector} 找到 {len(elements)} 个元素")
                    return elements
            except Exception as e:
                logger.debug(f"备用选择器 {selector} 失败: {e}")
                continue
        
        logger.warning("所有选择器都未找到职位元素")
        return []
    
    def _extract_enhanced_job_info(self, job_element, index: int) -> Optional[Dict]:
        """
        提取增强的职位信息
        包含更多元数据和状态信息
        """
        try:
            # 获取职位卡片HTML
            card_html = job_element.get_attribute('outerHTML')
            
            # 基础信息提取
            job_info = {
                'index': index,
                'extraction_time': datetime.now().isoformat()
            }
            
            # 1. 职位标题和链接
            title_info = self._extract_title_and_url(job_element)
            job_info.update(title_info)
            
            # 2. 公司信息
            company_info = self._extract_company_info(job_element)
            job_info.update(company_info)
            
            # 3. 地点信息
            location_info = self._extract_location_info(job_element)
            job_info.update(location_info)
            
            # 4. 职位状态和标签
            status_info = self._extract_job_status(job_element, card_html)
            job_info.update(status_info)
            
            # 5. 时间信息
            time_info = self._extract_time_info(job_element, card_html)
            job_info.update(time_info)
            
            # 6. Easy Apply状态
            easy_apply_info = self._extract_easy_apply_status(job_element, card_html)
            job_info.update(easy_apply_info)
            
            # 7. 职位ID
            job_id = self._extract_job_id_from_element(job_element, job_info.get('url', ''))
            job_info['job_id'] = job_id
            
            # 8. 原始HTML（用于LLM分析）
            job_info['raw_html'] = card_html
            job_info['html_length'] = len(card_html)
            
            return job_info
            
        except Exception as e:
            logger.debug(f"提取职位信息失败 (索引 {index}): {e}")
            return None
    
    def _extract_title_and_url(self, element) -> Dict[str, str]:
        """提取职位标题和链接"""
        title_selectors = [
            "h3 a",
            ".job-card-list__title a",
            ".jobs-unified-top-card__job-title a",
            "[data-control-name='job_card_title'] a",
            ".base-search-card__title a"
        ]
        
        for selector in title_selectors:
            try:
                title_element = element.find_element(By.CSS_SELECTOR, selector)
                return {
                    'title': title_element.text.strip(),
                    'url': title_element.get_attribute('href')
                }
            except:
                continue
        
        return {'title': '未知职位', 'url': ''}
    
    def _extract_company_info(self, element) -> Dict[str, str]:
        """提取公司信息"""
        company_selectors = [
            ".job-card-container__company-name a",
            ".jobs-unified-top-card__company-name a",
            ".job-card-container__primary-description",
            "[data-control-name='job_card_company_name'] a",
            ".base-search-card__subtitle a"
        ]
        
        for selector in company_selectors:
            try:
                company_element = element.find_element(By.CSS_SELECTOR, selector)
                return {
                    'company': company_element.text.strip(),
                    'company_url': company_element.get_attribute('href') or ''
                }
            except:
                continue
        
        return {'company': '未知公司', 'company_url': ''}
    
    def _extract_location_info(self, element) -> Dict[str, str]:
        """提取地点信息"""
        location_selectors = [
            ".job-card-container__metadata-item",
            ".jobs-unified-top-card__bullet",
            ".job-card-container__metadata-wrapper span",
            ".base-search-card__metadata span"
        ]
        
        for selector in location_selectors:
            try:
                location_elements = element.find_elements(By.CSS_SELECTOR, selector)
                for loc_elem in location_elements:
                    text = loc_elem.text.strip()
                    # 简单的地点识别逻辑
                    if text and (',' in text or '市' in text or '省' in text or 'Remote' in text):
                        return {'location': text}
            except:
                continue
        
        return {'location': '未知地点'}
    
    def _extract_job_status(self, element, card_html: str) -> Dict[str, Union[bool, List[str]]]:
        """提取职位状态信息"""
        status_info = {
            'is_promoted': False,
            'is_viewed': False,
            'is_actively_reviewing': False,
            'status_tags': []
        }
        
        # 检查各种状态标识
        for status_type, keywords in self.job_status_indicators.items():
            if status_type == 'recently_posted':  # 时间信息单独处理
                continue
                
            for keyword in keywords:
                if keyword in card_html:
                    if status_type == 'promoted':
                        status_info['is_promoted'] = True
                        status_info['status_tags'].append('Promoted')
                    elif status_type == 'viewed':
                        status_info['is_viewed'] = True
                        status_info['status_tags'].append('Viewed')
                    elif status_type == 'actively_reviewing':
                        status_info['is_actively_reviewing'] = True
                        status_info['status_tags'].append('Actively reviewing applicants')
                    break
        
        return status_info
    
    def _extract_time_info(self, element, card_html: str) -> Dict[str, str]:
        """提取时间信息"""
        time_patterns = [
            r'(\d+)\s*(hour|hours|day|days|week|weeks)\s*ago',
            r'(\d+)\s*(小时|天|周)前'
        ]
        
        for pattern in time_patterns:
            match = re.search(pattern, card_html, re.IGNORECASE)
            if match:
                return {
                    'posted_time': match.group(0),
                    'is_recent': True
                }
        
        return {
            'posted_time': '未知时间',
            'is_recent': False
        }
    
    def _extract_easy_apply_status(self, element, card_html: str) -> Dict[str, bool]:
        """提取Easy Apply状态"""
        easy_apply_indicators = [
            'Easy Apply', '轻松申请', '抢先申请',
            'job-posting-benefits', 'Apply now', 'easy-apply'
        ]
        
        # 检查HTML内容
        for indicator in easy_apply_indicators:
            if indicator in card_html:
                return {'is_easy_apply': True}
        
        # 检查按钮元素
        easy_apply_selectors = [
            ".jobs-apply-button--top-card",
            ".job-card-container__apply-method",
            "[data-control-name*='easy_apply']",
            ".easy-apply-button"
        ]
        
        for selector in easy_apply_selectors:
            try:
                easy_element = element.find_element(By.CSS_SELECTOR, selector)
                if easy_element and easy_element.is_displayed():
                    return {'is_easy_apply': True}
            except:
                continue
        
        return {'is_easy_apply': False}
    
    def _extract_job_id_from_element(self, element, url: str) -> str:
        """从元素或URL提取职位ID"""
        # 方法1: 从data-job-id属性提取
        try:
            job_id = element.get_attribute('data-job-id')
            if job_id:
                return job_id
        except:
            pass
        
        # 方法2: 从URL提取
        if url:
            match = re.search(r'/jobs/view/(\d+)', url)
            if match:
                return match.group(1)
        
        # 方法3: 从子元素的data-job-id提取
        try:
            job_id_element = element.find_element(By.CSS_SELECTOR, "[data-job-id]")
            job_id = job_id_element.get_attribute('data-job-id')
            if job_id:
                return job_id
        except:
            pass
        
        return f"unknown_{int(time.time())}"
    
    def _filter_recommended_jobs(self, jobs: List[Dict]) -> List[Dict]:
        """使用LLM过滤推荐职位"""
        if not self.llm_parser or not jobs:
            return jobs
        
        try:
            logger.info("🤖 使用LLM过滤推荐职位...")
            
            # 准备职位数据用于LLM分析
            jobs_json = self._prepare_jobs_for_llm(jobs)
            
            # 调用LLM进行分析
            filtered_jobs = self._llm_filter_jobs(jobs_json)
            
            logger.info(f"LLM过滤完成: {len(jobs)} -> {len(filtered_jobs)} 个职位")
            return filtered_jobs
            
        except Exception as e:
            logger.warning(f"LLM过滤失败，返回原始结果: {e}")
            return jobs
    
    def _prepare_jobs_for_llm(self, jobs: List[Dict]) -> str:
        """准备职位数据用于LLM分析"""
        # 简化职位数据，只保留关键信息
        simplified_jobs = []
        for job in jobs:
            simplified_job = {
                'index': job.get('index'),
                'title': job.get('title'),
                'company': job.get('company'),
                'location': job.get('location'),
                'status_tags': job.get('status_tags', []),
                'is_promoted': job.get('is_promoted', False),
                'posted_time': job.get('posted_time'),
                'html_snippet': job.get('raw_html', '')[:500]  # 只取前500字符
            }
            simplified_jobs.append(simplified_job)
        
        return json.dumps(simplified_jobs, ensure_ascii=False, indent=2)
    
    def _llm_filter_jobs(self, jobs_json: str) -> List[Dict]:
        """使用LLM过滤职位"""
        # 这里需要实现LLM调用逻辑
        # 暂时返回原始数据，后续可以集成Gemini API
        # TODO: 实现LLM过滤逻辑
        return json.loads(jobs_json)
    
    def _get_page_info(self) -> Dict[str, Union[int, str]]:
        """获取页面信息"""
        try:
            # 尝试获取分页信息
            current_url = self.driver.current_url
            
            # 从URL提取页面信息
            page_match = re.search(r'start=(\d+)', current_url)
            current_start = int(page_match.group(1)) if page_match else 0
            current_page = (current_start // self.jobs_per_page) + 1
            
            return {
                'current_page': current_page,
                'jobs_per_page': self.jobs_per_page,
                'start_index': current_start,
                'url': current_url
            }
        except Exception as e:
            logger.debug(f"获取页面信息失败: {e}")
            return {
                'current_page': 1,
                'jobs_per_page': self.jobs_per_page,
                'start_index': 0,
                'url': self.driver.current_url
            }
    
    def _create_empty_result(self, error: str = None) -> Dict:
        """创建空结果"""
        result = {
            'jobs': [],
            'total_found': 0,
            'page_info': self._get_page_info(),
            'extraction_time': datetime.now().isoformat(),
            'selector_used': self.primary_job_selector
        }
        
        if error:
            result['error'] = error
        
        return result
    
    def extract_jobs_with_pagination(self, max_pages: int = None) -> Dict[str, Union[List[Dict], int, str]]:
        """
        使用分页提取所有职位
        优化的分页策略，基于25个职位/页的规律
        """
        if max_pages is None:
            max_pages = self.max_pages
        
        try:
            logger.info(f"🔄 开始分页职位提取，最大页数: {max_pages}")
            
            all_jobs = []
            current_page = 1
            
            while current_page <= max_pages:
                logger.info(f"📄 提取第 {current_page} 页...")
                
                # 提取当前页面职位
                page_result = self.extract_jobs_from_current_page()
                page_jobs = page_result.get('jobs', [])
                
                if not page_jobs:
                    logger.info("当前页面无职位，停止分页")
                    break
                
                all_jobs.extend(page_jobs)
                logger.info(f"第 {current_page} 页提取到 {len(page_jobs)} 个职位")
                
                # 检查是否还有下一页
                if not self._navigate_to_next_page():
                    logger.info("已到达最后一页")
                    break
                
                current_page += 1
                
                # 页面间延迟
                time.sleep(2)
            
            result = {
                'jobs': all_jobs,
                'total_found': len(all_jobs),
                'pages_processed': current_page,
                'extraction_time': datetime.now().isoformat(),
                'method': 'pagination'
            }
            
            logger.info(f"✅ 分页提取完成: 共 {len(all_jobs)} 个职位，处理了 {current_page} 页")
            return result
            
        except Exception as e:
            logger.error(f"❌ 分页提取失败: {e}")
            return {
                'jobs': all_jobs if 'all_jobs' in locals() else [],
                'total_found': len(all_jobs) if 'all_jobs' in locals() else 0,
                'pages_processed': current_page if 'current_page' in locals() else 0,
                'extraction_time': datetime.now().isoformat(),
                'method': 'pagination',
                'error': str(e)
            }
    
    def _navigate_to_next_page(self) -> bool:
        """
        导航到下一页
        
        Returns:
            bool: 是否成功导航到下一页
        """
        try:
            # 查找下一页按钮
            next_button_selectors = [
                "[aria-label='Next']",
                ".artdeco-pagination__button--next",
                "[data-control-name='pagination_next']",
                ".pv3 button[aria-label*='Next']"
            ]
            
            for selector in next_button_selectors:
                try:
                    next_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if next_button and next_button.is_enabled() and next_button.is_displayed():
                        # 滚动到按钮位置
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", next_button)
                        time.sleep(1)
                        
                        # 点击下一页
                        next_button.click()
                        
                        # 等待页面加载
                        self._wait_for_page_load()
                        
                        logger.info("成功导航到下一页")
                        return True
                except:
                    continue
            
            logger.info("未找到可用的下一页按钮")
            return False
            
        except Exception as e:
            logger.debug(f"导航到下一页失败: {e}")
            return False