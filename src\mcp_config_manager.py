#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP配置管理模块
管理MCP集成的各种配置选项和设置
"""

import json
import os
from typing import Dict, Any, Optional, List
from pathlib import Path
from dataclasses import dataclass, asdict
from loguru import logger

@dataclass
class MCPServerConfig:
    """MCP服务器配置"""
    name: str
    enabled: bool = True
    timeout: int = 30
    retry_count: int = 3
    rate_limit: float = 1.0  # 请求间隔秒数
    custom_settings: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.custom_settings is None:
            self.custom_settings = {}

@dataclass
class MultFetchConfig:
    """Mult-Fetch MCP服务器配置"""
    enabled: bool = True
    timeout: int = 30
    max_retries: int = 3
    content_size_limit: int = 50000
    enable_content_splitting: bool = True
    extract_content: bool = True
    include_metadata: bool = True
    fallback_to_original: bool = True
    use_browser: bool = False
    auto_detect_mode: bool = True
    save_cookies: bool = True
    
    def to_fetch_params(self) -> Dict[str, Any]:
        """转换为fetch参数"""
        return {
            "timeout": self.timeout * 1000,  # 转换为毫秒
            "contentSizeLimit": self.content_size_limit,
            "enableContentSplitting": self.enable_content_splitting,
            "extractContent": self.extract_content,
            "includeMetadata": self.include_metadata,
            "fallbackToOriginal": self.fallback_to_original,
            "useBrowser": self.use_browser,
            "autoDetectMode": self.auto_detect_mode,
            "saveCookies": self.save_cookies
        }

@dataclass
class Context7Config:
    """Context7 MCP服务器配置"""
    enabled: bool = True
    timeout: int = 30
    max_tokens: int = 10000
    default_libraries: List[str] = None
    cache_enabled: bool = True
    cache_ttl: int = 3600  # 缓存时间（秒）
    
    def __post_init__(self):
        if self.default_libraries is None:
            self.default_libraries = [
                "react", "vue", "angular", "node.js", "python", 
                "django", "flask", "fastapi", "express", "spring"
            ]

@dataclass
class AutomationConfig:
    """自动化配置"""
    selenium_enabled: bool = True
    playwright_enabled: bool = False
    default_automation: str = "selenium"
    headless: bool = False
    implicit_wait: int = 10
    page_load_timeout: int = 30
    request_delay_range: tuple = (1, 3)  # 请求延迟范围（秒）
    max_retry_attempts: int = 3
    
    def __post_init__(self):
        if self.default_automation not in ["selenium", "playwright"]:
            self.default_automation = "selenium"

@dataclass
class JobAnalysisConfig:
    """职位分析配置"""
    enabled: bool = True
    compatibility_threshold: float = 0.7
    max_jobs_to_analyze: int = 50
    analysis_timeout: int = 60
    tech_stack_analysis: bool = True
    company_analysis: bool = True
    salary_analysis: bool = True
    location_analysis: bool = True
    skills_matching: bool = True
    
class MCPConfigManager:
    """MCP配置管理器"""
    
    def __init__(self, config_file: str = None):
        self.config_file = config_file or "mcp_config.json"
        self.config_path = Path(self.config_file)
        
        # 默认配置
        self.mult_fetch = MultFetchConfig()
        self.context7 = Context7Config()
        self.automation = AutomationConfig()
        self.job_analysis = JobAnalysisConfig()
        self.mcp_servers = {
            "mult-fetch": MCPServerConfig("mult-fetch-mcp-server"),
            "context7": MCPServerConfig("context7-mcp")
        }
        
        # 加载配置
        self.load_config()
    
    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 更新配置
                self._update_from_dict(config_data)
                logger.info(f"配置文件加载成功: {self.config_path}")
                return True
            else:
                logger.info("配置文件不存在，使用默认配置")
                self.save_config()  # 保存默认配置
                return True
                
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return False
    
    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            config_data = self._to_dict()
            
            # 确保目录存在
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"配置文件保存成功: {self.config_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False
    
    def _to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "mult_fetch": asdict(self.mult_fetch),
            "context7": asdict(self.context7),
            "automation": asdict(self.automation),
            "job_analysis": asdict(self.job_analysis),
            "mcp_servers": {k: asdict(v) for k, v in self.mcp_servers.items()}
        }
    
    def _update_from_dict(self, config_data: Dict[str, Any]):
        """从字典更新配置"""
        try:
            if "mult_fetch" in config_data:
                self.mult_fetch = MultFetchConfig(**config_data["mult_fetch"])
            
            if "context7" in config_data:
                self.context7 = Context7Config(**config_data["context7"])
            
            if "automation" in config_data:
                self.automation = AutomationConfig(**config_data["automation"])
            
            if "job_analysis" in config_data:
                self.job_analysis = JobAnalysisConfig(**config_data["job_analysis"])
            
            if "mcp_servers" in config_data:
                for name, server_config in config_data["mcp_servers"].items():
                    self.mcp_servers[name] = MCPServerConfig(**server_config)
                    
        except Exception as e:
            logger.error(f"更新配置失败: {e}")
    
    def get_mult_fetch_params(self) -> Dict[str, Any]:
        """获取Mult-Fetch参数"""
        return self.mult_fetch.to_fetch_params()
    
    def get_context7_params(self) -> Dict[str, Any]:
        """获取Context7参数"""
        return {
            "tokens": self.context7.max_tokens
        }
    
    def is_server_enabled(self, server_name: str) -> bool:
        """检查服务器是否启用"""
        if server_name in self.mcp_servers:
            return self.mcp_servers[server_name].enabled
        return False
    
    def get_server_config(self, server_name: str) -> Optional[MCPServerConfig]:
        """获取服务器配置"""
        return self.mcp_servers.get(server_name)
    
    def update_server_config(self, server_name: str, **kwargs):
        """更新服务器配置"""
        if server_name in self.mcp_servers:
            server_config = self.mcp_servers[server_name]
            for key, value in kwargs.items():
                if hasattr(server_config, key):
                    setattr(server_config, key, value)
                else:
                    server_config.custom_settings[key] = value
    
    def enable_server(self, server_name: str):
        """启用服务器"""
        if server_name in self.mcp_servers:
            self.mcp_servers[server_name].enabled = True
    
    def disable_server(self, server_name: str):
        """禁用服务器"""
        if server_name in self.mcp_servers:
            self.mcp_servers[server_name].enabled = False
    
    def get_automation_config(self) -> AutomationConfig:
        """获取自动化配置"""
        return self.automation
    
    def get_job_analysis_config(self) -> JobAnalysisConfig:
        """获取职位分析配置"""
        return self.job_analysis
    
    def validate_config(self) -> List[str]:
        """验证配置"""
        errors = []
        
        # 验证自动化配置
        if self.automation.default_automation not in ["selenium", "playwright"]:
            errors.append("无效的默认自动化类型")
        
        if self.automation.implicit_wait <= 0:
            errors.append("隐式等待时间必须大于0")
        
        # 验证职位分析配置
        if not (0 <= self.job_analysis.compatibility_threshold <= 1):
            errors.append("兼容性阈值必须在0-1之间")
        
        if self.job_analysis.max_jobs_to_analyze <= 0:
            errors.append("最大分析职位数必须大于0")
        
        # 验证MCP服务器配置
        for name, server in self.mcp_servers.items():
            if server.timeout <= 0:
                errors.append(f"服务器{name}的超时时间必须大于0")
            
            if server.retry_count < 0:
                errors.append(f"服务器{name}的重试次数不能为负数")
        
        return errors
    
    def reset_to_defaults(self):
        """重置为默认配置"""
        self.mult_fetch = MultFetchConfig()
        self.context7 = Context7Config()
        self.automation = AutomationConfig()
        self.job_analysis = JobAnalysisConfig()
        self.mcp_servers = {
            "mult-fetch": MCPServerConfig("mult-fetch-mcp-server"),
            "context7": MCPServerConfig("context7-mcp")
        }
        logger.info("配置已重置为默认值")
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            "mult_fetch_enabled": self.mult_fetch.enabled,
            "context7_enabled": self.context7.enabled,
            "default_automation": self.automation.default_automation,
            "job_analysis_enabled": self.job_analysis.enabled,
            "enabled_servers": [name for name, server in self.mcp_servers.items() if server.enabled],
            "config_file": str(self.config_path)
        }

# 全局配置管理器实例
_config_manager = None

def get_config_manager(config_file: str = None) -> MCPConfigManager:
    """获取配置管理器实例（单例模式）"""
    global _config_manager
    if _config_manager is None or config_file:
        _config_manager = MCPConfigManager(config_file)
    return _config_manager

def load_mcp_config(config_file: str = None) -> MCPConfigManager:
    """加载MCP配置"""
    return get_config_manager(config_file)

# 便捷函数
def get_mult_fetch_config() -> MultFetchConfig:
    """获取Mult-Fetch配置"""
    return get_config_manager().mult_fetch

def get_context7_config() -> Context7Config:
    """获取Context7配置"""
    return get_config_manager().context7

def get_automation_config() -> AutomationConfig:
    """获取自动化配置"""
    return get_config_manager().automation

def get_job_analysis_config() -> JobAnalysisConfig:
    """获取职位分析配置"""
    return get_config_manager().job_analysis