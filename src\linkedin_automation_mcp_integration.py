#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LinkedIn自动化MCP集成模块
将MCP增强功能集成到现有的LinkedIn自动化系统中
保持与Selenium/Undetected ChromeDriver的完全兼容性
"""

import asyncio
import random
import time
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from loguru import logger

# 导入现有的自动化类
try:
    from .linkedin_automation import LinkedInAutomation as SeleniumAutomation
except ImportError:
    SeleniumAutomation = None
    logger.warning("无法导入Selenium自动化类")

try:
    from .linkedin_automation_playwright import LinkedInAutomationPlaywright as PlaywrightAutomation
except ImportError:
    PlaywrightAutomation = None
    logger.warning("无法导入Playwright自动化类")

# 导入MCP增强模块
try:
    from .mcp_enhanced_automation import MCPEnhancedAutomation, MCPConfig, create_enhanced_automation
except ImportError:
    logger.error("无法导入MCP增强模块")
    MCPEnhancedAutomation = None
    MCPConfig = None

class LinkedInAutomationWithMCP:
    """集成MCP功能的LinkedIn自动化类"""
    
    def __init__(self, automation_type: str = "selenium", config_path: str = None, enable_mcp: bool = True):
        """
        初始化集成MCP功能的LinkedIn自动化
        
        Args:
            automation_type: 自动化类型 ('selenium', 'playwright')
            config_path: 配置文件路径
            enable_mcp: 是否启用MCP功能
        """
        self.automation_type = automation_type
        self.enable_mcp = enable_mcp and (MCPEnhancedAutomation is not None)
        self.base_automation = None
        self.mcp_enhancement = None
        
        # 初始化基础自动化
        self._init_base_automation(config_path)
        
        # 初始化MCP增强
        if self.enable_mcp:
            self._init_mcp_enhancement()
        
        logger.info(f"LinkedIn自动化初始化完成 - 类型: {automation_type}, MCP: {self.enable_mcp}")
    
    def _init_base_automation(self, config_path: str):
        """初始化基础自动化实例"""
        try:
            if self.automation_type == "selenium" and SeleniumAutomation:
                self.base_automation = SeleniumAutomation(config_path)
                logger.info("Selenium自动化初始化成功")
            elif self.automation_type == "playwright" and PlaywrightAutomation:
                self.base_automation = PlaywrightAutomation(config_path)
                logger.info("Playwright自动化初始化成功")
            else:
                raise ValueError(f"不支持的自动化类型: {self.automation_type}")
        except Exception as e:
            logger.error(f"基础自动化初始化失败: {e}")
            raise
    
    def _init_mcp_enhancement(self):
        """初始化MCP增强功能"""
        try:
            if MCPEnhancedAutomation:
                self.mcp_enhancement = create_enhanced_automation(
                    base_automation=self.base_automation,
                    enable_mcp=True
                )
                logger.info("MCP增强功能初始化成功")
            else:
                logger.warning("MCP增强功能不可用")
                self.enable_mcp = False
        except Exception as e:
            logger.error(f"MCP增强功能初始化失败: {e}")
            self.enable_mcp = False
    
    def setup_driver(self, **kwargs):
        """设置浏览器驱动（保持原有接口）"""
        if not self.base_automation:
            raise RuntimeError("基础自动化未初始化")
        
        return self.base_automation.setup_driver(**kwargs)
    
    def login(self, email: str = None, password: str = None) -> bool:
        """登录LinkedIn（保持原有接口）"""
        if not self.base_automation:
            raise RuntimeError("基础自动化未初始化")
        
        try:
            result = self.base_automation.login(email, password)
            if result:
                logger.info("LinkedIn登录成功")
            return result
        except Exception as e:
            logger.error(f"LinkedIn登录失败: {e}")
            return False
    
    async def enhanced_search_jobs(self, search_criteria: Dict) -> List[Dict]:
        """增强的职位搜索功能"""
        if not self.base_automation:
            raise RuntimeError("基础自动化未初始化")
        
        try:
            # 使用基础自动化进行搜索
            if hasattr(self.base_automation, 'search_jobs'):
                jobs = self.base_automation.search_jobs(search_criteria)
            else:
                # 如果没有search_jobs方法，使用基础搜索逻辑
                jobs = await self._basic_job_search(search_criteria)
            
            # 如果启用了MCP，进行增强分析
            if self.enable_mcp and self.mcp_enhancement and jobs:
                enhanced_jobs = []
                for job in jobs[:10]:  # 限制分析数量以提高性能
                    try:
                        enhanced_job = await self._enhance_job_data(job)
                        enhanced_jobs.append(enhanced_job)
                    except Exception as e:
                        logger.warning(f"职位增强分析失败: {e}")
                        enhanced_jobs.append(job)  # 保留原始数据
                
                logger.info(f"完成{len(enhanced_jobs)}个职位的增强分析")
                return enhanced_jobs
            
            return jobs
            
        except Exception as e:
            logger.error(f"职位搜索失败: {e}")
            return []
    
    async def _basic_job_search(self, search_criteria: Dict) -> List[Dict]:
        """基础职位搜索逻辑"""
        # 这里实现基础的职位搜索逻辑
        # 可以调用现有的搜索方法或实现新的搜索逻辑
        logger.info(f"执行基础职位搜索: {search_criteria}")
        
        # 模拟搜索结果
        return [
            {
                "title": "Software Engineer",
                "company": "Tech Corp",
                "location": "San Francisco, CA",
                "url": "https://linkedin.com/jobs/123456",
                "description": "We are looking for a skilled software engineer..."
            }
        ]
    
    async def _enhance_job_data(self, job: Dict) -> Dict:
        """使用MCP增强职位数据"""
        if not self.mcp_enhancement:
            return job
        
        try:
            # 使用MCP进行增强分析
            analysis_result = await self.mcp_enhancement.enhanced_job_analysis(
                job_url=job.get("url", ""),
                job_data=job
            )
            
            # 将分析结果合并到职位数据中
            enhanced_job = job.copy()
            enhanced_job["mcp_analysis"] = analysis_result
            enhanced_job["enhanced"] = True
            
            # 添加兼容性评分
            if "compatibility_score" in analysis_result:
                enhanced_job["compatibility_score"] = analysis_result["compatibility_score"]
            
            return enhanced_job
            
        except Exception as e:
            logger.error(f"职位数据增强失败: {e}")
            return job
    
    def apply_to_job(self, job_url: str, **kwargs) -> bool:
        """申请职位（保持原有接口）"""
        if not self.base_automation:
            raise RuntimeError("基础自动化未初始化")
        
        try:
            if hasattr(self.base_automation, 'apply_to_job'):
                return self.base_automation.apply_to_job(job_url, **kwargs)
            else:
                logger.warning("基础自动化不支持apply_to_job方法")
                return False
        except Exception as e:
            logger.error(f"职位申请失败: {e}")
            return False
    
    async def intelligent_job_application_flow(self, search_criteria: Dict, max_applications: int = 10) -> Dict:
        """智能职位申请流程"""
        results = {
            "searched": 0,
            "analyzed": 0,
            "applied": 0,
            "successful": 0,
            "failed": 0,
            "jobs": []
        }
        
        try:
            # 1. 搜索职位
            jobs = await self.enhanced_search_jobs(search_criteria)
            results["searched"] = len(jobs)
            
            if not jobs:
                logger.warning("未找到符合条件的职位")
                return results
            
            # 2. 筛选高兼容性职位
            qualified_jobs = []
            for job in jobs:
                if self.enable_mcp and "compatibility_score" in job:
                    if job["compatibility_score"] >= 0.7:  # 兼容性阈值
                        qualified_jobs.append(job)
                        results["analyzed"] += 1
                else:
                    # 如果没有MCP分析，使用基础筛选
                    qualified_jobs.append(job)
            
            logger.info(f"筛选出{len(qualified_jobs)}个高兼容性职位")
            
            # 3. 批量申请
            applied_count = 0
            for job in qualified_jobs[:max_applications]:
                try:
                    if self.apply_to_job(job.get("url", "")):
                        results["successful"] += 1
                        job["application_status"] = "success"
                    else:
                        results["failed"] += 1
                        job["application_status"] = "failed"
                    
                    applied_count += 1
                    results["applied"] = applied_count
                    results["jobs"].append(job)
                    
                    # 添加延迟避免被检测
                    await asyncio.sleep(random.uniform(30, 60))
                    
                except Exception as e:
                    logger.error(f"申请职位失败 {job.get('url', '')}: {e}")
                    results["failed"] += 1
                    job["application_status"] = "error"
                    job["error"] = str(e)
                    results["jobs"].append(job)
            
            logger.info(f"申请流程完成 - 成功: {results['successful']}, 失败: {results['failed']}")
            return results
            
        except Exception as e:
            logger.error(f"智能申请流程失败: {e}")
            results["error"] = str(e)
            return results
    
    def get_status(self) -> Dict:
        """获取系统状态"""
        status = {
            "automation_type": self.automation_type,
            "mcp_enabled": self.enable_mcp,
            "base_automation_ready": self.base_automation is not None,
            "logged_in": False
        }
        
        if self.base_automation:
            status["logged_in"] = getattr(self.base_automation, 'is_logged_in', False)
        
        if self.mcp_enhancement:
            status["mcp_status"] = self.mcp_enhancement.get_enhancement_status()
        
        return status
    
    def close(self):
        """关闭自动化实例"""
        try:
            if self.base_automation and hasattr(self.base_automation, 'close'):
                self.base_automation.close()
            elif self.base_automation and hasattr(self.base_automation, 'driver'):
                if self.base_automation.driver:
                    self.base_automation.driver.quit()
            
            logger.info("LinkedIn自动化实例已关闭")
        except Exception as e:
            logger.error(f"关闭自动化实例失败: {e}")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

# 工厂函数
def create_linkedin_automation(automation_type: str = "selenium", config_path: str = None, enable_mcp: bool = True) -> LinkedInAutomationWithMCP:
    """创建LinkedIn自动化实例"""
    return LinkedInAutomationWithMCP(automation_type, config_path, enable_mcp)

# 异步上下文管理器
class AsyncLinkedInAutomationWithMCP:
    """异步LinkedIn自动化上下文管理器"""
    
    def __init__(self, automation_type: str = "selenium", config_path: str = None, enable_mcp: bool = True):
        self.automation = LinkedInAutomationWithMCP(automation_type, config_path, enable_mcp)
    
    async def __aenter__(self):
        return self.automation
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        self.automation.close()

# 兼容性函数，保持与现有代码的兼容性
def get_enhanced_linkedin_automation(config_path: str = None) -> LinkedInAutomationWithMCP:
    """获取增强的LinkedIn自动化实例（向后兼容）"""
    return create_linkedin_automation("selenium", config_path, True)