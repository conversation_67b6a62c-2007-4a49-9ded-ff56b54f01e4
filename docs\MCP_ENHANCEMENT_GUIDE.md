# MCP增强功能使用指南

本指南详细介绍了Jobs Applier AI Agent的MCP（Model Context Protocol）增强功能，包括安装、配置和使用方法。

## 目录

- [概述](#概述)
- [功能特性](#功能特性)
- [安装和配置](#安装和配置)
- [使用方法](#使用方法)
- [配置说明](#配置说明)
- [API参考](#api参考)
- [故障排除](#故障排除)
- [最佳实践](#最佳实践)

## 概述

MCP增强功能为Jobs Applier AI Agent添加了强大的外部数据获取和分析能力，通过集成多个MCP服务器，实现了：

- **智能网页内容获取**：自动获取和解析职位页面内容
- **技术文档查询**：实时获取最新的技术库文档
- **增强职位分析**：深度分析职位要求和匹配度
- **智能申请流程**：基于分析结果的自动化申请

## 功能特性

### 🌐 网页内容获取
- 支持多种内容格式（HTML、文本、Markdown）
- 智能内容提取和清理
- 自动处理大型页面的分块加载
- 支持浏览器模式处理动态内容

### 📚 技术文档查询
- 实时获取最新技术库文档
- 智能库名解析和匹配
- 支持特定主题的文档查询
- 文档缓存优化

### 🔍 增强职位分析
- **技术栈分析**：自动识别职位要求的技术栈
- **公司信息分析**：获取公司背景和文化信息
- **薪资分析**：市场薪资对比和竞争力评估
- **技能匹配**：与用户技能的详细匹配分析
- **兼容性评分**：综合评分系统

### 🤖 智能自动化
- LinkedIn搜索结果的自动分析
- 基于兼容性评分的智能筛选
- 自动化申请流程
- 个性化简历和求职信生成（可选）

## 安装和配置

### 1. 环境要求

```bash
# Python 3.8+
python --version

# 安装依赖
pip install -r requirements.txt
```

### 2. MCP服务器配置

确保以下MCP服务器已正确配置：

- **mult-fetch-mcp-server**：网页内容获取
- **context7-mcp**：技术文档查询
- **memory**：知识图谱（可选）
- **GitHub**：GitHub集成（可选）

### 3. 配置文件设置

#### 主配置文件：`config/mcp_config.yaml`

```yaml
# 启用MCP功能
mcp_servers:
  mult-fetch:
    enabled: true
    timeout: 30
  context7:
    enabled: true
    timeout: 45

# 职位分析配置
job_analysis:
  enabled: true
  tech_stack:
    enabled: true
    confidence_threshold: 0.7
```

#### 用户技能配置：`config/user_skills.yaml`

```yaml
# 个人信息
user_profile:
  name: "Your Name"
  experience_years: 5
  location: "San Francisco, CA"

# 技术技能
technical_skills:
  programming_languages:
    - name: "Python"
      proficiency: "expert"
      years_experience: 5
```

### 4. 验证安装

```bash
# 运行示例脚本验证安装
python examples/mcp_enhanced_example.py
```

## 使用方法

### 基础使用

#### 1. 导入模块

```python
from src.linkedin_automation_mcp_integration import create_linkedin_automation
from src.job_analysis_enhanced import analyze_job_posting
from src.mcp_tools_integration import fetch_webpage_content
```

#### 2. 创建自动化实例

```python
# 创建带MCP增强的LinkedIn自动化实例
automation = create_linkedin_automation(
    automation_type="selenium",
    enable_mcp=True
)
```

#### 3. 执行增强搜索

```python
# 搜索条件
search_criteria = {
    "keywords": "python developer",
    "location": "San Francisco",
    "experience_level": "mid-level"
}

# 执行增强搜索（包含自动分析）
jobs = await automation.enhanced_search_jobs(search_criteria)

# 查看结果
for job in jobs:
    print(f"职位: {job['title']}")
    print(f"公司: {job['company']}")
    print(f"兼容性评分: {job.get('compatibility_score', 'N/A')}")
```

### 高级使用

#### 1. 单独职位分析

```python
# 分析特定职位
job_url = "https://linkedin.com/jobs/view/*********"
job_data = {
    "title": "Senior Python Developer",
    "company": "Tech Corp",
    "description": "Job description here..."
}

analysis = await analyze_job_posting(job_url, job_data)

# 查看分析结果
print(f"技术栈: {analysis.tech_stack}")
print(f"技能匹配: {analysis.skills_matching}")
print(f"兼容性评分: {analysis.compatibility_score.overall_score}")
```

#### 2. 批量职位分析

```python
from src.job_analysis_enhanced import batch_analyze_jobs

# 批量分析多个职位
jobs_list = [
    {"url": "https://linkedin.com/jobs/view/1", "title": "Job 1"},
    {"url": "https://linkedin.com/jobs/view/2", "title": "Job 2"},
    # ...
]

analyses = await batch_analyze_jobs(jobs_list, max_concurrent=3)

# 按兼容性评分排序
sorted_analyses = sorted(
    analyses, 
    key=lambda x: x.compatibility_score.overall_score, 
    reverse=True
)
```

#### 3. 智能申请流程

```python
# 执行完整的智能申请流程
results = await automation.intelligent_job_application_flow(
    search_criteria=search_criteria,
    max_applications=5,
    min_compatibility_score=0.7
)

print(f"申请结果: {results}")
```

### 工具函数使用

#### 1. 网页内容获取

```python
from src.mcp_tools_integration import fetch_webpage_content

# 获取网页内容
result = await fetch_webpage_content(
    url="https://example.com",
    format_type="plaintext",
    extract_content=True
)

if result.success:
    content = result.data['content']
    print(f"获取内容长度: {len(content)}")
```

#### 2. 技术文档查询

```python
from src.mcp_tools_integration import get_library_documentation

# 获取React文档
doc_result = await get_library_documentation(
    library_name="react",
    topic="hooks",
    max_tokens=5000
)

if doc_result.success:
    documentation = doc_result.data['documentation']
    print(f"文档内容: {documentation[:500]}...")
```

## 配置说明

### MCP服务器配置

#### Mult-Fetch配置

```yaml
mcp_servers:
  mult-fetch:
    enabled: true
    timeout: 30  # 请求超时时间
    max_retries: 3  # 最大重试次数
    default_params:
      contentSizeLimit: 50000  # 内容大小限制
      enableContentSplitting: true  # 启用内容分割
      extractContent: false  # 提取主要内容
```

#### Context7配置

```yaml
mcp_servers:
  context7:
    enabled: true
    timeout: 45
    default_params:
      tokens: 10000  # 默认文档令牌数
    cache:
      enabled: true
      ttl: 3600  # 缓存时间
```

### 职位分析配置

```yaml
job_analysis:
  # 兼容性评分权重
  compatibility_scoring:
    weights:
      skills_match: 0.4
      experience_level: 0.2
      location_preference: 0.15
      salary_expectation: 0.15
      company_culture: 0.1
    minimum_score: 0.6
```

### 用户技能配置

```yaml
# 技能定义
technical_skills:
  programming_languages:
    - name: "Python"
      proficiency: "expert"  # beginner, intermediate, advanced, expert
      years_experience: 5
      last_used: "2024"
```

## API参考

### 主要类和方法

#### LinkedInAutomationWithMCP

```python
class LinkedInAutomationWithMCP:
    async def enhanced_search_jobs(self, criteria: dict) -> List[dict]
    async def intelligent_job_application_flow(self, **kwargs) -> dict
    def get_status(self) -> dict
    def close(self) -> None
```

#### JobAnalysisEngine

```python
class JobAnalysisEngine:
    async def analyze_job(self, job_url: str, job_data: dict) -> JobAnalysis
    async def analyze_tech_stack(self, description: str) -> TechStackAnalysis
    async def analyze_company_info(self, company_name: str) -> CompanyInfo
    async def calculate_compatibility_score(self, analysis: JobAnalysis) -> CompatibilityScore
```

#### MCP工具函数

```python
# 网页内容获取
async def fetch_webpage_content(
    url: str, 
    format_type: str = "plaintext",
    **kwargs
) -> MCPResult

# 技术文档查询
async def get_library_documentation(
    library_name: str,
    topic: str = None,
    max_tokens: int = 10000
) -> MCPResult
```

### 数据结构

#### JobAnalysis

```python
@dataclass
class JobAnalysis:
    job_title: str
    company_name: str
    location: str
    tech_stack: TechStackAnalysis
    company_info: CompanyInfo
    salary_info: SalaryAnalysis
    skills_matching: SkillsMatching
    compatibility_score: CompatibilityScore
    analysis_timestamp: datetime
    mcp_enhanced: bool
```

#### CompatibilityScore

```python
@dataclass
class CompatibilityScore:
    overall_score: float  # 0.0 - 1.0
    skills_match_score: float
    experience_match_score: float
    location_match_score: float
    salary_match_score: float
    culture_match_score: float
    recommendation: str
```

## 故障排除

### 常见问题

#### 1. MCP服务器连接失败

**问题**：`MCPServerError: Failed to connect to server`

**解决方案**：
```bash
# 检查MCP服务器状态
python -c "from src.mcp_config_manager import get_config_manager; print(get_config_manager().validate_config())"

# 重启MCP服务器
# 检查网络连接
```

#### 2. 职位分析超时

**问题**：分析过程超时

**解决方案**：
```yaml
# 增加超时时间
mcp_servers:
  mult-fetch:
    timeout: 60  # 增加到60秒
  context7:
    timeout: 90  # 增加到90秒
```

#### 3. 内存使用过高

**问题**：处理大量职位时内存不足

**解决方案**：
```yaml
# 限制并发数
performance:
  concurrency:
    max_concurrent_job_analysis: 2  # 减少并发数
    
# 启用内容压缩
performance:
  optimization:
    enable_content_compression: true
```

### 调试模式

```yaml
# 启用调试模式
debug:
  enabled: true
  verbose_logging: true
  save_debug_data: true
```

```python
# 代码中启用详细日志
import logging
logging.getLogger('mcp_integration').setLevel(logging.DEBUG)
```

## 最佳实践

### 1. 性能优化

- **批量处理**：使用`batch_analyze_jobs`而不是循环调用单个分析
- **缓存利用**：启用缓存减少重复请求
- **并发控制**：根据系统资源调整并发数

```python
# 好的做法
analyses = await batch_analyze_jobs(jobs, max_concurrent=3)

# 避免的做法
analyses = []
for job in jobs:
    analysis = await analyze_job_posting(job['url'], job)
    analyses.append(analysis)
```

### 2. 错误处理

```python
try:
    analysis = await analyze_job_posting(job_url, job_data)
except MCPServerError as e:
    logger.error(f"MCP服务器错误: {e}")
    # 使用基础分析作为后备
    analysis = basic_job_analysis(job_data)
except Exception as e:
    logger.error(f"分析失败: {e}")
    # 处理其他错误
```

### 3. 配置管理

- 使用环境变量管理敏感配置
- 定期备份配置文件
- 使用版本控制跟踪配置变更

```python
# 环境变量配置
import os

config = {
    'api_key': os.getenv('MCP_API_KEY'),
    'timeout': int(os.getenv('MCP_TIMEOUT', '30'))
}
```

### 4. 监控和日志

```python
# 性能监控
from src.mcp_tools_integration import get_tools_manager

tools_manager = get_tools_manager()
stats = tools_manager.get_call_statistics()
logger.info(f"MCP调用统计: {stats}")
```

### 5. 安全考虑

- 不要在日志中记录敏感信息
- 使用HTTPS连接
- 定期更新依赖包
- 限制API调用频率

```yaml
# 安全配置
security:
  privacy:
    anonymize_logs: true
  access_control:
    rate_limiting: true
    requests_per_minute: 60
```

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 基础MCP集成功能
- 职位分析引擎
- LinkedIn自动化增强

### v1.1.0 (计划中)
- AI增强分析功能
- 预测性匹配算法
- 更多MCP服务器支持
- 性能优化

## 贡献指南

欢迎贡献代码和建议！请参考 [CONTRIBUTING.md](../CONTRIBUTING.md) 了解详细信息。

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](../LICENSE) 文件。

## 支持

如有问题或建议，请：

1. 查看本文档的故障排除部分
2. 搜索现有的 GitHub Issues
3. 创建新的 Issue 描述问题
4. 联系维护团队

---

**注意**：本功能仍在积极开发中，API可能会有变化。建议在生产环境使用前进行充分测试。