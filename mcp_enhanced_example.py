#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP增强功能示例脚本
展示如何使用新的MCP增强功能进行LinkedIn自动化
"""

import asyncio
import sys
import os
from pathlib import Path
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.linkedin_automation_mcp_integration import LinkedInAutomationWithMCP, create_linkedin_automation
    from src.mcp_config_manager import get_config_manager, load_mcp_config
    from src.mcp_tools_integration import get_tools_manager, fetch_webpage_content, get_library_documentation
    from src.job_analysis_enhanced import analyze_job_posting, batch_analyze_jobs, export_analysis_to_json
except ImportError as e:
    logger.error(f"导入模块失败: {e}")
    logger.error("请确保所有必需的模块都已正确安装")
    sys.exit(1)

async def example_basic_mcp_tools():
    """基础MCP工具使用示例"""
    logger.info("=== 基础MCP工具使用示例 ===")
    
    # 1. 获取网页内容
    logger.info("1. 获取网页内容示例")
    result = await fetch_webpage_content("https://example.com", "plaintext")
    if result.success:
        logger.info(f"成功获取内容，长度: {len(result.data.get('content', ''))}")
        logger.info(f"执行时间: {result.execution_time:.2f}秒")
    else:
        logger.error(f"获取内容失败: {result.error}")
    
    # 2. 获取库文档
    logger.info("\n2. 获取库文档示例")
    doc_result = await get_library_documentation("react", "hooks", 5000)
    if doc_result.success:
        logger.info(f"成功获取React文档")
        logger.info(f"文档长度: {len(doc_result.data.get('documentation', ''))}")
    else:
        logger.error(f"获取文档失败: {doc_result.error}")
    
    # 3. 工具管理器统计
    tools_manager = get_tools_manager()
    stats = tools_manager.get_call_statistics()
    logger.info(f"\n3. 工具调用统计: {stats}")

async def example_job_analysis():
    """职位分析示例"""
    logger.info("\n=== 职位分析示例 ===")
    
    # 模拟职位数据
    sample_job = {
        "title": "Senior Python Developer",
        "company": "Tech Innovations Inc.",
        "location": "San Francisco, CA",
        "url": "https://linkedin.com/jobs/sample-123456",
        "description": """
        We are seeking a Senior Python Developer to join our growing team.
        
        Requirements:
        - 5+ years of Python development experience
        - Strong knowledge of Django and FastAPI frameworks
        - Experience with PostgreSQL and Redis
        - Familiarity with Docker and Kubernetes
        - AWS cloud platform experience
        - Agile/Scrum methodology experience
        
        Preferred:
        - React.js frontend experience
        - CI/CD pipeline setup
        - Microservices architecture
        
        We offer:
        - Competitive salary $120,000 - $150,000
        - Health insurance, dental, vision
        - 401k with company match
        - Flexible work arrangements
        - Professional development budget
        
        Join our innovative team working on cutting-edge fintech solutions!
        """
    }
    
    try:
        # 分析单个职位
        logger.info("分析职位中...")
        analysis = await analyze_job_posting(sample_job["url"], sample_job)
        
        # 显示分析结果
        logger.info(f"\n职位分析结果:")
        logger.info(f"职位标题: {analysis.job_title}")
        logger.info(f"公司: {analysis.company_name}")
        logger.info(f"兼容性评分: {analysis.compatibility_score.overall_score:.2f}")
        
        logger.info(f"\n技术栈分析:")
        logger.info(f"编程语言: {analysis.tech_stack.languages}")
        logger.info(f"框架: {analysis.tech_stack.frameworks}")
        logger.info(f"数据库: {analysis.tech_stack.databases}")
        logger.info(f"工具: {analysis.tech_stack.tools}")
        
        logger.info(f"\n技能匹配:")
        logger.info(f"匹配技能: {analysis.skills_matching.matched_skills}")
        logger.info(f"缺失技能: {analysis.skills_matching.missing_skills}")
        logger.info(f"技能匹配度: {analysis.skills_matching.skill_gap_score:.2f}")
        
        logger.info(f"\n薪资分析:")
        logger.info(f"薪资范围: ${analysis.salary_info.salary_range[0]:,} - ${analysis.salary_info.salary_range[1]:,}")
        logger.info(f"市场竞争力: {analysis.salary_info.market_competitiveness:.2f}")
        
        # 导出分析结果
        export_path = export_analysis_to_json(analysis)
        logger.info(f"\n分析结果已导出到: {export_path}")
        
    except Exception as e:
        logger.error(f"职位分析失败: {e}")

async def example_batch_job_analysis():
    """批量职位分析示例"""
    logger.info("\n=== 批量职位分析示例 ===")
    
    # 模拟多个职位
    sample_jobs = [
        {
            "title": "Frontend Developer",
            "company": "StartupCorp",
            "url": "https://linkedin.com/jobs/frontend-123",
            "description": "React, TypeScript, Node.js experience required. Startup environment."
        },
        {
            "title": "DevOps Engineer",
            "company": "CloudTech",
            "url": "https://linkedin.com/jobs/devops-456",
            "description": "AWS, Docker, Kubernetes, Jenkins. Enterprise environment."
        },
        {
            "title": "Data Scientist",
            "company": "DataCorp",
            "url": "https://linkedin.com/jobs/data-789",
            "description": "Python, machine learning, SQL, big data analytics."
        }
    ]
    
    try:
        logger.info(f"开始批量分析{len(sample_jobs)}个职位...")
        analyses = await batch_analyze_jobs(sample_jobs, max_concurrent=2)
        
        logger.info(f"\n批量分析完成，成功分析{len(analyses)}个职位:")
        for analysis in analyses:
            logger.info(f"- {analysis.job_title}: 兼容性评分 {analysis.compatibility_score.overall_score:.2f}")
        
        # 按兼容性评分排序
        sorted_analyses = sorted(analyses, key=lambda x: x.compatibility_score.overall_score, reverse=True)
        logger.info(f"\n推荐职位排序:")
        for i, analysis in enumerate(sorted_analyses, 1):
            logger.info(f"{i}. {analysis.job_title} ({analysis.company_name}) - 评分: {analysis.compatibility_score.overall_score:.2f}")
        
    except Exception as e:
        logger.error(f"批量分析失败: {e}")

async def example_linkedin_automation_integration():
    """LinkedIn自动化集成示例"""
    logger.info("\n=== LinkedIn自动化集成示例 ===")
    
    try:
        # 创建增强的LinkedIn自动化实例
        automation = create_linkedin_automation(
            automation_type="selenium",
            enable_mcp=True
        )
        
        # 获取系统状态
        status = automation.get_status()
        logger.info(f"自动化状态: {status}")
        
        # 模拟搜索条件
        search_criteria = {
            "keywords": "python developer",
            "location": "San Francisco",
            "experience_level": "mid-level"
        }
        
        # 执行增强的职位搜索
        logger.info("执行增强职位搜索...")
        jobs = await automation.enhanced_search_jobs(search_criteria)
        logger.info(f"找到{len(jobs)}个职位")
        
        # 显示前几个职位的信息
        for i, job in enumerate(jobs[:3], 1):
            logger.info(f"\n职位 {i}:")
            logger.info(f"标题: {job.get('title', 'N/A')}")
            logger.info(f"公司: {job.get('company', 'N/A')}")
            if 'compatibility_score' in job:
                logger.info(f"兼容性评分: {job['compatibility_score']:.2f}")
            if 'mcp_analysis' in job:
                logger.info(f"MCP增强分析: 已完成")
        
        # 关闭自动化实例
        automation.close()
        
    except Exception as e:
        logger.error(f"LinkedIn自动化集成示例失败: {e}")

def example_config_management():
    """配置管理示例"""
    logger.info("\n=== 配置管理示例 ===")
    
    try:
        # 加载配置管理器
        config_manager = load_mcp_config()
        
        # 显示配置摘要
        summary = config_manager.get_config_summary()
        logger.info(f"配置摘要: {summary}")
        
        # 验证配置
        errors = config_manager.validate_config()
        if errors:
            logger.warning(f"配置验证错误: {errors}")
        else:
            logger.info("配置验证通过")
        
        # 获取特定配置
        mult_fetch_config = config_manager.get_mult_fetch_params()
        logger.info(f"Mult-Fetch配置: {mult_fetch_config}")
        
        context7_config = config_manager.get_context7_params()
        logger.info(f"Context7配置: {context7_config}")
        
        # 更新服务器配置
        config_manager.update_server_config("mult-fetch", timeout=60)
        logger.info("已更新mult-fetch服务器超时时间")
        
        # 保存配置
        if config_manager.save_config():
            logger.info("配置已保存")
        
    except Exception as e:
        logger.error(f"配置管理示例失败: {e}")

async def example_intelligent_application_flow():
    """智能申请流程示例"""
    logger.info("\n=== 智能申请流程示例 ===")
    
    try:
        # 创建自动化实例
        automation = create_linkedin_automation(enable_mcp=True)
        
        # 搜索条件
        search_criteria = {
            "keywords": "senior python developer",
            "location": "remote",
            "salary_min": 100000
        }
        
        # 执行智能申请流程
        logger.info("开始智能申请流程...")
        results = await automation.intelligent_job_application_flow(
            search_criteria=search_criteria,
            max_applications=5
        )
        
        # 显示结果
        logger.info(f"\n申请流程结果:")
        logger.info(f"搜索到职位: {results['searched']}")
        logger.info(f"分析职位: {results['analyzed']}")
        logger.info(f"申请职位: {results['applied']}")
        logger.info(f"成功申请: {results['successful']}")
        logger.info(f"失败申请: {results['failed']}")
        
        if results['jobs']:
            logger.info(f"\n申请详情:")
            for job in results['jobs']:
                status = job.get('application_status', 'unknown')
                logger.info(f"- {job.get('title', 'N/A')} ({job.get('company', 'N/A')}): {status}")
        
        automation.close()
        
    except Exception as e:
        logger.error(f"智能申请流程示例失败: {e}")

async def main():
    """主函数"""
    logger.info("开始MCP增强功能示例演示")
    
    try:
        # 1. 配置管理示例
        example_config_management()
        
        # 2. 基础MCP工具示例
        await example_basic_mcp_tools()
        
        # 3. 职位分析示例
        await example_job_analysis()
        
        # 4. 批量职位分析示例
        await example_batch_job_analysis()
        
        # 5. LinkedIn自动化集成示例
        await example_linkedin_automation_integration()
        
        # 6. 智能申请流程示例（注释掉，因为需要实际的浏览器环境）
        # await example_intelligent_application_flow()
        
        logger.info("\n=== 所有示例演示完成 ===")
        
    except Exception as e:
        logger.error(f"示例演示失败: {e}")
        raise

if __name__ == "__main__":
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # 运行示例
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)