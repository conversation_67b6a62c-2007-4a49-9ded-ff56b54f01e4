import React, { useState, useEffect } from 'react';
import { useLinkedIn } from './LinkedInContext';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  Alert,
  LinearProgress,
  Chip,
  Grid,
  List,
  ListItem,
  ListItemText,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  Container,
  IconButton,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Checkbox,
  Link,
  Pagination,
  CircularProgress
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Search as SearchIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Settings as SettingsIcon,
  Info as InfoIcon,

  Logout as LogoutIcon,
  Close as CloseIcon
} from '@mui/icons-material';

const LinkedInAutomation = () => {
  // 使用全局LinkedIn状态
  const {
    searchResults,
    selectedJobs,
    searchForm,
    currentPage,
    itemsPerPage,
    automationStatus,
    totalPages,
    startIndex,
    endIndex,
    currentPageJobs,
    isAllSelected,
    isIndeterminate,
    updateSearchResults,
    updateSelectedJobs,
    updateSearchForm,
    updateCurrentPage,
    updateItemsPerPage,
    setAutomationStatus
  } = useLinkedIn();

  // 本地状态（不需要跨页面保持的状态）
  const [loginForm, setLoginForm] = useState({
    email: '',
    password: '',
    headless: false,
    automation_type: 'selenium' // 默认selenium，根据memories更稳定
  });

  const [batchApplyForm, setBatchApplyForm] = useState({
    max_applications: '',
    keywords: [],
    location: ''
  });

  // 申请状态跟踪
  const [applyingJobs, setApplyingJobs] = useState(new Set()); // 正在申请的职位ID
  const [appliedJobs, setAppliedJobs] = useState(new Set()); // 已申请的职位ID

  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false); // 专门用于搜索状态
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showLoginDialog, setShowLoginDialog] = useState(false);


  // API基础URL
  const API_BASE = 'http://localhost:8003/api/linkedin';

  // 获取自动化状态
  const fetchStatus = async () => {
    try {
      const response = await fetch(`${API_BASE}/status`);
      if (response.ok) {
        const data = await response.json();
        setAutomationStatus(data);

        // 检查后端状态，如果后端重启了（找到职位为0且没有登录），清空前端缓存的搜索结果
        if (data.progress && data.progress.total_found === 0 && !data.is_logged_in && searchResults.length > 0) {
          console.log('检测到后端重启，清空前端缓存的搜索结果');
          updateSearchResults([]);
        }

        // 额外检查：如果后端没有progress数据但前端有搜索结果，也清空缓存
        if (!data.progress && searchResults.length > 0) {
          console.log('后端无progress数据，清空前端缓存');
          updateSearchResults([]);
        }
      }
    } catch (err) {
      console.error('获取状态失败:', err);
    }
  };
  


  // 定期更新状态
  useEffect(() => {
    fetchStatus();

    const statusInterval = setInterval(fetchStatus, 2000); // 每2秒更新一次状态

    return () => {
      clearInterval(statusInterval);
    };
  }, []);

  // 监听登录状态变化，自动关闭登录对话框
  useEffect(() => {
    console.log('状态监听 - 登录状态:', automationStatus.is_logged_in, '对话框显示:', showLoginDialog);
    if (automationStatus.is_logged_in && showLoginDialog) {
      console.log('🎉 检测到登录成功，自动关闭登录对话框');
      setShowLoginDialog(false);
      setSuccess('LinkedIn登录成功！验证已完成');
      setError(''); // 清除任何错误信息
      setLoading(false); // 确保加载状态被清除
    }
  }, [automationStatus.is_logged_in, showLoginDialog]);



  // 设置自动化
  const setupAutomation = async () => {
    console.log('🔧 setupAutomation 开始...');
    setError('');
    try {
      console.log('📡 发送setup请求:', {
        headless: loginForm.headless,
        automation_type: loginForm.automation_type
      });

      const response = await fetch(`${API_BASE}/setup`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          headless: loginForm.headless,
          automation_type: loginForm.automation_type
        })
      });

      console.log('📡 setup响应状态:', response.status);

      if (response.ok) {
        const result = await response.json();
        console.log('📋 setup响应结果:', result);
        if (result.success) {
          setSuccess(result.message);
          fetchStatus();
          console.log('✅ setupAutomation 成功完成');
        } else {
          console.error('❌ setup返回失败:', result.message);
          setError(result.message || '设置自动化失败');
          throw new Error(result.message || '设置自动化失败');
        }
      } else {
        const errorData = await response.json();
        console.error('❌ setup HTTP错误:', response.status, errorData);
        setError(errorData.detail || '设置自动化失败');
        throw new Error(errorData.detail || '设置自动化失败');
      }
    } catch (err) {
      console.error('❌ setupAutomation 异常:', err);
      setError('设置自动化失败: ' + err.message);
      throw err; // 重新抛出错误，让调用者知道setup失败了
    }
  };

  // 检查登录状态
  const checkLoginStatus = async () => {
    try {
      console.log('🔍 检查当前登录状态...');
      const response = await fetch(`${API_BASE}/verify-login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        const result = await response.json();
        console.log('📋 登录状态检查结果:', result);

        if (result.success) {
          console.log('🎉 检测到已登录LinkedIn！');
          setSuccess('检测到您已登录LinkedIn，无需重新登录！');
          setShowLoginDialog(false);
          fetchStatus();
          return true; // 已登录
        } else {
          console.log('ℹ️ 当前未登录，需要执行登录流程');
          return false; // 未登录
        }
      } else {
        console.error('❌ 登录状态检查失败:', response.status);
        return false;
      }
    } catch (err) {
      console.error('❌ 检查登录状态异常:', err);
      return false;
    }
  };

  // 登录LinkedIn
  const loginLinkedIn = async () => {
    setLoading(true);
    setError('');

    try {
      console.log('🔧 开始设置自动化...');
      // 先设置自动化
      await setupAutomation();
      console.log('✅ 自动化设置完成');

      // 检查是否已经登录
      const isAlreadyLoggedIn = await checkLoginStatus();
      if (isAlreadyLoggedIn) {
        console.log('✅ 已登录，跳过登录流程');
        setLoading(false);
        return;
      }

      // 如果未登录且没有提供用户名密码，提示用户输入
      if (!loginForm.email || !loginForm.password) {
        setError('检测到未登录，请输入邮箱和密码进行登录');
        setLoading(false);
        return;
      }

      console.log('🔑 开始执行登录流程...');
      const response = await fetch(`${API_BASE}/login`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(loginForm)
      });

      console.log('📡 登录API响应状态:', response.status);
      const result = await response.json();
      console.log('📋 登录API响应结果:', result);

      if (result.success) {
        setSuccess('LinkedIn登录成功');
        setShowLoginDialog(false);
        fetchStatus();
      } else {
        if (result.requires_action) {
          // 需要用户操作（验证码或二次验证）
          setAutomationStatus(prev => ({
            ...prev,
            current_task: result.status
          }));
          setSuccess('请在浏览器窗口中完成验证，系统将自动检测登录状态');
          setError(''); // 清除错误信息，因为这不是错误
          // 不关闭对话框，让后台监控来处理
        } else {
          setError(result.status || '登录失败');
        }
      }
    } catch (err) {
      console.error('❌ 登录过程出错:', err);
      setError('登录失败: ' + err.message);
    }
    setLoading(false);
  };

  // 搜索职位
  const searchJobs = async () => {
    if (!automationStatus.is_logged_in) {
      setError('请先登录LinkedIn');
      return;
    }

    setSearchLoading(true);
    setError('');
    try {
      const response = await fetch(`${API_BASE}/search`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(searchForm)
      });

      if (response.ok) {
        const jobs = await response.json();
        updateSearchResults(jobs);
        setSuccess(`找到 ${jobs.length} 个职位`);
      } else {
        const errorData = await response.json();
        setError(errorData.detail || '搜索失败');
      }
    } catch (err) {
      setError('搜索失败: ' + err.message);
    }
    setSearchLoading(false);
  };

  // 申请单个职位
  const applySingleJob = async (job) => {
    const jobId = job.job_id;

    // 设置申请中状态
    setApplyingJobs(prev => new Set([...prev, jobId]));
    setError('');

    try {
      const response = await fetch(`${API_BASE}/apply/${jobId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(job)
      });

      const result = await response.json();
      if (result.success) {
        setSuccess(result.message);
        // 标记为已申请
        setAppliedJobs(prev => new Set([...prev, jobId]));
        fetchStatus();
      } else {
        setError(result.message);
      }
    } catch (err) {
      setError('申请失败: ' + err.message);
    } finally {
      // 移除申请中状态
      setApplyingJobs(prev => {
        const newSet = new Set(prev);
        newSet.delete(jobId);
        return newSet;
      });
    }
  };

  // 批量申请职位
  const startBatchApply = async () => {
    if (!automationStatus.is_logged_in) {
      setError('请先登录LinkedIn');
      return;
    }

    setLoading(true);
    setError('');
    try {
      const response = await fetch(`${API_BASE}/batch-apply`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(batchApplyForm)
      });
      
      if (response.ok) {
        setSuccess('批量申请已开始');
        fetchStatus();
      } else {
        const errorData = await response.json();
        setError(errorData.detail || '批量申请失败');
      }
    } catch (err) {
      setError('批量申请失败: ' + err.message);
    }
    setLoading(false);
  };

  // 停止批量申请
  const stopBatchApply = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await fetch(`${API_BASE}/stop`, {
        method: 'POST'
      });
      
      if (response.ok) {
        setSuccess('批量申请已停止');
        fetchStatus();
      } else {
        const errorData = await response.json();
        setError(errorData.detail || '停止批量申请失败');
      }
    } catch (err) {
      setError('停止批量申请失败: ' + err.message);
    }
    setLoading(false);
  };

  // 手动验证登录状态
  const verifyLoginStatus = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await fetch(`${API_BASE}/verify-login`, { method: 'POST' });
      const result = await response.json();
      if (result.success) {
        setSuccess('登录状态验证成功');
        fetchStatus();
      } else {
        setError(result.message || '登录状态验证失败');
      }
    } catch (err) {
      setError('验证失败: ' + err.message);
    }
    setLoading(false);
  };

  // 登出LinkedIn
  const logoutLinkedIn = async () => {
    setLoading(true);
    setError('');
    try {
      console.log('正在尝试登出，API URL:', `${API_BASE}/logout`);

      // 首先检查后端服务是否可用
      const healthResponse = await fetch('http://localhost:8003/api/health');
      if (!healthResponse.ok) {
        throw new Error('后端服务不可用，请确保后端服务正在运行');
      }

      const response = await fetch(`${API_BASE}/logout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      if (result.success) {
        setSuccess('已成功登出LinkedIn');
        fetchStatus();
      } else {
        setError(result.message || '登出失败');
      }
    } catch (err) {
      console.error('登出错误详情:', err);
      if (err.message.includes('Failed to fetch') || err.message.includes('NetworkError')) {
        setError('网络连接失败，请检查后端服务是否正在运行 (http://localhost:8003)');
      } else {
        setError('登出失败: ' + err.message);
      }
    }
    setLoading(false);
  };

  // 选择框相关功能
  const handleSelectJob = (jobId, checked) => {
    const newSelected = new Set(selectedJobs);
    if (checked) {
      newSelected.add(jobId);
    } else {
      newSelected.delete(jobId);
    }
    updateSelectedJobs(newSelected);
  };

  const handleSelectAll = (checked) => {
    const newSelected = new Set(selectedJobs);
    if (checked) {
      // 选中当前页面的所有职位
      currentPageJobs.forEach(job => newSelected.add(job.job_id));
    } else {
      // 取消选中当前页面的所有职位
      currentPageJobs.forEach(job => newSelected.delete(job.job_id));
    }
    updateSelectedJobs(newSelected);
  };

  // 分页计算和选择逻辑现在来自全局状态

  // 批量申请选中的职位
  const applySelectedJobs = async () => {
    if (selectedJobs.size === 0) {
      setError('请先选择要申请的职位');
      return;
    }

    setLoading(true);
    setError('');
    let successCount = 0;
    let failCount = 0;

    try {
      for (const jobId of selectedJobs) {
        const job = searchResults.find(j => j.job_id === jobId);
        if (job) {
          try {
            const response = await fetch(`${API_BASE}/apply/${job.job_id}`, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(job)
            });

            const result = await response.json();
            if (result.success) {
              successCount++;
            } else {
              failCount++;
            }
          } catch (err) {
            console.error('申请失败:', err);
            failCount++;
          }
        }
      }

      setSuccess(`批量申请完成：成功 ${successCount} 个，失败 ${failCount} 个`);
      updateSelectedJobs(new Set()); // 清空选择
      fetchStatus();
    } catch (err) {
      setError('批量申请失败: ' + err.message);
    }
    setLoading(false);
  };

  return (
    <Container maxWidth={false} sx={{
      mt: 4,
      mb: 4,
      maxWidth: 'calc(1400px + 189px)', // 扩展最大宽度以容纳更宽的卡片
      mx: 'auto',
      px: { xs: 2, sm: 3, md: 4 },
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      width: '100%'
    }}>
      <Box sx={{
        mb: 4,
        alignSelf: 'flex-start',
        width: '100%',
        maxWidth: 'calc(1400px + 189px)', // 扩展标题区域最大宽度
        paddingLeft: { xs: 2, md: 'calc(4rem + 4cm)' }
      }}>
        <Typography variant="h4" sx={{ mb: 2, color: 'primary.main', fontWeight: 'bold' }}>
          LinkedIn求职
        </Typography>
      </Box>
      

      
      <Box sx={{
        display: 'flex',
        gap: 3,
        width: '100%',
        justifyContent: 'center',
        alignItems: 'flex-start',
        maxWidth: 'calc(1400px + 189px)', // 扩展主要内容区域最大宽度
        margin: '0 auto',
        paddingLeft: { xs: 2, md: 'calc(4rem + 4cm)' },
        paddingRight: { xs: 2, md: 4 }
      }}>
        {/* 左侧：运行状态卡片 */}
        <Box sx={{ width: { xs: '100%', md: '25%' }, minWidth: 0, display: 'flex', flexDirection: 'column' }}>
          <Card sx={{ flex: 1, display: 'flex', flexDirection: 'column', minHeight: 0 }}>
            <CardContent sx={{ p: 3, flexGrow: 0 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <InfoIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                  运行状态
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Typography variant="body1">
                  找到职位: <Box component="span" sx={{ fontWeight: 'bold' }}>{automationStatus.progress.total_found}</Box>
                </Typography>
                <Typography variant="body1">
                  成功申请: <Box component="span" sx={{ fontWeight: 'bold', color: 'success.main' }}>{automationStatus.progress.total_applied}</Box>
                </Typography>
                <Typography variant="body1">
                  申请中职位: <Box component="span" sx={{ fontWeight: 'bold', color: 'info.main' }}>
                    {(() => {
                      const task = automationStatus.current_task;
                      // 过滤掉非申请相关的状态信息
                      if (!task ||
                          task.includes('已设置浏览器') ||
                          task.includes('等待用户操作') ||
                          task.includes('已登录') ||
                          task.includes('已登出') ||
                          task.includes('登录状态') ||
                          task.includes('验证') ||
                          task.includes('设置')) {
                        return '无';
                      }
                      // 提取职位申请信息
                      if (task.includes('正在申请职位：')) {
                        return task.replace(/^.*正在申请职位：/, '').replace(/\s*-\s*.*$/, '');
                      }
                      // 如果包含申请相关关键词，显示原文
                      if (task.includes('申请') || task.includes('投递')) {
                        return task;
                      }
                      return '无';
                    })()}
                  </Box>
                </Typography>
                <Box>
                  <Typography variant="body1" sx={{ display: 'flex', flexDirection: 'column' }}>
                    <Box>
                      自动化类型: <Box component="span" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                        {/* 暂时只显示Selenium类型 */}
                        {loginForm.automation_type === 'playwright_async' ? 'Playwright 异步' :
                         loginForm.automation_type === 'playwright' ? 'Playwright 同步' :
                         'Selenium'}
                      </Box>
                    </Box>
                    <Box sx={{
                      fontWeight: 'bold',
                      color: 'primary.main',
                      paddingLeft: '5.7em' // 使用padding精确对齐
                    }}>
                      {loginForm.automation_type === 'playwright_async' ? '(暂时不可用)' :
                       loginForm.automation_type === 'playwright' ? '(暂时不可用)' :
                       '(稳定推荐)'}
                    </Box>
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap' }}>
                  <Typography variant="body1">
                    登录状态: <Box component="span" sx={{ fontWeight: 'bold', color: automationStatus.is_logged_in ? 'success.main' : 'error.main' }}>{automationStatus.is_logged_in ? '已登录' : '未登录'}</Box>
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {!automationStatus.is_logged_in && (
                      <Button
                        size="small"
                        color="primary"
                        variant="outlined"
                        onClick={verifyLoginStatus}
                        disabled={loading}
                        sx={{ borderRadius: 2 }}
                      >
                        验证登录
                      </Button>
                    )}
                    {automationStatus.is_logged_in && (
                      <Button size="small" color="error" variant="outlined" startIcon={<LogoutIcon />} onClick={logoutLinkedIn} sx={{ borderRadius: 2 }}>
                        退出登录
                      </Button>
                    )}
                  </Box>
                </Box>
              </Box>
            </CardContent>
            

            
            <CardContent sx={{ p: 3, flexGrow: 0, pt: 2 }}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={() => setShowLoginDialog(true)}
                  startIcon={<SettingsIcon />}
                  fullWidth
                  sx={{ borderRadius: 2 }}
                >
                  登录设置
                </Button>

                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={async () => {
                    setLoading(true);
                    setError('');
                    try {
                      console.log('🔍 手动检查登录状态...');

                      // 先检查是否有自动化实例，如果没有就先设置
                      const statusResponse = await fetch(`${API_BASE}/status`);
                      if (statusResponse.ok) {
                        const status = statusResponse.json ? await statusResponse.json() : {};
                        console.log('当前状态:', status);

                        // 如果没有自动化类型，先设置自动化
                        if (!status.automation_type) {
                          console.log('🔧 检测到未设置自动化，先进行设置...');
                          await setupAutomation();
                        }
                      }

                      // 然后检查登录状态
                      const isLoggedIn = await checkLoginStatus();
                      if (!isLoggedIn) {
                        setError('当前未登录LinkedIn，请使用登录设置进行登录');
                      }
                    } catch (err) {
                      console.error('检查登录状态失败:', err);
                      setError('检查登录状态失败: ' + err.message);
                    }
                    setLoading(false);
                  }}
                  startIcon={<span style={{fontSize: '1.2em'}}>⚡</span>}
                  fullWidth
                  sx={{ borderRadius: 2 }}
                  disabled={loading}
                >
                  启动自动登录
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Box>

        {/* 右侧：其他卡片+搜索结果卡片 */}
        <Box sx={{
          width: { xs: '100%', md: 'calc(75% + 189px)' },
          minWidth: 0,
          display: 'flex',
          flexDirection: 'column',
          gap: 3
        }}>
          {/* 搜索设置 */}
          <Grid item xs={12}>
            <Card sx={{ boxShadow: 3, borderRadius: 2 }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, justifyContent: 'space-between' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <SearchIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                      搜索设置
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={searchForm.easy_apply_only}
                          onChange={(e) => updateSearchForm({ ...searchForm, easy_apply_only: e.target.checked })}
                          color="primary"
                        />
                      }
                      label="快速申请"
                      sx={{ m: 0 }}
                    />
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={searchJobs}
                      startIcon={searchLoading ? <CircularProgress size={20} color="primary" /> : <SearchIcon />}
                      disabled={!automationStatus.is_logged_in || searchLoading}
                      sx={{ borderRadius: 2 }}
                    >
                      {searchLoading ? '🤖 AI正在解析职位...' : '搜索职位'}
                    </Button>
                  </Box>
                </Box>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="关键词"
                      value={searchForm.keywords}
                      onChange={(e) => updateSearchForm({ ...searchForm, keywords: e.target.value })}
                      fullWidth
                      variant="outlined"
                      sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="地点"
                      value={searchForm.location}
                      onChange={(e) => updateSearchForm({ ...searchForm, location: e.target.value })}
                      fullWidth
                      variant="outlined"
                      sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* 批量申请设置 */}
          <Grid item xs={12}>
            <Card sx={{ boxShadow: 3, borderRadius: 2 }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, justifyContent: 'space-between' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <SettingsIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                      批量申请设置
                    </Typography>
                  </Box>
                  <Button
                    variant="contained"
                    color={automationStatus.is_running ? 'error' : 'success'}
                    onClick={automationStatus.is_running ? stopBatchApply : startBatchApply}
                    startIcon={automationStatus.is_running ? <StopIcon /> : <PlayIcon />}
                    disabled={!automationStatus.is_logged_in || loading}
                    sx={{ borderRadius: 2 }}
                  >
                    {automationStatus.is_running ? '停止批量申请' : '开始批量申请'}
                  </Button>
                </Box>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={4}>
                    <TextField
                      label="最大申请数量"
                      type="number"
                      value={batchApplyForm.max_applications}
                      onChange={(e) => setBatchApplyForm({ ...batchApplyForm, max_applications: parseInt(e.target.value) })}
                      fullWidth
                      variant="outlined"
                      sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <TextField
                      label="关键词（用逗号分隔）"
                      value={batchApplyForm.keywords.join(', ')}
                      onChange={(e) => setBatchApplyForm({ ...batchApplyForm, keywords: e.target.value.split(',').map(k => k.trim()) })}
                      fullWidth
                      variant="outlined"
                      sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                    />
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <TextField
                      label="地点"
                      value={batchApplyForm.location}
                      onChange={(e) => setBatchApplyForm({ ...batchApplyForm, location: e.target.value })}
                      fullWidth
                      variant="outlined"
                      sx={{ '& .MuiOutlinedInput-root': { borderRadius: 2 } }}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
          
          {/* 搜索结果 - 移到批量申请设置下面 */}
          <Grid item xs={12}>
            <Card sx={{ display: 'flex', flexDirection: 'column' }}>
              <CardContent sx={{ display: 'flex', flexDirection: 'column' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <SearchIcon sx={{ mr: 1, color: 'primary.main' }} />
                    <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                      搜索结果
                    </Typography>
                    {searchResults.length > 0 && (
                      <Chip
                        label={`${searchResults.length} 个职位`}
                        size="small"
                        sx={{ ml: 2, bgcolor: 'success.main', color: 'white' }}
                      />
                    )}
                  </Box>
                  {searchResults.length > 0 && (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={isAllSelected}
                            indeterminate={isIndeterminate}
                            onChange={(e) => handleSelectAll(e.target.checked)}
                            size="small"
                          />
                        }
                        label="全选当前页"
                        sx={{ mr: 1 }}
                      />
                      <Button
                        variant="contained"
                        color="success"
                        size="small"
                        onClick={applySelectedJobs}
                        disabled={loading || selectedJobs.size === 0}
                        sx={{ borderRadius: 2 }}
                      >
                        申请选中 ({selectedJobs.size})
                      </Button>
                    </Box>
                  )}
                </Box>

                {/* 分页控制和每页显示数量选择 */}
                {searchResults.length > 0 && (
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2, px: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        每页显示:
                      </Typography>
                      <Select
                        value={itemsPerPage}
                        onChange={(e) => {
                          updateItemsPerPage(e.target.value);
                        }}
                        size="small"
                        sx={{ minWidth: 80 }}
                      >
                        <MenuItem value={10}>10</MenuItem>
                        <MenuItem value={20}>20</MenuItem>
                        <MenuItem value={30}>30</MenuItem>
                        <MenuItem value={50}>50</MenuItem>
                      </Select>
                      <Typography variant="body2" color="text.secondary">
                        显示 {startIndex + 1}-{Math.min(endIndex, searchResults.length)} 共 {searchResults.length} 个
                      </Typography>
                    </Box>

                    {totalPages > 1 && (
                      <Pagination
                        count={totalPages}
                        page={currentPage}
                        onChange={(_, page) => updateCurrentPage(page)}
                        color="primary"
                        size="small"
                        showFirstButton
                        showLastButton
                      />
                    )}
                  </Box>
                )}
                <List sx={{ flexGrow: 1, overflow: 'auto', minHeight: 0, maxHeight: '600px' }}>
                  {currentPageJobs.length > 0 ? (
                    currentPageJobs.map((job, index) => (
                      <React.Fragment key={job.job_id}>
                        <ListItem sx={{
                          px: 2,
                          py: 1,
                          bgcolor: appliedJobs.has(job.job_id)
                            ? 'success.50'
                            : applyingJobs.has(job.job_id)
                              ? 'warning.50'
                              : 'background.paper',
                          borderRadius: 1,
                          mb: 1,
                          alignItems: 'center',
                          border: appliedJobs.has(job.job_id)
                            ? '1px solid'
                            : 'none',
                          borderColor: appliedJobs.has(job.job_id)
                            ? 'success.main'
                            : 'transparent',
                          opacity: appliedJobs.has(job.job_id) ? 0.8 : 1,
                          transition: 'all 0.3s ease'
                        }}>
                          <Checkbox
                            checked={selectedJobs.has(job.job_id)}
                            onChange={(e) => handleSelectJob(job.job_id, e.target.checked)}
                            size="small"
                            sx={{ mr: 1 }}
                          />
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexGrow: 1, minWidth: 0 }}>
                            {/* 职位标题 - 可点击链接 */}
                            <Link
                              href={job.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              sx={{
                                textDecoration: 'none',
                                color: 'text.primary',
                                '&:hover': {
                                  color: 'primary.main',
                                  textDecoration: 'underline'
                                }
                              }}
                            >
                              <Typography
                                variant="subtitle2"
                                sx={{
                                  fontWeight: 'medium',
                                  minWidth: '250px',
                                  maxWidth: '250px',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  whiteSpace: 'nowrap'
                                }}
                              >
                                {job.title}
                              </Typography>
                            </Link>

                            {/* 公司名称 */}
                            <Typography
                              variant="body2"
                              color="text.primary"
                              sx={{
                                fontWeight: 'medium',
                                minWidth: '180px',
                                maxWidth: '180px',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }}
                            >
                              🏢 {job.company}
                            </Typography>

                            {/* 地点 */}
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{
                                minWidth: '200px',
                                maxWidth: '200px',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }}
                            >
                              📍 {job.location}
                            </Typography>

                            {/* Easy Apply 标签和申请状态 */}
                            <Box sx={{ minWidth: '80px', display: 'flex', justifyContent: 'center', gap: 0.5 }}>
                              {job.is_easy_apply && (
                                <Chip
                                  label="E"
                                  size="small"
                                  color="success"
                                  sx={{ fontSize: '0.7rem', height: '20px', minWidth: '24px' }}
                                />
                              )}
                              {/* 申请状态标记 */}
                              {appliedJobs.has(job.job_id) && (
                                <Chip
                                  label="✓"
                                  size="small"
                                  color="primary"
                                  variant="filled"
                                  sx={{
                                    fontSize: '0.7rem',
                                    height: '20px',
                                    minWidth: '24px',
                                    bgcolor: 'success.main',
                                    color: 'white',
                                    '&:hover': {
                                      bgcolor: 'success.dark'
                                    }
                                  }}
                                />
                              )}
                              {applyingJobs.has(job.job_id) && (
                                <Chip
                                  label="⏳"
                                  size="small"
                                  color="warning"
                                  variant="filled"
                                  sx={{
                                    fontSize: '0.7rem',
                                    height: '20px',
                                    minWidth: '24px',
                                    animation: 'pulse 1.5s infinite'
                                  }}
                                />
                              )}
                            </Box>


                          </Box>

                          {/* 申请按钮 - 带进度条和状态 */}
                          {appliedJobs.has(job.job_id) ? (
                            <Button
                              variant="contained"
                              color="success"
                              size="small"
                              disabled
                              sx={{ borderRadius: 2, minWidth: '80px', ml: 1 }}
                              startIcon={<CheckIcon />}
                            >
                              已申请
                            </Button>
                          ) : applyingJobs.has(job.job_id) ? (
                            <Button
                              variant="outlined"
                              color="primary"
                              size="small"
                              disabled
                              sx={{
                                borderRadius: 2,
                                minWidth: '80px',
                                ml: 1,
                                position: 'relative',
                                overflow: 'hidden'
                              }}
                            >
                              <Box sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 1,
                                position: 'relative',
                                zIndex: 1
                              }}>
                                <CircularProgress size={16} color="primary" />
                                申请中
                              </Box>
                              {/* 进度条背景动画 */}
                              <Box
                                sx={{
                                  position: 'absolute',
                                  top: 0,
                                  left: 0,
                                  right: 0,
                                  bottom: 0,
                                  background: 'linear-gradient(90deg, transparent 0%, rgba(25, 118, 210, 0.1) 50%, transparent 100%)',
                                  animation: 'progress-wave 2s infinite',
                                  '@keyframes progress-wave': {
                                    '0%': { transform: 'translateX(-100%)' },
                                    '100%': { transform: 'translateX(100%)' }
                                  }
                                }}
                              />
                            </Button>
                          ) : (
                            <Button
                              variant="outlined"
                              color="primary"
                              onClick={() => applySingleJob(job)}
                              disabled={loading}
                              size="small"
                              sx={{ borderRadius: 2, minWidth: '80px', ml: 1 }}
                            >
                              申请
                            </Button>
                          )}
                        </ListItem>
                        {index < currentPageJobs.length - 1 && <Divider sx={{ my: 1 }} />}
                      </React.Fragment>
                    ))
                  ) : (
                    <Typography variant="body2" color="text.secondary" sx={{ p: 2, textAlign: 'left' }}>
                      暂无搜索结果，请点击"搜索职位"按钮开始搜索
                    </Typography>
                  )}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* 申请记录 - 移到最下面并平铺 */}
          <Grid item xs={12}>
            <Card sx={{ boxShadow: 3, borderRadius: 2 }}>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CheckIcon sx={{ mr: 1, color: 'success.main' }} />
                <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                  申请记录
                </Typography>
              </Box>
              <List sx={{ maxHeight: '400px', overflow: 'auto', pr: 1 }}>
                {automationStatus.progress.successful_applications.length > 0 || automationStatus.progress.failed_applications.length > 0 ? (
                  <>
                    {automationStatus.progress.successful_applications.map((app, index) => (
                      <React.Fragment key={index}>
                        <ListItem sx={{ px: 2, py: 1.5, bgcolor: 'background.paper', borderRadius: 1, mb: 1 }}>
                          <ListItemText
                            primary={<Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>{app.title}</Typography>}
                            secondary={
                              <>
                                <Typography component="span" variant="body2" color="text.primary" sx={{ fontWeight: 'medium' }}>
                                  {app.company}
                                </Typography>
                                <Typography component="span" variant="body2" color="text.secondary">
                                  {` - ${app.location}`}
                                </Typography>
                                {app.applied_at && (
                                  <Typography component="div" variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                                    📅 申请时间: {new Date(app.applied_at).toLocaleString('zh-CN', {
                                      year: 'numeric',
                                      month: '2-digit',
                                      day: '2-digit',
                                      hour: '2-digit',
                                      minute: '2-digit'
                                    })}
                                  </Typography>
                                )}
                              </>
                            }
                          />
                          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: 1 }}>
                            <Chip
                              label="成功"
                              color="success"
                              size="small"
                              icon={<CheckIcon />}
                              sx={{ borderRadius: 1 }}
                            />
                            {app.applied_at && (
                              <Typography variant="caption" color="text.secondary">
                                {(() => {
                                  const appliedTime = new Date(app.applied_at);
                                  const now = new Date();
                                  const diffMs = now - appliedTime;
                                  const diffMins = Math.floor(diffMs / (1000 * 60));
                                  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
                                  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

                                  if (diffMins < 60) {
                                    return `${diffMins}分钟前`;
                                  } else if (diffHours < 24) {
                                    return `${diffHours}小时前`;
                                  } else {
                                    return `${diffDays}天前`;
                                  }
                                })()}
                              </Typography>
                            )}
                          </Box>
                        </ListItem>
                        {index < automationStatus.progress.successful_applications.length - 1 && <Divider sx={{ my: 1 }} />}
                      </React.Fragment>
                    ))}
                    {automationStatus.progress.failed_applications.map((app, index) => (
                      <React.Fragment key={`failed-${index}`}>
                        <ListItem sx={{ px: 2, py: 1.5, bgcolor: 'background.paper', borderRadius: 1, mb: 1 }}>
                          <ListItemText
                            primary={<Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>{app.title}</Typography>}
                            secondary={
                              <>
                                <Typography component="span" variant="body2" color="text.primary" sx={{ fontWeight: 'medium' }}>
                                  {app.company}
                                </Typography>
                                <Typography component="span" variant="body2" color="text.secondary">
                                  {` - ${app.location}`}
                                </Typography>
                                {app.failed_at && (
                                  <Typography component="div" variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                                    📅 失败时间: {new Date(app.failed_at).toLocaleString('zh-CN', {
                                      year: 'numeric',
                                      month: '2-digit',
                                      day: '2-digit',
                                      hour: '2-digit',
                                      minute: '2-digit'
                                    })}
                                  </Typography>
                                )}
                              </>
                            }
                          />
                          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: 1 }}>
                            <Chip
                              label="失败"
                              color="error"
                              size="small"
                              icon={<ErrorIcon />}
                              sx={{ borderRadius: 1 }}
                            />
                            {app.failed_at && (
                              <Typography variant="caption" color="text.secondary">
                                {(() => {
                                  const failedTime = new Date(app.failed_at);
                                  const now = new Date();
                                  const diffMs = now - failedTime;
                                  const diffMins = Math.floor(diffMs / (1000 * 60));
                                  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
                                  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

                                  if (diffMins < 60) {
                                    return `${diffMins}分钟前`;
                                  } else if (diffHours < 24) {
                                    return `${diffHours}小时前`;
                                  } else {
                                    return `${diffDays}天前`;
                                  }
                                })()}
                              </Typography>
                            )}
                          </Box>
                        </ListItem>
                        {index < automationStatus.progress.failed_applications.length - 1 && <Divider sx={{ my: 1 }} />}
                      </React.Fragment>
                    ))}
                  </>
                ) : (
                  <Typography variant="body2" color="text.secondary" sx={{ p: 2, textAlign: 'left' }}>
                    暂无申请记录，开始批量申请后将在此显示
                  </Typography>
                )}
              </List>
            </CardContent>
          </Card>
          </Grid>
        </Box>
      </Box>

      {/* 登录对话框 */}
      <Dialog
        open={showLoginDialog}
        onClose={() => setShowLoginDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          LinkedIn登录
          <IconButton
            onClick={() => {
              setShowLoginDialog(false);
              setSuccess('登录对话框已手动关闭');
            }}
            sx={{ color: 'grey.500' }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2" sx={{ mb: 1 }}>
              <strong>智能登录检测：</strong>
            </Typography>
            <Typography variant="body2">
              • 如果您之前已登录LinkedIn，系统会自动检测并跳过登录步骤<br/>
              • 如果需要重新登录，请输入用户名密码进行登录<br/>
              • 登录过程中可能需要进行二次验证，请注意查看浏览器窗口
            </Typography>
          </Alert>

          {/* 显示当前任务状态 */}
          {automationStatus.current_task && (automationStatus.current_task.includes('验证') || automationStatus.current_task.includes('等待')) && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography variant="body2">
                {automationStatus.current_task}
              </Typography>
            </Alert>
          )}

          {/* 显示登录状态 */}
          {automationStatus.is_logged_in && (
            <Alert severity="success" sx={{ mb: 2 }}>
              <Typography variant="body2">
                ✅ 检测到已成功登录LinkedIn！对话框将自动关闭...
              </Typography>
            </Alert>
          )}

          {/* 显示成功信息 */}
          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              {success}
            </Alert>
          )}

          {/* 显示错误信息 */}
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
            <TextField
              label="邮箱"
              value={loginForm.email}
              onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}
              fullWidth
            />
            <TextField
              label="密码"
              type="password"
              value={loginForm.password}
              onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
              fullWidth
            />
            <FormControl fullWidth>
              <InputLabel id="automation-type-label">自动化类型</InputLabel>
              <Select
                labelId="automation-type-label"
                value={loginForm.automation_type}
                label="自动化类型"
                onChange={(e) => setLoginForm({ ...loginForm, automation_type: e.target.value })}
              >
                {/* 暂时屏蔽Playwright选项，保留代码但注释掉 */}
                {/* <MenuItem value="playwright_async">Playwright 异步 (最佳性能)</MenuItem> */}
                {/* <MenuItem value="playwright">Playwright 同步 (推荐)</MenuItem> */}
                <MenuItem value="selenium">Selenium (稳定推荐)</MenuItem>
              </Select>
            </FormControl>
            <FormControlLabel
              control={
                <Switch
                  checked={loginForm.headless}
                  onChange={(e) => setLoginForm({ ...loginForm, headless: e.target.checked })}
                />
              }
              label="无头模式"
            />
            <Typography variant="body2" color="text.secondary">
              注意：建议关闭无头模式，以便处理可能出现的验证码或二次验证。
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowLoginDialog(false)}>取消</Button>
          <Button onClick={loginLinkedIn} variant="contained" disabled={loading || automationStatus.is_logged_in}>
            {automationStatus.is_logged_in ? '已登录' : (loading ? '登录中...' : '登录')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 加载进度条 */}
      {loading && (
        <Box sx={{ width: '100%', position: 'fixed', top: 0, left: 0 }}>
          <LinearProgress />
          {automationStatus.current_task && (
            <Typography variant="body2" sx={{ textAlign: 'center', mt: 1, color: 'primary.main' }}>
              {automationStatus.current_task}
            </Typography>
          )}
        </Box>
      )}

      {/* 错误提示 */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={() => setError('')}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        sx={{ mb: 2 }}
      >
        <Alert severity="error" onClose={() => setError('')}>
          {error}
        </Alert>
      </Snackbar>

      {/* 成功提示 */}
      <Snackbar
        open={!!success}
        autoHideDuration={3000}
        onClose={() => setSuccess('')}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        sx={{ mb: 2 }}
      >
        <Alert severity="success" onClose={() => setSuccess('')}>
          {success}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default LinkedInAutomation;