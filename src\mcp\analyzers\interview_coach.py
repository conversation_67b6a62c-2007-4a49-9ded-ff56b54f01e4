#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
面试准备助手 - 基于MCP的智能面试辅导系统

此模块提供全面的面试准备功能，包括：
- 面试问题预测和准备
- 行为面试STAR方法指导
- 技术面试题目练习
- 模拟面试和反馈
- 面试技巧和策略
- 公司特定面试准备
- 薪资谈判指导
- 面试后跟进建议

作者: AI Assistant
创建时间: 2024
"""

import asyncio
import json
import time
import random
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
import logging
from pathlib import Path
from datetime import datetime, timedelta

# 导入MCP管理器
try:
    from src.mcp.core.mcp_manager import get_mcp_manager, MCPToolResult
except ImportError:
    # 如果导入失败，创建模拟实现
    class MCPToolResult:
        def __init__(self, success=True, result=None, error=None):
            self.success = success
            self.result = result
            self.error = error
    
    def get_mcp_manager():
        return None

# 导入其他分析器
try:
    from src.mcp.analyzers.job_analyzer import JobAnalysisResult
    from src.mcp.analyzers.company_analyzer import CompanyAnalysisResult
    from src.mcp.analyzers.resume_optimizer import ResumeContent
except ImportError:
    # 创建模拟类
    @dataclass
    class JobAnalysisResult:
        job_title: str = ""
        required_skills: List = field(default_factory=list)
        responsibilities: List[str] = field(default_factory=list)
        company_name: str = ""
        experience_requirements: str = ""
        industry: str = ""
    
    @dataclass
    class CompanyAnalysisResult:
        company_name: str = ""
        culture_values: List[str] = field(default_factory=list)
        interview_process: List[str] = field(default_factory=list)
        company_size: str = ""
        industry: str = ""
    
    @dataclass
    class ResumeContent:
        name: str = ""
        technical_skills: List[str] = field(default_factory=list)
        work_experience: List[Dict[str, Any]] = field(default_factory=list)
        target_role: str = ""
        experience_years: int = 0

# 导入现有模块（保持兼容性）
try:
    from src.utils.logger import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)


class InterviewType(Enum):
    """面试类型"""
    PHONE_SCREENING = "phone_screening"     # 电话筛选
    VIDEO_INTERVIEW = "video_interview"     # 视频面试
    TECHNICAL = "technical"                 # 技术面试
    BEHAVIORAL = "behavioral"               # 行为面试
    CASE_STUDY = "case_study"               # 案例研究
    PANEL = "panel"                         # 小组面试
    FINAL = "final"                         # 终面
    CULTURAL_FIT = "cultural_fit"           # 文化匹配
    PRESENTATION = "presentation"           # 演示面试
    CODING_CHALLENGE = "coding_challenge"   # 编程挑战


class QuestionCategory(Enum):
    """问题类别"""
    GENERAL = "general"                     # 通用问题
    TECHNICAL = "technical"                 # 技术问题
    BEHAVIORAL = "behavioral"               # 行为问题
    SITUATIONAL = "situational"             # 情境问题
    COMPANY_SPECIFIC = "company_specific"   # 公司特定
    ROLE_SPECIFIC = "role_specific"         # 角色特定
    LEADERSHIP = "leadership"               # 领导力
    PROBLEM_SOLVING = "problem_solving"     # 问题解决
    COMMUNICATION = "communication"         # 沟通能力
    MOTIVATION = "motivation"               # 动机相关


class DifficultyLevel(Enum):
    """难度级别"""
    EASY = "easy"           # 简单
    MEDIUM = "medium"       # 中等
    HARD = "hard"           # 困难
    EXPERT = "expert"       # 专家级


class AnswerQuality(Enum):
    """回答质量"""
    EXCELLENT = "excellent"  # 优秀
    GOOD = "good"            # 良好
    FAIR = "fair"            # 一般
    POOR = "poor"            # 较差
    NEEDS_WORK = "needs_work" # 需要改进


@dataclass
class InterviewQuestion:
    """面试问题数据类"""
    question_id: str
    question_text: str
    category: QuestionCategory
    difficulty: DifficultyLevel
    
    # 问题属性
    interview_types: List[InterviewType] = field(default_factory=list)
    target_roles: List[str] = field(default_factory=list)
    industries: List[str] = field(default_factory=list)
    
    # 回答指导
    answer_framework: str = ""  # 回答框架 (如STAR)
    key_points: List[str] = field(default_factory=list)
    sample_answer: str = ""
    dos_and_donts: Dict[str, List[str]] = field(default_factory=dict)
    
    # 评估标准
    evaluation_criteria: List[str] = field(default_factory=list)
    common_mistakes: List[str] = field(default_factory=list)
    
    # 元数据
    frequency: str = ""  # 出现频率: high, medium, low
    importance: str = ""  # 重要性: critical, high, medium, low
    tags: List[str] = field(default_factory=list)


@dataclass
class STARResponse:
    """STAR方法回答结构"""
    situation: str = ""     # 情境
    task: str = ""          # 任务
    action: str = ""        # 行动
    result: str = ""        # 结果
    
    # 额外信息
    learning: str = ""      # 学到的经验
    metrics: List[str] = field(default_factory=list)  # 量化指标
    skills_demonstrated: List[str] = field(default_factory=list)


@dataclass
class InterviewAnswer:
    """面试回答数据类"""
    answer_id: str
    question_id: str
    answer_text: str
    
    # STAR结构（如果适用）
    star_response: Optional[STARResponse] = None
    
    # 评估
    quality_score: float = 0.0  # 0-1分数
    quality_level: AnswerQuality = AnswerQuality.FAIR
    
    # 反馈
    strengths: List[str] = field(default_factory=list)
    weaknesses: List[str] = field(default_factory=list)
    improvement_suggestions: List[str] = field(default_factory=list)
    
    # 时间信息
    response_time: float = 0.0  # 回答时间（秒）
    optimal_time_range: Tuple[float, float] = (30.0, 120.0)
    
    # 元数据
    timestamp: float = field(default_factory=time.time)
    version: str = "1.0"


@dataclass
class MockInterview:
    """模拟面试数据类"""
    interview_id: str
    interview_type: InterviewType
    
    # 面试配置
    target_role: str
    company_name: str = ""
    duration_minutes: int = 60
    
    # 问题和回答
    questions: List[InterviewQuestion] = field(default_factory=list)
    answers: List[InterviewAnswer] = field(default_factory=list)
    
    # 整体评估
    overall_score: float = 0.0
    performance_summary: str = ""
    
    # 分类评分
    technical_score: float = 0.0
    behavioral_score: float = 0.0
    communication_score: float = 0.0
    confidence_score: float = 0.0
    
    # 反馈和建议
    strengths: List[str] = field(default_factory=list)
    areas_for_improvement: List[str] = field(default_factory=list)
    specific_recommendations: List[str] = field(default_factory=list)
    
    # 时间信息
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None
    actual_duration: float = 0.0
    
    # 状态
    status: str = "pending"  # pending, in_progress, completed


@dataclass
class InterviewPreparationPlan:
    """面试准备计划数据类"""
    plan_id: str
    target_interview: Dict[str, Any]  # 目标面试信息
    
    # 准备时间线
    preparation_days: int = 7
    daily_study_hours: float = 2.0
    
    # 学习内容
    technical_topics: List[str] = field(default_factory=list)
    behavioral_scenarios: List[str] = field(default_factory=list)
    company_research_points: List[str] = field(default_factory=list)
    
    # 练习计划
    practice_questions: List[InterviewQuestion] = field(default_factory=list)
    mock_interviews: List[MockInterview] = field(default_factory=list)
    
    # 每日任务
    daily_tasks: Dict[int, List[str]] = field(default_factory=dict)
    
    # 资源推荐
    recommended_resources: List[str] = field(default_factory=list)
    study_materials: List[str] = field(default_factory=list)
    
    # 进度跟踪
    completed_tasks: List[str] = field(default_factory=list)
    progress_percentage: float = 0.0
    
    # 元数据
    created_date: str = field(default_factory=lambda: datetime.now().isoformat())
    last_updated: str = field(default_factory=lambda: datetime.now().isoformat())


@dataclass
class InterviewCoachingResult:
    """面试辅导结果数据类"""
    coaching_id: str
    user_profile: ResumeContent
    target_job: JobAnalysisResult
    timestamp: float = field(default_factory=time.time)
    
    # 输入信息
    company_info: Optional[CompanyAnalysisResult] = None
    
    # 准备计划
    preparation_plan: InterviewPreparationPlan
    
    # 问题库
    predicted_questions: List[InterviewQuestion] = field(default_factory=list)
    practice_questions: List[InterviewQuestion] = field(default_factory=list)
    
    # 模拟面试
    mock_interviews: List[MockInterview] = field(default_factory=list)
    
    # 技能评估
    current_readiness_score: float = 0.0
    skill_gaps: List[str] = field(default_factory=list)
    
    # 个性化建议
    preparation_tips: List[str] = field(default_factory=list)
    interview_strategies: List[str] = field(default_factory=list)
    common_pitfalls: List[str] = field(default_factory=list)
    
    # 公司特定准备
    company_insights: List[str] = field(default_factory=list)
    culture_fit_tips: List[str] = field(default_factory=list)
    
    # 薪资谈判
    salary_negotiation_tips: List[str] = field(default_factory=list)
    market_salary_data: Dict[str, Any] = field(default_factory=dict)
    
    # 跟进建议
    post_interview_actions: List[str] = field(default_factory=list)
    
    # 元数据
    processing_duration: float = 0.0
    coaching_version: str = "1.0"


class InterviewCoach:
    """面试准备助手主类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化面试准备助手
        
        Args:
            config: 助手配置
        """
        self.config = config or {}
        self.mcp_manager = get_mcp_manager()
        
        # 加载问题库和数据
        self.question_bank = self._load_question_bank()
        self.answer_frameworks = self._load_answer_frameworks()
        self.company_interview_data = self._load_company_interview_data()
        self.salary_data = self._load_salary_data()
        
        logger.info("面试准备助手初始化完成")
    
    def _load_question_bank(self) -> Dict[str, List[InterviewQuestion]]:
        """加载面试问题库"""
        return {
            'general': [
                InterviewQuestion(
                    question_id="gen_001",
                    question_text="请简单介绍一下你自己",
                    category=QuestionCategory.GENERAL,
                    difficulty=DifficultyLevel.EASY,
                    interview_types=[InterviewType.PHONE_SCREENING, InterviewType.VIDEO_INTERVIEW],
                    answer_framework="简洁的个人介绍框架",
                    key_points=["教育背景", "工作经验", "核心技能", "职业目标"],
                    frequency="high",
                    importance="high"
                ),
                InterviewQuestion(
                    question_id="gen_002",
                    question_text="为什么想要这个职位？",
                    category=QuestionCategory.MOTIVATION,
                    difficulty=DifficultyLevel.MEDIUM,
                    interview_types=[InterviewType.VIDEO_INTERVIEW, InterviewType.FINAL],
                    key_points=["职业发展", "公司匹配", "技能运用", "个人兴趣"],
                    frequency="high",
                    importance="critical"
                )
            ],
            'technical': [
                InterviewQuestion(
                    question_id="tech_001",
                    question_text="解释一下你最近参与的技术项目",
                    category=QuestionCategory.TECHNICAL,
                    difficulty=DifficultyLevel.MEDIUM,
                    interview_types=[InterviewType.TECHNICAL],
                    answer_framework="STAR",
                    key_points=["技术栈", "挑战", "解决方案", "结果"],
                    frequency="high",
                    importance="critical"
                ),
                InterviewQuestion(
                    question_id="tech_002",
                    question_text="如何处理技术债务？",
                    category=QuestionCategory.TECHNICAL,
                    difficulty=DifficultyLevel.HARD,
                    interview_types=[InterviewType.TECHNICAL],
                    key_points=["识别", "优先级", "重构策略", "团队协作"],
                    frequency="medium",
                    importance="high"
                )
            ],
            'behavioral': [
                InterviewQuestion(
                    question_id="beh_001",
                    question_text="描述一次你面临困难挑战的经历",
                    category=QuestionCategory.BEHAVIORAL,
                    difficulty=DifficultyLevel.MEDIUM,
                    interview_types=[InterviewType.BEHAVIORAL],
                    answer_framework="STAR",
                    key_points=["具体情境", "你的行动", "克服过程", "最终结果"],
                    frequency="high",
                    importance="high"
                ),
                InterviewQuestion(
                    question_id="beh_002",
                    question_text="告诉我一次你领导团队的经历",
                    category=QuestionCategory.LEADERSHIP,
                    difficulty=DifficultyLevel.HARD,
                    interview_types=[InterviewType.BEHAVIORAL, InterviewType.PANEL],
                    answer_framework="STAR",
                    key_points=["团队规模", "目标设定", "激励方式", "成果达成"],
                    frequency="medium",
                    importance="high"
                )
            ]
        }
    
    def _load_answer_frameworks(self) -> Dict[str, Dict[str, Any]]:
        """加载回答框架"""
        return {
            'STAR': {
                'name': 'STAR方法',
                'description': '情境-任务-行动-结果',
                'structure': {
                    'Situation': '描述具体情境和背景',
                    'Task': '说明你需要完成的任务',
                    'Action': '详述你采取的具体行动',
                    'Result': '展示最终的结果和影响'
                },
                'tips': [
                    '保持具体和量化',
                    '突出你的个人贡献',
                    '展示学习和成长',
                    '控制回答时间在2-3分钟'
                ]
            },
            'CAR': {
                'name': 'CAR方法',
                'description': '挑战-行动-结果',
                'structure': {
                    'Challenge': '描述面临的挑战',
                    'Action': '说明采取的行动',
                    'Result': '展示获得的结果'
                }
            },
            'SOAR': {
                'name': 'SOAR方法',
                'description': '情境-目标-行动-结果',
                'structure': {
                    'Situation': '描述情境',
                    'Objective': '明确目标',
                    'Action': '采取行动',
                    'Result': '达成结果'
                }
            }
        }
    
    def _load_company_interview_data(self) -> Dict[str, Dict[str, Any]]:
        """加载公司面试数据"""
        return {
            'google': {
                'interview_process': [
                    '电话筛选',
                    '技术电话面试',
                    '现场面试（4-5轮）',
                    '团队匹配'
                ],
                'focus_areas': ['算法', '系统设计', '编程能力', '文化匹配'],
                'common_questions': [
                    '设计一个大规模系统',
                    '优化算法复杂度',
                    '处理大数据问题'
                ],
                'tips': [
                    '重视算法和数据结构',
                    '准备系统设计问题',
                    '展示学习能力',
                    '体现Google价值观'
                ]
            },
            'microsoft': {
                'interview_process': [
                    '招聘人员筛选',
                    '技术筛选',
                    '现场面试循环',
                    'As Appropriate面试'
                ],
                'focus_areas': ['技术深度', '问题解决', '协作能力', '成长心态'],
                'tips': [
                    '展示成长心态',
                    '强调协作经验',
                    '准备技术深度问题',
                    '了解Microsoft文化'
                ]
            }
        }
    
    def _load_salary_data(self) -> Dict[str, Any]:
        """加载薪资数据"""
        return {
            'negotiation_tips': [
                '研究市场薪资水平',
                '准备多个谈判点',
                '考虑整体薪酬包',
                '保持专业和积极',
                '有备选方案'
            ],
            'timing': {
                'best_time': '收到offer后',
                'avoid': '面试初期提及薪资',
                'preparation': '面试前研究市场行情'
            },
            'components': [
                '基本工资',
                '奖金',
                '股票期权',
                '福利待遇',
                '职业发展机会'
            ]
        }
    
    async def prepare_interview(self, user_profile: ResumeContent,
                              target_job: JobAnalysisResult,
                              company_info: Optional[CompanyAnalysisResult] = None,
                              preparation_days: int = 7) -> InterviewCoachingResult:
        """准备面试
        
        Args:
            user_profile: 用户简历档案
            target_job: 目标职位
            company_info: 公司信息
            preparation_days: 准备天数
        
        Returns:
            InterviewCoachingResult: 面试辅导结果
        """
        start_time = time.time()
        
        logger.info(f"开始为 {target_job.job_title} 职位准备面试")
        
        # 创建辅导结果对象
        result = InterviewCoachingResult(
            coaching_id=str(int(time.time())),
            user_profile=user_profile,
            target_job=target_job,
            company_info=company_info
        )
        
        try:
            # 1. 预测面试问题
            result.predicted_questions = await self._predict_interview_questions(
                user_profile, target_job, company_info
            )
            
            # 2. 生成练习问题
            result.practice_questions = await self._generate_practice_questions(
                user_profile, target_job
            )
            
            # 3. 创建准备计划
            result.preparation_plan = await self._create_preparation_plan(
                user_profile, target_job, company_info, preparation_days
            )
            
            # 4. 评估当前准备程度
            result.current_readiness_score = await self._assess_readiness(
                user_profile, target_job
            )
            
            # 5. 识别技能缺口
            result.skill_gaps = await self._identify_skill_gaps(
                user_profile, target_job
            )
            
            # 6. 生成个性化建议
            result.preparation_tips = await self._generate_preparation_tips(
                user_profile, target_job, company_info
            )
            
            # 7. 面试策略
            result.interview_strategies = await self._generate_interview_strategies(
                user_profile, target_job
            )
            
            # 8. 常见陷阱
            result.common_pitfalls = await self._identify_common_pitfalls(
                user_profile, target_job
            )
            
            # 9. 公司特定准备
            if company_info:
                result.company_insights = await self._generate_company_insights(company_info)
                result.culture_fit_tips = await self._generate_culture_fit_tips(
                    user_profile, company_info
                )
            
            # 10. 薪资谈判准备
            result.salary_negotiation_tips = self._generate_salary_negotiation_tips()
            result.market_salary_data = await self._get_market_salary_data(target_job)
            
            # 11. 面试后跟进
            result.post_interview_actions = self._generate_post_interview_actions()
            
            result.processing_duration = time.time() - start_time
            
            logger.info(f"面试准备完成 (耗时: {result.processing_duration:.2f}s)")
            
            return result
            
        except Exception as e:
            logger.error(f"面试准备失败: {e}")
            result.processing_duration = time.time() - start_time
            return result
    
    async def _predict_interview_questions(self, user_profile: ResumeContent,
                                         target_job: JobAnalysisResult,
                                         company_info: Optional[CompanyAnalysisResult]) -> List[InterviewQuestion]:
        """预测面试问题"""
        predicted_questions = []
        
        # 1. 通用问题
        predicted_questions.extend(self.question_bank['general'])
        
        # 2. 基于职位的技术问题
        job_title_lower = target_job.job_title.lower()
        if any(keyword in job_title_lower for keyword in ['engineer', 'developer']):
            predicted_questions.extend(self.question_bank['technical'])
        
        # 3. 行为问题
        predicted_questions.extend(self.question_bank['behavioral'])
        
        # 4. 基于经验级别的问题
        experience_years = getattr(user_profile, 'experience_years', 0)
        if experience_years >= 5:
            # 添加领导力问题
            leadership_questions = [
                q for q in self.question_bank['behavioral'] 
                if q.category == QuestionCategory.LEADERSHIP
            ]
            predicted_questions.extend(leadership_questions)
        
        # 5. 公司特定问题
        if company_info and company_info.company_name.lower() in self.company_interview_data:
            company_data = self.company_interview_data[company_info.company_name.lower()]
            # 基于公司数据添加特定问题
            for question_text in company_data.get('common_questions', []):
                question = InterviewQuestion(
                    question_id=f"company_{hash(question_text)}",
                    question_text=question_text,
                    category=QuestionCategory.COMPANY_SPECIFIC,
                    difficulty=DifficultyLevel.MEDIUM,
                    frequency="medium",
                    importance="high"
                )
                predicted_questions.append(question)
        
        return predicted_questions
    
    async def _generate_practice_questions(self, user_profile: ResumeContent,
                                         target_job: JobAnalysisResult) -> List[InterviewQuestion]:
        """生成练习问题"""
        practice_questions = []
        
        # 基于技能生成技术问题
        for skill in user_profile.technical_skills:
            question = InterviewQuestion(
                question_id=f"practice_{hash(skill)}",
                question_text=f"请详细介绍你在 {skill} 方面的经验",
                category=QuestionCategory.TECHNICAL,
                difficulty=DifficultyLevel.MEDIUM,
                answer_framework="STAR",
                key_points=["项目背景", "技术应用", "遇到的挑战", "解决方案", "学到的经验"]
            )
            practice_questions.append(question)
        
        # 基于工作经验生成行为问题
        for exp in user_profile.work_experience:
            if isinstance(exp, dict) and 'position' in exp:
                question = InterviewQuestion(
                    question_id=f"exp_{hash(exp['position'])}",
                    question_text=f"描述你在 {exp['position']} 职位上的主要成就",
                    category=QuestionCategory.BEHAVIORAL,
                    difficulty=DifficultyLevel.MEDIUM,
                    answer_framework="STAR"
                )
                practice_questions.append(question)
        
        return practice_questions[:10]  # 限制数量
    
    async def _create_preparation_plan(self, user_profile: ResumeContent,
                                     target_job: JobAnalysisResult,
                                     company_info: Optional[CompanyAnalysisResult],
                                     preparation_days: int) -> InterviewPreparationPlan:
        """创建准备计划"""
        plan = InterviewPreparationPlan(
            plan_id=f"plan_{int(time.time())}",
            target_interview={
                'job_title': target_job.job_title,
                'company': target_job.company_name,
                'industry': target_job.industry
            },
            preparation_days=preparation_days
        )
        
        # 技术主题
        plan.technical_topics = [skill.name for skill in target_job.required_skills[:5]]
        
        # 行为场景
        plan.behavioral_scenarios = [
            "团队协作经历",
            "解决困难问题",
            "处理冲突情况",
            "领导项目经验",
            "学习新技术"
        ]
        
        # 公司研究要点
        plan.company_research_points = [
            "公司历史和发展",
            "产品和服务",
            "企业文化和价值观",
            "最近新闻和动态",
            "竞争对手分析"
        ]
        
        # 每日任务分配
        daily_hours = plan.daily_study_hours
        for day in range(1, preparation_days + 1):
            tasks = []
            
            if day <= 2:
                tasks.extend([
                    "研究公司背景 (30分钟)",
                    "准备自我介绍 (30分钟)",
                    "复习技术基础 (60分钟)"
                ])
            elif day <= 4:
                tasks.extend([
                    "练习行为问题 (45分钟)",
                    "技术深度复习 (60分钟)",
                    "模拟面试练习 (15分钟)"
                ])
            else:
                tasks.extend([
                    "综合模拟面试 (60分钟)",
                    "薪资谈判准备 (30分钟)",
                    "最终复习 (30分钟)"
                ])
            
            plan.daily_tasks[day] = tasks
        
        # 推荐资源
        plan.recommended_resources = [
            "LeetCode - 算法练习",
            "Glassdoor - 面试经验",
            "公司官网和博客",
            "行业报告和新闻",
            "技术文档和教程"
        ]
        
        return plan
    
    async def _assess_readiness(self, user_profile: ResumeContent,
                              target_job: JobAnalysisResult) -> float:
        """评估准备程度"""
        readiness_factors = []
        
        # 技能匹配度
        user_skills = set(skill.lower() for skill in user_profile.technical_skills)
        required_skills = set(skill.name.lower() for skill in target_job.required_skills)
        
        if required_skills:
            skill_match = len(user_skills & required_skills) / len(required_skills)
            readiness_factors.append(skill_match * 0.4)
        
        # 经验匹配度
        experience_years = getattr(user_profile, 'experience_years', 0)
        required_years = self._extract_experience_years(target_job.experience_requirements)
        
        if required_years > 0:
            exp_match = min(experience_years / required_years, 1.0)
            readiness_factors.append(exp_match * 0.3)
        
        # 行业经验
        if target_job.industry and hasattr(user_profile, 'target_industry'):
            industry_match = 1.0 if target_job.industry.lower() == user_profile.target_industry.lower() else 0.5
            readiness_factors.append(industry_match * 0.2)
        
        # 基础准备（假设有一定基础）
        readiness_factors.append(0.6 * 0.1)  # 基础分
        
        return sum(readiness_factors) if readiness_factors else 0.5
    
    def _extract_experience_years(self, experience_text: str) -> int:
        """从经验描述中提取年数"""
        if not experience_text:
            return 0
        
        import re
        patterns = [r'(\d+)\+?\s*years?', r'(\d+)\+?\s*yrs?', r'(\d+)\+?\s*年']
        
        for pattern in patterns:
            match = re.search(pattern, experience_text.lower())
            if match:
                return int(match.group(1))
        
        return 2  # 默认值
    
    async def _identify_skill_gaps(self, user_profile: ResumeContent,
                                 target_job: JobAnalysisResult) -> List[str]:
        """识别技能缺口"""
        user_skills = set(skill.lower() for skill in user_profile.technical_skills)
        required_skills = set(skill.name.lower() for skill in target_job.required_skills)
        
        skill_gaps = list(required_skills - user_skills)
        return skill_gaps[:5]  # 返回前5个最重要的缺口
    
    async def _generate_preparation_tips(self, user_profile: ResumeContent,
                                       target_job: JobAnalysisResult,
                                       company_info: Optional[CompanyAnalysisResult]) -> List[str]:
        """生成准备建议"""
        tips = []
        
        # 通用建议
        tips.extend([
            "提前研究公司和职位信息",
            "准备具体的项目案例和成就",
            "练习清晰简洁的表达",
            "准备针对性的问题询问面试官"
        ])
        
        # 基于经验级别的建议
        experience_years = getattr(user_profile, 'experience_years', 0)
        if experience_years < 2:
            tips.extend([
                "重点展示学习能力和潜力",
                "准备学校项目和实习经历",
                "展示对技术的热情"
            ])
        elif experience_years >= 5:
            tips.extend([
                "准备领导力和管理经验",
                "展示战略思维和决策能力",
                "准备指导他人的经历"
            ])
        
        # 技术相关建议
        if any(keyword in target_job.job_title.lower() for keyword in ['engineer', 'developer']):
            tips.extend([
                "复习核心算法和数据结构",
                "准备系统设计问题",
                "练习代码实现和优化"
            ])
        
        return tips
    
    async def _generate_interview_strategies(self, user_profile: ResumeContent,
                                           target_job: JobAnalysisResult) -> List[str]:
        """生成面试策略"""
        strategies = [
            "使用STAR方法回答行为问题",
            "保持积极和自信的态度",
            "主动询问有意义的问题",
            "展示学习和适应能力",
            "强调与团队的协作经验",
            "准备失败经历和学到的教训",
            "量化你的成就和影响",
            "展示对公司和行业的了解"
        ]
        
        return strategies
    
    async def _identify_common_pitfalls(self, user_profile: ResumeContent,
                                      target_job: JobAnalysisResult) -> List[str]:
        """识别常见陷阱"""
        pitfalls = [
            "回答过于冗长或偏离主题",
            "没有准备具体的例子",
            "对公司了解不足",
            "过分批评前雇主",
            "没有准备问题询问面试官",
            "技术回答缺乏深度",
            "忽视软技能的重要性",
            "薪资期望不切实际"
        ]
        
        return pitfalls
    
    async def _generate_company_insights(self, company_info: CompanyAnalysisResult) -> List[str]:
        """生成公司洞察"""
        insights = []
        
        if company_info.culture_values:
            insights.append(f"公司重视: {', '.join(company_info.culture_values)}")
        
        if company_info.company_size:
            insights.append(f"公司规模: {company_info.company_size}")
        
        if company_info.industry:
            insights.append(f"所属行业: {company_info.industry}")
        
        # 基于公司名称的特定洞察
        company_name_lower = company_info.company_name.lower()
        if company_name_lower in self.company_interview_data:
            company_data = self.company_interview_data[company_name_lower]
            insights.extend(company_data.get('tips', []))
        
        return insights
    
    async def _generate_culture_fit_tips(self, user_profile: ResumeContent,
                                       company_info: CompanyAnalysisResult) -> List[str]:
        """生成文化匹配建议"""
        tips = []
        
        if company_info.culture_values:
            for value in company_info.culture_values:
                tips.append(f"准备展示你如何体现 '{value}' 这一价值观的例子")
        
        tips.extend([
            "研究公司的使命和愿景",
            "了解公司的工作方式和团队文化",
            "准备说明为什么想在这家公司工作",
            "展示你对公司价值观的认同"
        ])
        
        return tips
    
    def _generate_salary_negotiation_tips(self) -> List[str]:
        """生成薪资谈判建议"""
        return self.salary_data['negotiation_tips']
    
    async def _get_market_salary_data(self, target_job: JobAnalysisResult) -> Dict[str, Any]:
        """获取市场薪资数据"""
        # 简化的薪资数据
        return {
            'position': target_job.job_title,
            'salary_range': target_job.salary_range or "市场价格",
            'factors': [
                "经验年限",
                "技能水平",
                "地理位置",
                "公司规模",
                "行业类型"
            ],
            'negotiation_timing': self.salary_data['timing']
        }
    
    def _generate_post_interview_actions(self) -> List[str]:
        """生成面试后行动建议"""
        return [
            "24小时内发送感谢邮件",
            "总结面试表现和改进点",
            "跟进面试进度（适当时机）",
            "继续其他机会的申请",
            "准备可能的后续面试",
            "保持积极的心态",
            "记录面试经验和学习"
        ]
    
    async def conduct_mock_interview(self, user_profile: ResumeContent,
                                   interview_type: InterviewType,
                                   duration_minutes: int = 30) -> MockInterview:
        """进行模拟面试"""
        mock_interview = MockInterview(
            interview_id=f"mock_{int(time.time())}",
            interview_type=interview_type,
            target_role=user_profile.target_role,
            duration_minutes=duration_minutes,
            status="in_progress"
        )
        
        # 选择适合的问题
        suitable_questions = []
        for category_questions in self.question_bank.values():
            for question in category_questions:
                if interview_type in question.interview_types:
                    suitable_questions.append(question)
        
        # 随机选择问题
        num_questions = min(5, len(suitable_questions))
        mock_interview.questions = random.sample(suitable_questions, num_questions)
        
        return mock_interview
    
    async def evaluate_answer(self, question: InterviewQuestion,
                            answer_text: str,
                            response_time: float = 60.0) -> InterviewAnswer:
        """评估面试回答"""
        answer = InterviewAnswer(
            answer_id=f"ans_{int(time.time())}",
            question_id=question.question_id,
            answer_text=answer_text,
            response_time=response_time
        )
        
        # 简化的评估逻辑
        answer.quality_score = self._calculate_answer_quality(question, answer_text)
        answer.quality_level = self._determine_quality_level(answer.quality_score)
        
        # 生成反馈
        answer.strengths, answer.weaknesses, answer.improvement_suggestions = \
            self._generate_answer_feedback(question, answer_text, answer.quality_score)
        
        return answer
    
    def _calculate_answer_quality(self, question: InterviewQuestion, answer_text: str) -> float:
        """计算回答质量分数"""
        if not answer_text or len(answer_text.strip()) < 10:
            return 0.1
        
        score = 0.5  # 基础分
        
        # 长度检查
        word_count = len(answer_text.split())
        if 50 <= word_count <= 200:
            score += 0.2
        
        # 关键词检查
        if question.key_points:
            mentioned_points = sum(1 for point in question.key_points 
                                 if point.lower() in answer_text.lower())
            score += (mentioned_points / len(question.key_points)) * 0.3
        
        return min(score, 1.0)
    
    def _determine_quality_level(self, score: float) -> AnswerQuality:
        """确定回答质量等级"""
        if score >= 0.9:
            return AnswerQuality.EXCELLENT
        elif score >= 0.7:
            return AnswerQuality.GOOD
        elif score >= 0.5:
            return AnswerQuality.FAIR
        elif score >= 0.3:
            return AnswerQuality.POOR
        else:
            return AnswerQuality.NEEDS_WORK
    
    def _generate_answer_feedback(self, question: InterviewQuestion,
                                answer_text: str, quality_score: float) -> Tuple[List[str], List[str], List[str]]:
        """生成回答反馈"""
        strengths = []
        weaknesses = []
        suggestions = []
        
        # 基于质量分数的反馈
        if quality_score >= 0.7:
            strengths.append("回答内容充实")
        else:
            weaknesses.append("回答需要更多细节")
            suggestions.append("增加具体的例子和数据")
        
        # 长度检查
        word_count = len(answer_text.split())
        if word_count < 30:
            weaknesses.append("回答过于简短")
            suggestions.append("提供更详细的解释")
        elif word_count > 300:
            weaknesses.append("回答过于冗长")
            suggestions.append("保持回答简洁有力")
        
        # STAR方法检查（对于行为问题）
        if question.category == QuestionCategory.BEHAVIORAL:
            star_keywords = ['situation', 'task', 'action', 'result', '情境', '任务', '行动', '结果']
            if any(keyword in answer_text.lower() for keyword in star_keywords):
                strengths.append("使用了结构化回答方法")
            else:
                suggestions.append("考虑使用STAR方法组织回答")
        
        return strengths, weaknesses, suggestions


# 测试代码
if __name__ == "__main__":
    async def test_interview_coach():
        """测试面试准备助手"""
        print("=== 面试准备助手测试 ===")
        
        # 创建测试数据
        user_profile = ResumeContent(
            name="李四",
            technical_skills=["Python", "Django", "React", "PostgreSQL"],
            target_role="高级后端工程师",
            experience_years=4
        )
        
        target_job = JobAnalysisResult(
            job_title="高级后端工程师",
            company_name="科技创新公司",
            industry="科技",
            experience_requirements="3-5年经验"
        )
        
        # 创建面试助手
        coach = InterviewCoach()
        
        # 准备面试
        result = await coach.prepare_interview(
            user_profile=user_profile,
            target_job=target_job,
            preparation_days=5
        )
        
        # 输出结果
        print(f"\n=== 面试准备结果 ===")
        print(f"当前准备程度: {result.current_readiness_score:.1%}")
        print(f"处理时间: {result.processing_duration:.2f}秒")
        
        print(f"\n=== 预测问题 ({len(result.predicted_questions)} 个) ===")
        for i, q in enumerate(result.predicted_questions[:5], 1):
            print(f"{i}. {q.question_text} ({q.category.value})")
        
        print(f"\n=== 技能缺口 ===")
        for gap in result.skill_gaps:
            print(f"- {gap}")
        
        print(f"\n=== 准备建议 ===")
        for tip in result.preparation_tips[:5]:
            print(f"- {tip}")
        
        print(f"\n=== 面试策略 ===")
        for strategy in result.interview_strategies[:5]:
            print(f"- {strategy}")
        
        print(f"\n=== 准备计划 ===")
        plan = result.preparation_plan
        print(f"准备天数: {plan.preparation_days} 天")
        print(f"每日学习: {plan.daily_study_hours} 小时")
        
        print(f"\n第1天任务:")
        for task in plan.daily_tasks.get(1, []):
            print(f"  - {task}")
        
        # 模拟面试测试
        print(f"\n=== 模拟面试测试 ===")
        mock_interview = await coach.conduct_mock_interview(
            user_profile=user_profile,
            interview_type=InterviewType.BEHAVIORAL,
            duration_minutes=20
        )
        
        print(f"模拟面试ID: {mock_interview.interview_id}")
        print(f"面试类型: {mock_interview.interview_type.value}")
        print(f"问题数量: {len(mock_interview.questions)}")
        
        # 测试回答评估
        if mock_interview.questions:
            test_question = mock_interview.questions[0]
            test_answer = "我在之前的项目中遇到了一个技术挑战，通过研究和团队协作，最终成功解决了问题，项目按时交付。"
            
            evaluated_answer = await coach.evaluate_answer(
                question=test_question,
                answer_text=test_answer,
                response_time=45.0
            )
            
            print(f"\n=== 回答评估 ===")
            print(f"问题: {test_question.question_text}")
            print(f"回答质量: {evaluated_answer.quality_level.value} ({evaluated_answer.quality_score:.1%})")
            print(f"优点: {', '.join(evaluated_answer.strengths)}")
            print(f"改进建议: {', '.join(evaluated_answer.improvement_suggestions)}")
        
        print("\n=== 测试完成 ===")
    
    # 运行测试
    asyncio.run(test_interview_coach())