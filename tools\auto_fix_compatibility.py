#!/usr/bin/env python3
"""
自动检测并修复 Selenium/undetected-chromedriver/Chrome 兼容性与配置问题的脚本
适用于 Windows
"""
import os
import sys
import subprocess
import shutil
import time
import platform

# 1. 检查 pip 包版本
def check_package_version(pkg_name):
    try:
        import pkg_resources
        version = pkg_resources.get_distribution(pkg_name).version
        return version
    except Exception:
        return None

def upgrade_package(pkg_name):
    print(f"升级 {pkg_name} ...")
    subprocess.run([sys.executable, '-m', 'pip', 'install', '-U', pkg_name], check=False)

# 2. 检查 Chrome 浏览器主版本
def get_chrome_version():
    try:
        if platform.system() == 'Windows':
            import winreg
            reg_path = r'SOFTWARE\\Google\\Chrome\\BLBeacon'
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, reg_path)
            version, _ = winreg.QueryValueEx(key, 'version')
            return version
        else:
            result = subprocess.run(['google-chrome', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                return result.stdout.strip().split()[-1]
    except Exception:
        return None

# 3. 清理 undetected_chromedriver 配置目录
def clean_profile_dir():
    user_profile = os.path.expanduser('~')
    chrome_profile = os.path.join(user_profile, '.linkedin_automation', 'chrome_profile')
    if os.path.exists(chrome_profile):
        print(f"检测到旧的 Chrome 配置目录: {chrome_profile}\n正在删除...")
        try:
            shutil.rmtree(chrome_profile)
            print("✅ 配置目录已删除")
        except Exception as e:
            print(f"❌ 删除失败: {e}")
    else:
        print("未检测到旧的 Chrome 配置目录，无需清理。")

# 4. 检查三者主版本号并输出建议
def check_versions():
    print("\n=== 版本检测 ===")
    selenium_ver = check_package_version('selenium')
    uc_ver = check_package_version('undetected-chromedriver')
    chrome_ver = get_chrome_version()
    print(f"selenium: {selenium_ver}")
    print(f"undetected-chromedriver: {uc_ver}")
    print(f"Chrome 浏览器: {chrome_ver}")
    if selenium_ver and uc_ver and chrome_ver:
        chrome_major = chrome_ver.split('.')[0]
        print(f"Chrome 主版本: {chrome_major}")
        print("建议：三者均保持最新版，主版本一致。若仍有问题，优先清理 profile 并重试。\n")
    else:
        print("⚠️ 有组件未检测到，请检查是否已正确安装 Chrome、selenium、undetected-chromedriver。\n")

# 5. 主流程
def main():
    print("\n==== 自动检测与修复脚本 ====")
    check_versions()
    print("\n1. 开始升级 selenium 和 undetected-chromedriver ...")
    upgrade_package('selenium')
    upgrade_package('undetected-chromedriver')
    print("\n2. 清理 undetected_chromedriver 配置目录 ...")
    clean_profile_dir()
    print("\n3. 检查完成。请重启你的自动化脚本。若仍有问题，建议重启电脑后再试。\n")

if __name__ == '__main__':
    main()
