#!/usr/bin/env python3
"""
LinkedIn职位检测增强模块
重构后的职位检测系统，提供更准确和可靠的职位计数和加载模式检测
"""

import time
import random
import re
from typing import List, Dict, Tuple, Optional, Union
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import logging

logger = logging.getLogger(__name__)

class LinkedInJobDetector:
    """
    LinkedIn职位检测器 - 增强版
    提供准确的职位计数、加载模式检测和智能滚动策略
    """
    
    def __init__(self, driver):
        self.driver = driver
        self.wait = WebDriverWait(driver, 10)
        self._latest_annotated_html = None
        
        # 职位卡片选择器配置（按优先级排序）
        self.job_card_selectors = [
            # 主要选择器（高优先级）
            "[data-job-id]",  # 最可靠的选择器
            ".job-card-container",
            ".jobs-search-results__list-item",
            ".base-search-card",
            
            # 备用选择器（中优先级）
            ".job-card-list__item",
            ".jobs-search-result-item",
            ".scaffold-layout__list-item",
            
            # 新版本选择器（低优先级但重要）
            ".job-card-job-posting-card-wrapper",
            "[data-entity-urn*='job']",
            "[class*='job-card']",
            ".artdeco-entity-lockup"
        ]
        
        # 滚动容器选择器
        self.scroll_container_selectors = [
            ".scaffold-layout__list-container",
            ".jobs-search-results-list",
            ".jobs-search-results__list",
            ".jobs-search__results-list",
            ".scaffold-layout__list > div",
            "#main > div > div.scaffold-layout__list > div",
            ".scaffold-layout__list"
        ]
        
        # 推荐区域检测关键词
        self.recommendation_keywords = [
            "Top job picks for you",
            "Based on your profile",
            "Your job alert",
            "Remote opportunities",
            "Hybrid opportunities",
            "Because you expressed interest",
            "More jobs for you",
            "Recommended for you"
        ]
    
    def get_accurate_job_count(self) -> Dict[str, Union[int, str]]:
        """
        获取准确的职位数量
        
        Returns:
            Dict: 包含职位数量、使用的选择器和检测详情
        """
        try:
            logger.info("🔍 开始准确职位计数检测...")
            
            results = {
                'count': 0,
                'selector_used': None,
                'all_selector_results': {},
                'detection_method': 'element_based',
                'confidence': 'low'
            }
            
            # 方法1: 基于元素的检测
            element_results = self._count_by_elements()
            results['all_selector_results'] = element_results
            
            # 方法2: 基于页面源码的检测（备用）
            source_count = self._count_by_page_source()
            
            # 方法3: 基于可见性的智能检测
            visible_results = self._count_visible_jobs()
            
            # 选择最佳结果
            best_result = self._select_best_count_result(
                element_results, source_count, visible_results
            )
            
            results.update(best_result)
            
            logger.info(f"✅ 职位计数完成: {results['count']} 个职位 (置信度: {results['confidence']})")
            return results
            
        except Exception as e:
            logger.error(f"❌ 职位计数检测失败: {e}")
            return {
                'count': 0,
                'selector_used': None,
                'all_selector_results': {},
                'detection_method': 'failed',
                'confidence': 'none',
                'error': str(e)
            }
    
    def _count_by_elements(self) -> Dict[str, int]:
        """基于DOM元素的职位计数"""
        results = {}
        
        for selector in self.job_card_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                visible_count = sum(1 for elem in elements if self._is_truly_visible(elem))
                results[selector] = visible_count
                
                logger.debug(f"选择器 {selector}: {len(elements)} 总数, {visible_count} 可见")
                
            except Exception as e:
                logger.debug(f"选择器 {selector} 失败: {e}")
                results[selector] = 0
        
        return results
    
    def _count_by_page_source(self) -> int:
        """基于页面源码的职位计数（备用方法）"""
        try:
            page_source = self.driver.page_source
            
            # 统计 data-job-id 属性出现次数
            job_id_pattern = r'data-job-id="[^"]+"'
            matches = re.findall(job_id_pattern, page_source)
            
            # 去重（同一个职位可能有多个元素包含data-job-id）
            unique_job_ids = set()
            for match in matches:
                job_id = re.search(r'data-job-id="([^"]+)"', match)
                if job_id:
                    unique_job_ids.add(job_id.group(1))
            
            count = len(unique_job_ids)
            logger.debug(f"页面源码检测到 {count} 个唯一职位ID")
            return count
            
        except Exception as e:
            logger.debug(f"页面源码计数失败: {e}")
            return 0
    
    def _count_visible_jobs(self) -> Dict[str, int]:
        """基于可见性的智能职位计数"""
        try:
            # 滚动到页面顶部确保完整视图
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(1)
            
            # 使用多种策略检测可见职位
            strategies = {
                'viewport_intersection': self._count_viewport_intersecting_jobs(),
                'display_check': self._count_displayed_jobs(),
                'position_check': self._count_positioned_jobs()
            }
            
            return strategies
            
        except Exception as e:
            logger.debug(f"可见性检测失败: {e}")
            return {}
    
    def _count_viewport_intersecting_jobs(self) -> int:
        """计算与视口相交的职位数量"""
        try:
            # 使用JavaScript检测视口相交
            script = """
            const selectors = arguments[0];
            let maxCount = 0;
            
            for (const selector of selectors) {
                const elements = document.querySelectorAll(selector);
                let visibleCount = 0;
                
                elements.forEach(el => {
                    const rect = el.getBoundingClientRect();
                    const isVisible = rect.top < window.innerHeight && 
                                    rect.bottom > 0 && 
                                    rect.left < window.innerWidth && 
                                    rect.right > 0 &&
                                    window.getComputedStyle(el).display !== 'none' &&
                                    window.getComputedStyle(el).visibility !== 'hidden';
                    if (isVisible) visibleCount++;
                });
                
                maxCount = Math.max(maxCount, visibleCount);
            }
            
            return maxCount;
            """
            
            count = self.driver.execute_script(script, self.job_card_selectors)
            return count if isinstance(count, int) else 0
            
        except Exception as e:
            logger.debug(f"视口相交检测失败: {e}")
            return 0
    
    def _count_displayed_jobs(self) -> int:
        """计算显示状态的职位数量"""
        max_count = 0
        
        for selector in self.job_card_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                displayed_count = sum(1 for elem in elements if elem.is_displayed())
                max_count = max(max_count, displayed_count)
            except Exception:
                continue
        
        return max_count
    
    def _count_positioned_jobs(self) -> int:
        """计算有有效位置的职位数量"""
        try:
            script = """
            const selectors = arguments[0];
            let maxCount = 0;
            
            for (const selector of selectors) {
                const elements = document.querySelectorAll(selector);
                let validCount = 0;
                
                elements.forEach(el => {
                    const rect = el.getBoundingClientRect();
                    if (rect.width > 0 && rect.height > 0) {
                        validCount++;
                    }
                });
                
                maxCount = Math.max(maxCount, validCount);
            }
            
            return maxCount;
            """
            
            count = self.driver.execute_script(script, self.job_card_selectors)
            return count if isinstance(count, int) else 0
            
        except Exception as e:
            logger.debug(f"位置检测失败: {e}")
            return 0
    
    def _is_truly_visible(self, element) -> bool:
        """检查元素是否真正可见"""
        try:
            if not element.is_displayed():
                return False
            
            # 检查元素尺寸
            size = element.size
            if size['width'] <= 0 or size['height'] <= 0:
                return False
            
            # 检查元素位置
            location = element.location
            if location['x'] < -1000 or location['y'] < -1000:
                return False
            
            return True
            
        except Exception:
            return False
    
    def _select_best_count_result(self, element_results: Dict, source_count: int, 
                                visible_results: Dict) -> Dict:
        """选择最佳的计数结果"""
        # 获取所有计数结果
        all_counts = []
        
        # 添加元素检测结果
        for selector, count in element_results.items():
            if count > 0:
                all_counts.append((count, selector, 'element'))
        
        # 添加源码检测结果
        if source_count > 0:
            all_counts.append((source_count, 'page_source', 'source'))
        
        # 添加可见性检测结果
        for method, count in visible_results.items():
            if count > 0:
                all_counts.append((count, method, 'visibility'))
        
        if not all_counts:
            return {
                'count': 0,
                'selector_used': None,
                'detection_method': 'none',
                'confidence': 'none'
            }
        
        # 排序：优先选择合理范围内的最大值
        all_counts.sort(key=lambda x: x[0], reverse=True)
        
        # LinkedIn通常每页显示20-25个职位
        # 选择策略：
        # 1. 如果有20-30范围内的结果，选择最大的
        # 2. 否则选择所有结果中的最大值
        # 3. 如果最大值过大（>50），选择较小但合理的值
        
        reasonable_counts = [x for x in all_counts if 20 <= x[0] <= 30]
        if reasonable_counts:
            best = reasonable_counts[0]
            confidence = 'high'
        else:
            # 选择最大值，但检查是否合理
            best = all_counts[0]
            if best[0] > 50:
                # 寻找更合理的值
                moderate_counts = [x for x in all_counts if 10 <= x[0] <= 50]
                if moderate_counts:
                    best = moderate_counts[0]
                    confidence = 'medium'
                else:
                    confidence = 'low'
            elif best[0] >= 10:
                confidence = 'medium'
            else:
                confidence = 'low'
        
        return {
            'count': best[0],
            'selector_used': best[1],
            'detection_method': best[2],
            'confidence': confidence
        }
    
    def detect_loading_mode(self) -> Dict[str, Union[bool, str, int]]:
        """
        检测LinkedIn页面的加载模式
        
        Returns:
            Dict: 包含加载模式检测结果的详细信息
        """
        try:
            logger.info("🔍 开始智能加载模式检测...")
            
            # 等待页面稳定
            time.sleep(2)
            
            # 获取初始状态（不生成带注释HTML，因为第一页不需要）
            initial_state = self._capture_page_state(update_html_cache=False)
            logger.info(f"📊 初始状态: {initial_state['job_count']} 个职位")
            
            # 执行滚动测试
            scroll_result = self._perform_scroll_test()
            
            # 获取滚动后状态（不生成HTML缓存）
            after_state = self._capture_page_state(update_html_cache=False)
            logger.info(f"📊 滚动后状态: {after_state['job_count']} 个职位")
            
            # 分析结果
            analysis = self._analyze_loading_mode(
                initial_state, after_state, scroll_result
            )
            
            logger.info(f"✅ 加载模式检测完成: {analysis['mode']} (置信度: {analysis['confidence']})")
            return analysis
            
        except Exception as e:
            logger.error(f"❌ 加载模式检测失败: {e}")
            return {
                'mode': 'dynamic',  # 默认动态模式
                'confidence': 'none',
                'reason': f'检测失败: {e}',
                'is_static': False
            }
    
    def _capture_page_state(self, update_html_cache: bool = True, generate_annotations: bool = False) -> Dict:
        """捕获页面当前状态"""
        job_count_result = self.get_accurate_job_count()
        
        # 只在需要时更新带注释的HTML缓存（通常只在最后一页）
        if update_html_cache and generate_annotations:
            try:
                # 直接调用get_annotated_html_snapshot会自动更新缓存
                self.get_annotated_html_snapshot()
                logger.info("📝 已生成带注释的HTML快照（用于区分推荐职位）")
            except Exception as e:
                logger.debug(f"⚠️ 更新带注释HTML缓存失败: {e}")
        
        return {
            'job_count': job_count_result['count'],
            'confidence': job_count_result['confidence'],
            'html_size': len(self.driver.page_source),
            'scroll_height': self.driver.execute_script("return document.body.scrollHeight"),
            'timestamp': time.time()
        }
    
    def _perform_scroll_test(self) -> Dict:
        """执行滚动测试"""
        try:
            # 记录初始滚动位置
            initial_scroll = self.driver.execute_script("return window.pageYOffset")
            
            # 执行滚动
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)  # 等待可能的动态加载
            
            # 检查是否真的滚动了
            final_scroll = self.driver.execute_script("return window.pageYOffset")
            
            return {
                'scrolled': final_scroll > initial_scroll,
                'scroll_distance': final_scroll - initial_scroll,
                'wait_time': 3
            }
            
        except Exception as e:
            logger.debug(f"滚动测试失败: {e}")
            return {'scrolled': False, 'scroll_distance': 0, 'wait_time': 0}
    
    def _analyze_loading_mode(self, initial_state: Dict, after_state: Dict, 
                            scroll_result: Dict) -> Dict:
        """分析加载模式"""
        job_increase = after_state['job_count'] > initial_state['job_count']
        html_increase = after_state['html_size'] > initial_state['html_size'] * 1.1
        
        # 计算变化量
        job_change = after_state['job_count'] - initial_state['job_count']
        html_change_percent = ((after_state['html_size'] - initial_state['html_size']) / 
                             initial_state['html_size']) * 100
        
        # 判断逻辑 - 修复LinkedIn加载模式检测
        if job_increase and job_change >= 3:
            # 职位数量显著增加（降低阈值，因为LinkedIn每次可能只加载几个职位）
            mode = 'dynamic'
            confidence = 'high'
            reason = f'滚动后职位增加了 {job_change} 个'
        elif html_increase and html_change_percent > 15:
            # HTML大小显著增加（降低阈值）
            mode = 'dynamic'
            confidence = 'medium'
            reason = f'HTML大小增加了 {html_change_percent:.1f}%'
        elif initial_state['job_count'] >= 50:
            # 只有当初始职位数非常多时才判断为静态（提高阈值）
            # LinkedIn第一页通常只有7个职位，不应该判断为静态
            mode = 'static'
            confidence = 'medium'
            reason = f'初始页面已包含 {initial_state["job_count"]} 个职位，疑似静态加载'
        else:
            # 默认判断为动态 - LinkedIn通常是动态加载
            mode = 'dynamic'
            confidence = 'medium'
            reason = f'LinkedIn通常为动态加载模式，初始职位数: {initial_state["job_count"]}'
        
        return {
            'mode': mode,
            'is_static': mode == 'static',
            'confidence': confidence,
            'reason': reason,
            'job_change': job_change,
            'html_change_percent': html_change_percent,
            'initial_jobs': initial_state['job_count'],
            'final_jobs': after_state['job_count'],
            'scroll_performed': scroll_result['scrolled']
        }
    
    def smart_scroll_jobs(self, max_jobs: int = 100, loading_analysis: Dict = None) -> Dict:
        """
        智能滚动职位列表
        
        Args:
            max_jobs: 最大职位数量限制
            loading_analysis: 可选的加载模式分析结果，如果提供则跳过重复检测
            
        Returns:
            Dict: 滚动结果和统计信息
        """
        try:
            logger.info(f"🔄 开始智能滚动，目标最大职位数: {max_jobs}")
            
            # 如果没有提供加载模式分析，则进行检测
            if loading_analysis is None:
                loading_analysis = self.detect_loading_mode()
            
            if loading_analysis.get('is_static', False):
                logger.info("📄 检测到静态加载模式，跳过滚动")
                return {
                    'success': True,
                    'mode': 'static',
                    'final_job_count': loading_analysis.get('initial_jobs', 0),
                    'scroll_attempts': 0,
                    'reason': '静态加载模式'
                }
            
            # 动态加载模式，执行智能滚动
            return self._perform_smart_scroll(max_jobs)
            
        except Exception as e:
            logger.error(f"❌ 智能滚动失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'final_job_count': 0,
                'scroll_attempts': 0
            }
    
    def _perform_smart_scroll(self, max_jobs: int) -> Dict:
        """执行智能渐进式滚动"""
        logger.info("🔄 执行动态加载模式渐进式滚动")
        
        # 查找滚动容器
        scroll_container = self._find_scroll_container()
        
        # 滚动参数
        max_scroll_steps = 100  # 最大滚动步数
        scroll_step_size = 600  # 每次滚动像素
        scroll_steps = 0
        last_job_count = 0
        stable_count = 0
        jobs_loaded_in_batch = 0
        batch_size = 7  # 期望每批加载的职位数
        
        # 获取初始职位数量
        initial_count_result = self.get_accurate_job_count()
        initial_job_count = initial_count_result['count']
        last_job_count = initial_job_count
        
        logger.info(f"📊 初始职位数: {initial_job_count}，目标: {max_jobs}")
        
        while scroll_steps < max_scroll_steps:
            # 检查是否达到目标
            current_count_result = self.get_accurate_job_count()
            current_job_count = current_count_result['count']
            
            if current_job_count >= max_jobs:
                logger.info(f"✅ 达到目标职位数 {max_jobs}，停止滚动")
                break
            
            # 检查是否进入推荐区域
            if self._check_recommendation_section():
                logger.info("🚫 检测到推荐职位区域，停止滚动")
                break
            
            # 检查分页控件
            if self._check_pagination_visible():
                logger.info("✅ 检测到分页控件，停止滚动")
                break
            
            # 执行渐进式滚动
            logger.info(f"📜 执行滚动步骤 {scroll_steps + 1}/{max_scroll_steps}")
            scroll_success = self._scroll_container(scroll_container, scroll_step_size)
            if not scroll_success:
                logger.info("📜 已滚动到页面底部或滚动失败，停止滚动")
                break
            
            # 短暂等待让页面加载
            time.sleep(random.uniform(1.5, 2.0))
            
            # 检查滚动后的职位数量变化
            after_scroll_result = self.get_accurate_job_count()
            after_scroll_count = after_scroll_result['count']
            
            # 计算本次滚动新增的职位数
            new_jobs = after_scroll_count - current_job_count
            if new_jobs > 0:
                jobs_loaded_in_batch += new_jobs
                logger.info(f"📈 滚动步骤 {scroll_steps + 1}: 新增 {new_jobs} 个职位，当前总数: {after_scroll_count}")
                stable_count = 0  # 重置稳定计数
            else:
                logger.info(f"📊 滚动步骤 {scroll_steps + 1}: 无新增职位，当前总数: {after_scroll_count}")
            
            # 更新last_job_count为滚动后的数量
            last_job_count = after_scroll_count
            
            # 检查职位数量变化
            if new_jobs == 0:
                stable_count += 1
                if stable_count >= 5:  # 连续5次无变化
                    logger.info("✅ 职位数量长时间稳定，可能已加载完毕")
                    break
            
            scroll_steps += 1
            
            # 每隔一定步数检查是否需要调整滚动策略
            if scroll_steps % 10 == 0:
                logger.info(f"📊 滚动进度: {scroll_steps}/{max_scroll_steps} 步，当前职位: {after_scroll_count}")
        
        # 最终统计
        final_count_result = self.get_accurate_job_count()
        final_job_count = final_count_result['count']
        total_loaded = final_job_count - initial_job_count
        
        logger.info(f"📊 滚动完成: 初始 {initial_job_count} -> 最终 {final_job_count} (新增 {total_loaded})")
        
        return {
            'success': True,
            'mode': 'dynamic_progressive',
            'initial_job_count': initial_job_count,
            'final_job_count': final_job_count,
            'jobs_loaded': total_loaded,
            'scroll_steps': scroll_steps,
            'max_steps_reached': scroll_steps >= max_scroll_steps,
            'final_count_confidence': final_count_result['confidence'],
            'scroll_step_size': scroll_step_size
        }
    
    def _find_scroll_container(self):
        """查找可滚动的容器"""
        logger.info("🔍 开始查找滚动容器...")
        
        for selector in self.scroll_container_selectors:
            try:
                container = self.driver.find_element(By.CSS_SELECTOR, selector)
                if container.is_displayed():
                    # 检查容器是否可滚动
                    scroll_height = self.driver.execute_script(
                        "return arguments[0].scrollHeight", container
                    )
                    client_height = self.driver.execute_script(
                        "return arguments[0].clientHeight", container
                    )
                    
                    if scroll_height > client_height:
                        logger.info(f"✅ 找到可滚动容器: {selector} (高度: {scroll_height}px, 可见: {client_height}px)")
                        return container
                    else:
                        logger.debug(f"📏 容器 {selector} 不可滚动 (高度: {scroll_height}px, 可见: {client_height}px)")
                else:
                    logger.debug(f"👁️ 容器 {selector} 不可见")
            except Exception as e:
                logger.debug(f"❌ 查找容器 {selector} 失败: {e}")
                continue
        
        logger.warning("⚠️ 未找到专用滚动容器，使用页面滚动")
        return None
    
    def _scroll_container(self, container, scroll_step: int = 800) -> bool:
        """渐进式滚动容器或页面
        
        Args:
            container: 滚动容器元素
            scroll_step: 每次滚动的像素距离
            
        Returns:
            bool: 滚动是否成功
        """
        try:
            if container:
                # 获取当前滚动位置和容器高度
                current_scroll = self.driver.execute_script(
                    "return arguments[0].scrollTop", container
                )
                scroll_height = self.driver.execute_script(
                    "return arguments[0].scrollHeight", container
                )
                client_height = self.driver.execute_script(
                    "return arguments[0].clientHeight", container
                )
                
                # 计算新的滚动位置（渐进式滚动）
                new_scroll_position = min(current_scroll + scroll_step, scroll_height - client_height)
                
                # 执行渐进式滚动
                self.driver.execute_script(
                    "arguments[0].scrollTop = arguments[1]", 
                    container, new_scroll_position
                )
                
                logger.debug(f"📜 容器滚动: {current_scroll} -> {new_scroll_position} (步长: {scroll_step})")
                
                # 检查是否已滚动到底部
                if new_scroll_position >= scroll_height - client_height:
                    logger.debug("📜 容器已滚动到底部")
                    return False  # 表示已到底部，无需继续滚动
                    
            else:
                # 渐进式滚动整个页面
                current_scroll = self.driver.execute_script("return window.pageYOffset")
                page_height = self.driver.execute_script("return document.body.scrollHeight")
                window_height = self.driver.execute_script("return window.innerHeight")
                
                logger.info(f"📜 页面滚动信息: 当前位置={current_scroll}px, 页面高度={page_height}px, 窗口高度={window_height}px")
                
                # 计算新的滚动位置
                new_scroll_position = min(current_scroll + scroll_step, page_height - window_height)
                
                # 执行渐进式滚动
                self.driver.execute_script(f"window.scrollTo(0, {new_scroll_position});")
                
                logger.info(f"📜 页面滚动: {current_scroll} -> {new_scroll_position} (步长: {scroll_step})")
                
                # 验证滚动是否成功
                actual_scroll = self.driver.execute_script("return window.pageYOffset")
                logger.info(f"📜 滚动验证: 期望位置={new_scroll_position}px, 实际位置={actual_scroll}px")
                
                # 检查是否已滚动到底部
                if new_scroll_position >= page_height - window_height:
                    logger.info("📜 页面已滚动到底部")
                    return False  # 表示已到底部，无需继续滚动
                    
            return True  # 成功滚动且未到底部
            
        except Exception as e:
            logger.debug(f"滚动失败: {e}")
            return False
    
    def _check_recommendation_section(self) -> bool:
        """检查是否到达推荐职位区域"""
        try:
            page_source = self.driver.page_source.lower()
            
            for keyword in self.recommendation_keywords:
                if keyword.lower() in page_source:
                    # 进一步检查关键词是否在当前视口中
                    try:
                        elements = self.driver.find_elements(
                            By.XPATH, 
                            f"//*[contains(text(), '{keyword}')]"
                        )
                        for element in elements:
                            if element.is_displayed():
                                location = element.location
                                window_height = self.driver.execute_script(
                                    "return window.innerHeight"
                                )
                                scroll_y = self.driver.execute_script(
                                    "return window.pageYOffset"
                                )
                                
                                # 检查元素是否在当前视口中
                                if (scroll_y <= location['y'] <= 
                                    scroll_y + window_height):
                                    logger.info(f"🚫 检测到推荐区关键词: {keyword}")
                                    return True
                    except Exception:
                        continue
            
            return False
            
        except Exception as e:
            logger.debug(f"推荐区检测失败: {e}")
            return False
    
    def _check_pagination_visible(self) -> bool:
        """检查分页控件是否可见"""
        pagination_selectors = [
            ".artdeco-pagination",
            ".jobs-search-pagination",
            "[aria-label*='pagination']",
            ".pagination",
            "[data-test-pagination]"
        ]
        
        for selector in pagination_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    if element.is_displayed():
                        logger.info(f"✅ 检测到分页控件: {selector}")
                        return True
            except Exception:
                continue
        
        return False
    
    def get_job_extraction_strategy(self) -> str:
        """
        根据检测结果推荐职位提取策略
        
        Returns:
            str: 推荐的提取策略 ('static_snapshot', 'dynamic_scroll', 'hybrid')
        """
        try:
            loading_analysis = self.detect_loading_mode()
            job_count = loading_analysis['initial_jobs']
            
            if loading_analysis['is_static'] and job_count >= 20:
                return 'static_snapshot'
            elif loading_analysis['confidence'] == 'high':
                return 'dynamic_scroll' if not loading_analysis['is_static'] else 'static_snapshot'
            else:
                return 'hybrid'
                
        except Exception:
            return 'hybrid'  # 默认混合策略
    
    def generate_detection_report(self) -> Dict:
        """
        生成详细的检测报告
        
        Returns:
            Dict: 包含所有检测结果的详细报告
        """
        try:
            logger.info("📋 生成LinkedIn职位检测报告...")
            
            # 职位计数检测
            job_count_result = self.get_accurate_job_count()
            
            # 加载模式检测
            loading_mode_result = self.detect_loading_mode()
            
            # 推荐策略
            recommended_strategy = self.get_job_extraction_strategy()
            
            # 页面信息
            page_info = {
                'url': self.driver.current_url,
                'title': self.driver.title,
                'page_source_size': len(self.driver.page_source),
                'viewport_size': self.driver.get_window_size(),
                'timestamp': time.time()
            }
            
            report = {
                'page_info': page_info,
                'job_count_detection': job_count_result,
                'loading_mode_detection': loading_mode_result,
                'recommended_strategy': recommended_strategy,
                'summary': {
                    'total_jobs_detected': job_count_result['count'],
                    'loading_mode': loading_mode_result['mode'],
                    'confidence_level': min(
                        job_count_result.get('confidence', 'low'),
                        loading_mode_result.get('confidence', 'low')
                    ),
                    'ready_for_extraction': (
                        job_count_result['count'] > 0 and 
                        loading_mode_result['confidence'] != 'none'
                    )
                }
            }
            
            logger.info("✅ 检测报告生成完成")
            return report
            
        except Exception as e:
            logger.error(f"❌ 生成检测报告失败: {e}")
            return {
                'error': str(e),
                'summary': {
                    'total_jobs_detected': 0,
                    'loading_mode': 'unknown',
                    'confidence_level': 'none',
                    'ready_for_extraction': False
                }
            }
    
    def get_annotated_html_snapshot(self) -> Optional[str]:
        """
        获取带注释的HTML快照
        在HTML中插入特殊注释标记，帮助LLM区分主要职位和推荐职位
        
        Returns:
            Optional[str]: 带注释的HTML内容，如果生成失败则返回None
        """
        try:
            logger.info("📝 生成带注释的HTML快照...")
            
            # 获取当前页面HTML
            html = self.driver.page_source
            
            if not html:
                logger.warning("页面HTML为空，无法生成注释")
                return None
            
            # 插入注释标记
            annotated_html = self._insert_annotation_markers(html)
            
            # 更新内部缓存
            self._latest_annotated_html = annotated_html
            
            logger.info(f"✅ 带注释HTML生成完成，长度: {len(annotated_html)} 字符")
            return annotated_html
            
        except Exception as e:
            logger.error(f"❌ 生成带注释HTML失败: {e}")
            return None
    
    def _insert_annotation_markers(self, html: str) -> str:
        """
        在HTML中插入注释标记
        
        Args:
            html: 原始HTML内容
            
        Returns:
            str: 带注释标记的HTML内容
        """
        try:
            import re
            annotated_html = html
            
            # 1. 标记主要职位列表容器
            main_list_patterns = [
                # 主要搜索结果容器
                r'(<(ul|div)[^>]*class=["\']?[^"\'>]*scaffold-layout__list[^"\'>]*["\']?[^>]*>)',
                r'(<(ul|div)[^>]*class=["\']?[^"\'>]*jobs-search-results-list[^"\'>]*["\']?[^>]*>)',
                r'(<(ul|div)[^>]*class=["\']?[^"\'>]*jobs-search-results__list[^"\'>]*["\']?[^>]*>)',
                r'(<(ul|div)[^>]*class=["\']?[^"\'>]*jobs-search__results-list[^"\'>]*["\']?[^>]*>)'
            ]
            
            for pattern in main_list_patterns:
                annotated_html = re.sub(
                    pattern,
                    r'<!-- MAIN_JOB_LIST_START -->\1',
                    annotated_html,
                    flags=re.IGNORECASE
                )
            
            # 标记主要职位列表结束
            annotated_html = re.sub(
                r'(</(ul|div)>)',
                r'\1<!-- MAIN_JOB_LIST_END -->',
                annotated_html,
                count=1,
                flags=re.IGNORECASE
            )
            
            # 2. 标记推荐职位区域
            rec_patterns = [
                r'(<(div|section)[^>]*class=["\']?[^"\'>]*job-recommendations[^"\'>]*["\']?[^>]*>)',
                r'(<(div|section)[^>]*class=["\']?[^"\'>]*recommendations[^"\'>]*["\']?[^>]*>)',
                r'(<(div|section)[^>]*class=["\']?[^"\'>]*job-picks[^"\'>]*["\']?[^>]*>)',
                r'(<(div|section)[^>]*data-test-id=["\']?recommendations["\']?[^>]*>)'
            ]
            
            for pattern in rec_patterns:
                annotated_html = re.sub(
                    pattern,
                    r'<!-- RECOMMENDATION_SECTION_START -->\1',
                    annotated_html,
                    flags=re.IGNORECASE
                )
            
            # 3. 基于关键词标记推荐区域
            for keyword in self.recommendation_keywords:
                # 查找包含推荐关键词的文本，并在其前面插入标记
                keyword_pattern = f"(<[^>]*?>)({re.escape(keyword)})"
                annotated_html = re.sub(
                    keyword_pattern,
                    r'<!-- RECOMMENDATION_SECTION_START -->\1\2',
                    annotated_html,
                    flags=re.IGNORECASE
                )
            
            # 4. 标记推荐区域结束（在页面底部）
            annotated_html = re.sub(
                r'(</body>|</html>)',
                r'<!-- RECOMMENDATION_SECTION_END -->\1',
                annotated_html,
                flags=re.IGNORECASE
            )
            
            # 5. 标记个别职位卡片
            job_card_pattern = r'(<[^>]*data-job-id=["\']?[^"\'>]+["\']?[^>]*>)'
            annotated_html = re.sub(
                job_card_pattern,
                r'<!-- JOB_CARD_START -->\1',
                annotated_html,
                flags=re.IGNORECASE
            )
            
            logger.info("✅ HTML注释标记插入完成")
            return annotated_html
            
        except Exception as e:
            logger.error(f"❌ 插入注释标记失败: {e}")
            return html  # 返回原始HTML作为回退
    
    def get_latest_annotated_html(self) -> Optional[str]:
        """
        获取最新的带注释HTML（从缓存）
        
        Returns:
            Optional[str]: 最新的带注释HTML，如果没有则返回None
        """
        return self._latest_annotated_html
    
    def clear_annotated_html_cache(self):
        """
        清除带注释HTML缓存
        """
        self._latest_annotated_html = None
    
    def smart_scroll_and_load(self) -> bool:
        """
        智能滚动和加载职位
        
        Returns:
            bool: 滚动是否成功
        """
        try:
            logger.info("🔄 开始智能滚动和加载...")
            
            # 检测加载模式
            mode_result = self.detect_loading_mode()
            
            if mode_result.get('is_static', False):
                logger.info("📄 检测到静态模式，无需滚动")
                return True
            
            # 动态模式下执行渐进式滚动
            logger.info("🔄 检测到动态模式，开始渐进式滚动加载")
            
            # 调用智能滚动方法
            scroll_result = self.smart_scroll_jobs(max_jobs=100, loading_analysis=mode_result)
            
            if scroll_result['success']:
                logger.info(f"✅ 智能滚动完成: 加载了 {scroll_result.get('jobs_loaded', 0)} 个新职位")
                return True
            else:
                logger.warning(f"⚠️ 智能滚动未完全成功: {scroll_result.get('error', '未知错误')}")
                return False
            
        except Exception as e:
             logger.error(f"❌ 智能滚动失败: {e}")
             return False