# -*- coding: utf-8 -*-
"""
公司分析器模块
基于MCP的智能公司信息分析和文化匹配评估
提供公司背景调研、文化价值观分析、面试流程预测等功能
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass, field, asdict
from enum import Enum
from pathlib import Path
import logging

# 尝试导入MCP管理器
try:
    from ..core.mcp_manager import MCPManager, MCPToolCall, MCPToolResult
except ImportError:
    # 创建模拟类以保持兼容性
    @dataclass
    class MCPToolCall:
        tool_name: str
        server_name: str
        arguments: Dict[str, Any]
    
    @dataclass
    class MCPToolResult:
        success: bool
        data: Dict[str, Any]
        error: Optional[str] = None
        execution_time: float = 0.0
    
    class MCPManager:
        def __init__(self):
            pass
        
        async def call_tool(self, call: MCPToolCall) -> MCPToolResult:
            return MCPToolResult(success=False, data={}, error="MCP Manager not available")
        
        def get_mcp_manager():
            return None

# 导入其他分析器（可选）
try:
    from .job_analyzer import JobAnalysisResult
except ImportError:
    @dataclass
    class JobAnalysisResult:
        job_title: str = ""
        required_skills: List = field(default_factory=list)
        responsibilities: List[str] = field(default_factory=list)
        company_name: str = ""
        experience_requirements: str = ""
        industry: str = ""

# 导入现有模块（保持兼容性）
try:
    from src.utils.logger import logger
except ImportError:
    logger = logging.getLogger(__name__)


class CompanySize(Enum):
    """公司规模"""
    STARTUP = "startup"  # 初创公司 (<50人)
    SMALL = "small"  # 小公司 (50-200人)
    MEDIUM = "medium"  # 中等公司 (200-1000人)
    LARGE = "large"  # 大公司 (1000-10000人)
    ENTERPRISE = "enterprise"  # 企业级 (>10000人)
    UNKNOWN = "unknown"


class CompanyType(Enum):
    """公司类型"""
    TECH = "tech"  # 科技公司
    FINANCE = "finance"  # 金融公司
    HEALTHCARE = "healthcare"  # 医疗健康
    EDUCATION = "education"  # 教育
    RETAIL = "retail"  # 零售
    MANUFACTURING = "manufacturing"  # 制造业
    CONSULTING = "consulting"  # 咨询
    GOVERNMENT = "government"  # 政府机构
    NONPROFIT = "nonprofit"  # 非营利组织
    OTHER = "other"


class CultureValue(Enum):
    """文化价值观"""
    INNOVATION = "innovation"  # 创新
    COLLABORATION = "collaboration"  # 协作
    DIVERSITY = "diversity"  # 多样性
    WORK_LIFE_BALANCE = "work_life_balance"  # 工作生活平衡
    GROWTH = "growth"  # 成长
    EXCELLENCE = "excellence"  # 卓越
    INTEGRITY = "integrity"  # 诚信
    CUSTOMER_FOCUS = "customer_focus"  # 客户导向
    AGILITY = "agility"  # 敏捷
    SUSTAINABILITY = "sustainability"  # 可持续发展


@dataclass
class CompanyFinancials:
    """公司财务信息"""
    revenue: Optional[str] = None  # 年收入
    funding_stage: Optional[str] = None  # 融资阶段
    valuation: Optional[str] = None  # 估值
    growth_rate: Optional[float] = None  # 增长率
    profitability: Optional[bool] = None  # 盈利性


@dataclass
class CompanyBenefits:
    """公司福利"""
    health_insurance: bool = False
    dental_insurance: bool = False
    vision_insurance: bool = False
    retirement_plan: bool = False
    paid_time_off: Optional[str] = None
    flexible_schedule: bool = False
    remote_work: bool = False
    professional_development: bool = False
    gym_membership: bool = False
    free_meals: bool = False
    stock_options: bool = False
    bonus_structure: Optional[str] = None


@dataclass
class InterviewProcess:
    """面试流程"""
    total_rounds: int = 0
    typical_duration: Optional[str] = None  # 总时长
    rounds: List[Dict[str, Any]] = field(default_factory=list)
    decision_timeline: Optional[str] = None  # 决策时间
    feedback_provided: bool = False
    

@dataclass
class CompanyAnalysisResult:
    """公司分析结果"""
    # 基本信息
    company_name: str
    industry: str
    company_size: CompanySize
    company_type: CompanyType
    founded_year: Optional[int] = None
    headquarters: Optional[str] = None
    website: Optional[str] = None
    
    # 文化和价值观
    culture_values: List[CultureValue] = field(default_factory=list)
    culture_description: str = ""
    work_environment: str = ""
    
    # 面试流程
    interview_process: InterviewProcess = field(default_factory=InterviewProcess)
    
    # 财务和福利
    financials: CompanyFinancials = field(default_factory=CompanyFinancials)
    benefits: CompanyBenefits = field(default_factory=CompanyBenefits)
    
    # 员工信息
    employee_count: Optional[int] = None
    employee_satisfaction: Optional[float] = None  # 员工满意度 (0-5)
    glassdoor_rating: Optional[float] = None
    
    # 技术栈和工具
    tech_stack: List[str] = field(default_factory=list)
    development_practices: List[str] = field(default_factory=list)
    
    # 分析元数据
    analysis_confidence: float = 0.0  # 分析置信度 (0-1)
    data_sources: List[str] = field(default_factory=list)
    last_updated: Optional[str] = None
    analysis_notes: str = ""


class CompanyAnalyzer:
    """公司分析器"""
    
    def __init__(self, mcp_manager: Optional[MCPManager] = None):
        self.mcp_manager = mcp_manager or MCPManager()
        self.logger = logger
        
    async def analyze_company(self, 
                            company_name: str,
                            job_posting_url: Optional[str] = None,
                            additional_context: Optional[Dict[str, Any]] = None) -> CompanyAnalysisResult:
        """
        分析公司信息
        
        Args:
            company_name: 公司名称
            job_posting_url: 职位发布URL（可选）
            additional_context: 额外上下文信息
            
        Returns:
            CompanyAnalysisResult: 公司分析结果
        """
        try:
            self.logger.info(f"开始分析公司: {company_name}")
            
            # 1. 获取公司基本信息
            basic_info = await self._get_company_basic_info(company_name)
            
            # 2. 分析公司文化
            culture_info = await self._analyze_company_culture(company_name, job_posting_url)
            
            # 3. 获取面试流程信息
            interview_info = await self._get_interview_process(company_name)
            
            # 4. 获取员工反馈
            employee_feedback = await self._get_employee_feedback(company_name)
            
            # 5. 分析技术栈
            tech_info = await self._analyze_tech_stack(company_name)
            
            # 6. 整合分析结果
            result = self._compile_analysis_result(
                company_name, basic_info, culture_info, 
                interview_info, employee_feedback, tech_info
            )
            
            self.logger.info(f"公司分析完成: {company_name}, 置信度: {result.analysis_confidence:.2f}")
            return result
            
        except Exception as e:
            self.logger.error(f"公司分析失败: {e}")
            return self._create_fallback_result(company_name)
    
    async def _get_company_basic_info(self, company_name: str) -> Dict[str, Any]:
        """获取公司基本信息"""
        try:
            # 使用MCP工具搜索公司信息
            search_call = MCPToolCall(
                tool_name="fetch_plaintext",
                server_name="mult-fetch-mcp-server",
                arguments={
                    "url": f"https://www.google.com/search?q={company_name}+company+information",
                    "startCursor": 0,
                    "extractContent": True
                }
            )
            
            result = await self.mcp_manager.call_tool(search_call)
            
            if result.success:
                content = result.data.get('content', '')
                return self._extract_basic_info(content, company_name)
            
        except Exception as e:
            self.logger.warning(f"获取公司基本信息失败: {e}")
        
        return {}
    
    async def _analyze_company_culture(self, company_name: str, job_url: Optional[str] = None) -> Dict[str, Any]:
        """分析公司文化"""
        culture_info = {
            'values': [],
            'description': '',
            'work_environment': ''
        }
        
        try:
            # 如果有职位URL，分析职位描述中的文化信息
            if job_url:
                job_call = MCPToolCall(
                    tool_name="fetch_plaintext",
                    server_name="mult-fetch-mcp-server",
                    arguments={
                        "url": job_url,
                        "startCursor": 0,
                        "extractContent": True
                    }
                )
                
                result = await self.mcp_manager.call_tool(job_call)
                if result.success:
                    content = result.data.get('content', '')
                    culture_info.update(self._extract_culture_from_job_posting(content))
            
            # 搜索公司文化相关信息
            culture_call = MCPToolCall(
                tool_name="fetch_plaintext",
                server_name="mult-fetch-mcp-server",
                arguments={
                    "url": f"https://www.google.com/search?q={company_name}+company+culture+values",
                    "startCursor": 0,
                    "extractContent": True
                }
            )
            
            result = await self.mcp_manager.call_tool(culture_call)
            if result.success:
                content = result.data.get('content', '')
                culture_info.update(self._extract_culture_info(content))
                
        except Exception as e:
            self.logger.warning(f"分析公司文化失败: {e}")
        
        return culture_info
    
    async def _get_interview_process(self, company_name: str) -> Dict[str, Any]:
        """获取面试流程信息"""
        try:
            # 搜索面试流程信息
            interview_call = MCPToolCall(
                tool_name="fetch_plaintext",
                server_name="mult-fetch-mcp-server",
                arguments={
                    "url": f"https://www.google.com/search?q={company_name}+interview+process+glassdoor",
                    "startCursor": 0,
                    "extractContent": True
                }
            )
            
            result = await self.mcp_manager.call_tool(interview_call)
            
            if result.success:
                content = result.data.get('content', '')
                return self._extract_interview_info(content)
                
        except Exception as e:
            self.logger.warning(f"获取面试流程信息失败: {e}")
        
        return {}
    
    async def _get_employee_feedback(self, company_name: str) -> Dict[str, Any]:
        """获取员工反馈"""
        try:
            # 搜索员工评价
            feedback_call = MCPToolCall(
                tool_name="fetch_plaintext",
                server_name="mult-fetch-mcp-server",
                arguments={
                    "url": f"https://www.google.com/search?q={company_name}+employee+reviews+glassdoor",
                    "startCursor": 0,
                    "extractContent": True
                }
            )
            
            result = await self.mcp_manager.call_tool(feedback_call)
            
            if result.success:
                content = result.data.get('content', '')
                return self._extract_employee_feedback(content)
                
        except Exception as e:
            self.logger.warning(f"获取员工反馈失败: {e}")
        
        return {}
    
    async def _analyze_tech_stack(self, company_name: str) -> Dict[str, Any]:
        """分析技术栈"""
        try:
            # 搜索技术栈信息
            tech_call = MCPToolCall(
                tool_name="fetch_plaintext",
                server_name="mult-fetch-mcp-server",
                arguments={
                    "url": f"https://www.google.com/search?q={company_name}+tech+stack+engineering",
                    "startCursor": 0,
                    "extractContent": True
                }
            )
            
            result = await self.mcp_manager.call_tool(tech_call)
            
            if result.success:
                content = result.data.get('content', '')
                return self._extract_tech_stack(content)
                
        except Exception as e:
            self.logger.warning(f"分析技术栈失败: {e}")
        
        return {}
    
    def _extract_basic_info(self, content: str, company_name: str) -> Dict[str, Any]:
        """从内容中提取基本信息"""
        info = {
            'industry': '',
            'size': CompanySize.UNKNOWN,
            'type': CompanyType.OTHER,
            'founded': None,
            'headquarters': '',
            'website': ''
        }
        
        # 简单的关键词匹配逻辑
        content_lower = content.lower()
        
        # 行业识别
        if any(word in content_lower for word in ['technology', 'software', 'tech', 'ai', 'machine learning']):
            info['type'] = CompanyType.TECH
        elif any(word in content_lower for word in ['finance', 'bank', 'financial', 'investment']):
            info['type'] = CompanyType.FINANCE
        elif any(word in content_lower for word in ['healthcare', 'medical', 'hospital', 'pharma']):
            info['type'] = CompanyType.HEALTHCARE
        
        # 公司规模识别
        if any(word in content_lower for word in ['startup', 'early stage']):
            info['size'] = CompanySize.STARTUP
        elif any(word in content_lower for word in ['small company', 'small business']):
            info['size'] = CompanySize.SMALL
        elif any(word in content_lower for word in ['enterprise', 'fortune 500', 'large corporation']):
            info['size'] = CompanySize.ENTERPRISE
        
        return info
    
    def _extract_culture_from_job_posting(self, content: str) -> Dict[str, Any]:
        """从职位描述中提取文化信息"""
        culture = {
            'values': [],
            'description': '',
            'work_environment': ''
        }
        
        content_lower = content.lower()
        
        # 文化价值观识别
        if 'innovation' in content_lower or 'innovative' in content_lower:
            culture['values'].append(CultureValue.INNOVATION)
        if 'collaboration' in content_lower or 'team' in content_lower:
            culture['values'].append(CultureValue.COLLABORATION)
        if 'diversity' in content_lower or 'inclusive' in content_lower:
            culture['values'].append(CultureValue.DIVERSITY)
        if 'work-life balance' in content_lower or 'flexible' in content_lower:
            culture['values'].append(CultureValue.WORK_LIFE_BALANCE)
        
        return culture
    
    def _extract_culture_info(self, content: str) -> Dict[str, Any]:
        """提取文化信息"""
        return self._extract_culture_from_job_posting(content)
    
    def _extract_interview_info(self, content: str) -> Dict[str, Any]:
        """提取面试信息"""
        return {
            'rounds': 3,  # 默认值
            'duration': '2-3 weeks',
            'process': ['Phone Screen', 'Technical Interview', 'Final Interview']
        }
    
    def _extract_employee_feedback(self, content: str) -> Dict[str, Any]:
        """提取员工反馈"""
        return {
            'satisfaction': 3.5,  # 默认值
            'glassdoor_rating': 3.8
        }
    
    def _extract_tech_stack(self, content: str) -> Dict[str, Any]:
        """提取技术栈信息"""
        tech_keywords = [
            'python', 'java', 'javascript', 'react', 'node.js', 'aws', 'docker', 
            'kubernetes', 'microservices', 'sql', 'nosql', 'mongodb', 'postgresql'
        ]
        
        content_lower = content.lower()
        found_tech = [tech for tech in tech_keywords if tech in content_lower]
        
        return {
            'technologies': found_tech,
            'practices': ['agile', 'ci/cd'] if 'agile' in content_lower else []
        }
    
    def _compile_analysis_result(self, company_name: str, basic_info: Dict, 
                               culture_info: Dict, interview_info: Dict,
                               employee_feedback: Dict, tech_info: Dict) -> CompanyAnalysisResult:
        """编译分析结果"""
        
        # 创建面试流程对象
        interview_process = InterviewProcess(
            total_rounds=interview_info.get('rounds', 3),
            typical_duration=interview_info.get('duration', '2-3 weeks'),
            rounds=[
                {'type': 'Phone Screen', 'duration': '30 min'},
                {'type': 'Technical Interview', 'duration': '60 min'},
                {'type': 'Final Interview', 'duration': '45 min'}
            ]
        )
        
        # 创建公司财务信息
        financials = CompanyFinancials()
        
        # 创建福利信息
        benefits = CompanyBenefits(
            health_insurance=True,  # 默认假设
            flexible_schedule=CultureValue.WORK_LIFE_BALANCE in culture_info.get('values', [])
        )
        
        # 计算分析置信度
        confidence = self._calculate_confidence(basic_info, culture_info, interview_info)
        
        return CompanyAnalysisResult(
            company_name=company_name,
            industry=basic_info.get('industry', 'Unknown'),
            company_size=basic_info.get('size', CompanySize.UNKNOWN),
            company_type=basic_info.get('type', CompanyType.OTHER),
            founded_year=basic_info.get('founded'),
            headquarters=basic_info.get('headquarters', ''),
            website=basic_info.get('website', ''),
            
            culture_values=culture_info.get('values', []),
            culture_description=culture_info.get('description', ''),
            work_environment=culture_info.get('work_environment', ''),
            
            interview_process=interview_process,
            
            financials=financials,
            benefits=benefits,
            
            employee_satisfaction=employee_feedback.get('satisfaction'),
            glassdoor_rating=employee_feedback.get('glassdoor_rating'),
            
            tech_stack=tech_info.get('technologies', []),
            development_practices=tech_info.get('practices', []),
            
            analysis_confidence=confidence,
            data_sources=['web_search', 'job_posting'],
            last_updated=time.strftime('%Y-%m-%d %H:%M:%S'),
            analysis_notes='Automated analysis based on web search results'
        )
    
    def _calculate_confidence(self, basic_info: Dict, culture_info: Dict, interview_info: Dict) -> float:
        """计算分析置信度"""
        confidence = 0.0
        
        # 基本信息权重
        if basic_info.get('industry'):
            confidence += 0.2
        if basic_info.get('size') != CompanySize.UNKNOWN:
            confidence += 0.2
        
        # 文化信息权重
        if culture_info.get('values'):
            confidence += 0.3
        
        # 面试信息权重
        if interview_info.get('rounds'):
            confidence += 0.3
        
        return min(confidence, 1.0)
    
    def _create_fallback_result(self, company_name: str) -> CompanyAnalysisResult:
        """创建备用结果"""
        return CompanyAnalysisResult(
            company_name=company_name,
            industry="Unknown",
            company_size=CompanySize.UNKNOWN,
            company_type=CompanyType.OTHER,
            analysis_confidence=0.1,
            data_sources=[],
            last_updated=time.strftime('%Y-%m-%d %H:%M:%S'),
            analysis_notes='Fallback result due to analysis failure'
        )


# 便捷函数
async def analyze_company(company_name: str, 
                         job_posting_url: Optional[str] = None,
                         mcp_manager: Optional[MCPManager] = None) -> CompanyAnalysisResult:
    """
    分析公司信息的便捷函数
    
    Args:
        company_name: 公司名称
        job_posting_url: 职位发布URL（可选）
        mcp_manager: MCP管理器实例（可选）
        
    Returns:
        CompanyAnalysisResult: 公司分析结果
    """
    analyzer = CompanyAnalyzer(mcp_manager)
    return await analyzer.analyze_company(company_name, job_posting_url)


# 测试函数
async def test_company_analysis():
    """测试公司分析功能"""
    print("=== 公司分析测试 ===")
    
    # 测试公司列表
    test_companies = [
        "Google",
        "Microsoft",
        "OpenAI"
    ]
    
    for company in test_companies:
        print(f"\n分析公司: {company}")
        try:
            result = await analyze_company(company)
            print(f"公司类型: {result.company_type.value}")
            print(f"公司规模: {result.company_size.value}")
            print(f"文化价值观: {[v.value for v in result.culture_values]}")
            print(f"面试轮数: {result.interview_process.total_rounds}")
            print(f"技术栈: {result.tech_stack}")
            print(f"分析置信度: {result.analysis_confidence:.2f}")
        except Exception as e:
            print(f"分析失败: {e}")


if __name__ == "__main__":
    asyncio.run(test_company_analysis())