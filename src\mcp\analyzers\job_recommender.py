#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
职位推荐引擎 - 基于MCP的智能职位推荐系统

此模块提供全面的职位推荐功能，包括：
- 基于技能匹配的职位推荐
- 职业发展路径分析
- 薪资预测和市场分析
- 个性化推荐算法
- 职位趋势分析
- 技能缺口识别
- 学习路径建议
- 职业规划指导

作者: AI Assistant
创建时间: 2024
"""

import asyncio
import json
import time
import math
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
import logging
from pathlib import Path
from datetime import datetime, timedelta
import hashlib

# 导入MCP管理器
try:
    from src.mcp.core.mcp_manager import get_mcp_manager, MCPToolResult
except ImportError:
    # 如果导入失败，创建模拟实现
    class MCPToolResult:
        def __init__(self, success=True, result=None, error=None):
            self.success = success
            self.result = result
            self.error = error
    
    def get_mcp_manager():
        return None

# 导入其他分析器
try:
    from src.mcp.analyzers.job_analyzer import JobAnalysisResult, SkillRequirement
    from src.mcp.analyzers.company_analyzer import CompanyAnalysisResult
    from src.mcp.analyzers.resume_optimizer import ResumeContent
except ImportError:
    # 创建模拟类
    @dataclass
    class JobAnalysisResult:
        job_title: str = ""
        required_skills: List = field(default_factory=list)
        preferred_skills: List = field(default_factory=list)
        responsibilities: List[str] = field(default_factory=list)
        company_name: str = ""
        keywords: List[str] = field(default_factory=list)
        experience_requirements: str = ""
        salary_range: str = ""
        location: str = ""
        job_type: str = ""
        industry: str = ""
    
    @dataclass
    class SkillRequirement:
        name: str = ""
        importance: str = ""
        category: str = ""
        proficiency_level: str = ""
    
    @dataclass
    class CompanyAnalysisResult:
        company_name: str = ""
        culture_values: List[str] = field(default_factory=list)
        preferred_backgrounds: List[str] = field(default_factory=list)
        company_size: str = ""
        industry: str = ""
        growth_stage: str = ""
        tech_stack: List[str] = field(default_factory=list)
    
    @dataclass
    class ResumeContent:
        name: str = ""
        technical_skills: List[str] = field(default_factory=list)
        soft_skills: List[str] = field(default_factory=list)
        work_experience: List[Dict[str, Any]] = field(default_factory=list)
        education: List[Dict[str, Any]] = field(default_factory=list)
        target_role: str = ""
        target_industry: str = ""
        experience_years: int = 0

# 导入现有模块（保持兼容性）
try:
    from src.utils.logger import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)

try:
    from src.utils.config_loader import ConfigLoader
except ImportError:
    class ConfigLoader:
        @staticmethod
        def load_config(config_path: str) -> Dict[str, Any]:
            return {}


class RecommendationType(Enum):
    """推荐类型"""
    SKILL_BASED = "skill_based"           # 基于技能匹配
    CAREER_GROWTH = "career_growth"       # 职业发展
    SALARY_OPTIMIZED = "salary_optimized" # 薪资优化
    LOCATION_BASED = "location_based"     # 地理位置
    COMPANY_CULTURE = "company_culture"   # 公司文化
    INDUSTRY_TREND = "industry_trend"     # 行业趋势
    LEARNING_PATH = "learning_path"       # 学习路径
    TRANSITION = "transition"             # 职业转换


class MatchLevel(Enum):
    """匹配程度"""
    EXCELLENT = "excellent"  # 优秀匹配 (90-100%)
    GOOD = "good"            # 良好匹配 (75-89%)
    FAIR = "fair"            # 一般匹配 (60-74%)
    POOR = "poor"            # 较差匹配 (40-59%)
    VERY_POOR = "very_poor"  # 很差匹配 (0-39%)


class CareerLevel(Enum):
    """职业级别"""
    ENTRY = "entry"           # 入门级
    JUNIOR = "junior"         # 初级
    MID = "mid"               # 中级
    SENIOR = "senior"         # 高级
    LEAD = "lead"             # 主管级
    PRINCIPAL = "principal"   # 首席级
    DIRECTOR = "director"     # 总监级
    VP = "vp"                 # 副总裁级
    C_LEVEL = "c_level"       # C级高管


class JobMarketTrend(Enum):
    """职位市场趋势"""
    HOT = "hot"               # 热门
    GROWING = "growing"       # 增长中
    STABLE = "stable"         # 稳定
    DECLINING = "declining"   # 下降中
    EMERGING = "emerging"     # 新兴


@dataclass
class SkillGap:
    """技能缺口数据类"""
    skill_name: str
    importance: str  # critical, high, medium, low
    current_level: str  # none, basic, intermediate, advanced, expert
    required_level: str
    gap_score: float  # 0-1, 1表示完全缺失
    learning_difficulty: str  # easy, medium, hard
    estimated_learning_time: str  # 学习时间估计
    learning_resources: List[str] = field(default_factory=list)
    related_skills: List[str] = field(default_factory=list)


@dataclass
class SalaryPrediction:
    """薪资预测数据类"""
    position_title: str
    location: str
    experience_level: str
    
    # 薪资范围
    min_salary: float
    max_salary: float
    median_salary: float
    average_salary: float
    
    # 市场数据
    market_percentile: float  # 在市场中的百分位
    salary_growth_rate: float  # 年增长率
    demand_level: str  # high, medium, low
    
    # 影响因素
    salary_factors: Dict[str, float] = field(default_factory=dict)
    bonus_potential: float = 0.0
    equity_potential: str = ""
    
    # 数据来源
    data_sources: List[str] = field(default_factory=list)
    confidence_score: float = 0.0
    last_updated: str = field(default_factory=lambda: datetime.now().isoformat())


@dataclass
class CareerPath:
    """职业路径数据类"""
    path_id: str
    current_role: str
    target_role: str
    
    # 路径信息
    estimated_duration: str  # 预计时间
    difficulty_level: str    # easy, medium, hard
    success_probability: float  # 成功概率
    
    # 步骤
    intermediate_roles: List[str] = field(default_factory=list)
    required_skills: List[SkillGap] = field(default_factory=list)
    recommended_actions: List[str] = field(default_factory=list)
    
    # 市场信息
    market_demand: str = ""  # 市场需求
    salary_progression: List[float] = field(default_factory=list)
    
    # 学习建议
    learning_milestones: List[Dict[str, Any]] = field(default_factory=list)
    certification_recommendations: List[str] = field(default_factory=list)
    
    # 网络建议
    networking_suggestions: List[str] = field(default_factory=list)
    industry_events: List[str] = field(default_factory=list)


@dataclass
class JobRecommendation:
    """职位推荐数据类"""
    recommendation_id: str
    job_analysis: JobAnalysisResult
    company_analysis: Optional[CompanyAnalysisResult] = None
    
    # 匹配分析
    overall_match_score: float = 0.0
    match_level: MatchLevel = MatchLevel.FAIR
    
    # 详细匹配
    skill_match_score: float = 0.0
    experience_match_score: float = 0.0
    culture_match_score: float = 0.0
    location_match_score: float = 0.0
    
    # 技能分析
    matched_skills: List[str] = field(default_factory=list)
    skill_gaps: List[SkillGap] = field(default_factory=list)
    transferable_skills: List[str] = field(default_factory=list)
    
    # 推荐原因
    recommendation_reasons: List[str] = field(default_factory=list)
    pros: List[str] = field(default_factory=list)
    cons: List[str] = field(default_factory=list)
    
    # 市场信息
    salary_prediction: Optional[SalaryPrediction] = None
    market_trend: JobMarketTrend = JobMarketTrend.STABLE
    competition_level: str = "medium"  # low, medium, high
    
    # 申请建议
    application_tips: List[str] = field(default_factory=list)
    resume_optimization_tips: List[str] = field(default_factory=list)
    interview_preparation_tips: List[str] = field(default_factory=list)
    
    # 元数据
    recommendation_confidence: float = 0.0
    data_freshness: str = ""
    recommendation_timestamp: float = field(default_factory=time.time)


@dataclass
class JobRecommendationResult:
    """职位推荐结果数据类"""
    recommendation_id: str
    timestamp: float = field(default_factory=time.time)
    
    # 输入信息
    user_profile: ResumeContent
    recommendation_criteria: Dict[str, Any] = field(default_factory=dict)
    
    # 推荐结果
    recommendations: List[JobRecommendation] = field(default_factory=list)
    total_jobs_analyzed: int = 0
    
    # 分类推荐
    perfect_matches: List[JobRecommendation] = field(default_factory=list)
    good_matches: List[JobRecommendation] = field(default_factory=list)
    growth_opportunities: List[JobRecommendation] = field(default_factory=list)
    stretch_goals: List[JobRecommendation] = field(default_factory=list)
    
    # 职业发展
    career_paths: List[CareerPath] = field(default_factory=list)
    skill_development_plan: List[SkillGap] = field(default_factory=list)
    
    # 市场洞察
    market_insights: Dict[str, Any] = field(default_factory=dict)
    industry_trends: List[str] = field(default_factory=list)
    emerging_roles: List[str] = field(default_factory=list)
    
    # 个性化建议
    personalized_advice: List[str] = field(default_factory=list)
    next_steps: List[str] = field(default_factory=list)
    
    # 元数据
    processing_duration: float = 0.0
    data_sources: List[str] = field(default_factory=list)
    recommendation_version: str = "1.0"


class JobRecommendationEngine:
    """职位推荐引擎主类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化职位推荐引擎
        
        Args:
            config: 引擎配置
        """
        self.config = config or {}
        self.mcp_manager = get_mcp_manager()
        
        # 加载推荐算法和数据
        self.skill_weights = self._load_skill_weights()
        self.career_progression_data = self._load_career_progression_data()
        self.salary_data = self._load_salary_data()
        self.market_trends = self._load_market_trends()
        self.learning_resources = self._load_learning_resources()
        
        logger.info("职位推荐引擎初始化完成")
    
    def _load_skill_weights(self) -> Dict[str, float]:
        """加载技能权重"""
        return {
            # 技术技能权重
            'programming_languages': 0.25,
            'frameworks': 0.20,
            'databases': 0.15,
            'cloud_platforms': 0.15,
            'tools': 0.10,
            
            # 软技能权重
            'leadership': 0.20,
            'communication': 0.15,
            'problem_solving': 0.15,
            'teamwork': 0.10,
            'project_management': 0.10,
            
            # 经验权重
            'years_experience': 0.30,
            'relevant_projects': 0.25,
            'industry_experience': 0.20,
            'company_size_experience': 0.15,
            'leadership_experience': 0.10
        }
    
    def _load_career_progression_data(self) -> Dict[str, Any]:
        """加载职业发展数据"""
        return {
            'typical_progressions': {
                'software_engineer': {
                    'junior': ['Software Engineer', 'Frontend Developer', 'Backend Developer'],
                    'mid': ['Senior Software Engineer', 'Full Stack Developer'],
                    'senior': ['Lead Developer', 'Principal Engineer', 'Architect'],
                    'lead': ['Engineering Manager', 'Technical Director', 'CTO']
                },
                'data_scientist': {
                    'junior': ['Data Analyst', 'Junior Data Scientist'],
                    'mid': ['Data Scientist', 'ML Engineer'],
                    'senior': ['Senior Data Scientist', 'Principal Data Scientist'],
                    'lead': ['Data Science Manager', 'Head of Data', 'Chief Data Officer']
                },
                'product_manager': {
                    'junior': ['Associate Product Manager', 'Product Analyst'],
                    'mid': ['Product Manager', 'Senior Product Manager'],
                    'senior': ['Principal Product Manager', 'Group Product Manager'],
                    'lead': ['Director of Product', 'VP of Product', 'CPO']
                }
            },
            'transition_paths': {
                'engineer_to_manager': {
                    'duration': '2-4 years',
                    'required_skills': ['Leadership', 'Communication', 'Project Management'],
                    'success_rate': 0.7
                },
                'analyst_to_scientist': {
                    'duration': '1-3 years',
                    'required_skills': ['Machine Learning', 'Statistics', 'Python'],
                    'success_rate': 0.8
                }
            }
        }
    
    def _load_salary_data(self) -> Dict[str, Any]:
        """加载薪资数据"""
        return {
            'base_salaries': {
                'software_engineer': {
                    'junior': {'min': 60000, 'max': 90000, 'median': 75000},
                    'mid': {'min': 80000, 'max': 120000, 'median': 100000},
                    'senior': {'min': 120000, 'max': 180000, 'median': 150000},
                    'lead': {'min': 160000, 'max': 250000, 'median': 200000}
                },
                'data_scientist': {
                    'junior': {'min': 70000, 'max': 100000, 'median': 85000},
                    'mid': {'min': 90000, 'max': 140000, 'median': 115000},
                    'senior': {'min': 130000, 'max': 200000, 'median': 165000},
                    'lead': {'min': 180000, 'max': 300000, 'median': 240000}
                },
                'product_manager': {
                    'junior': {'min': 80000, 'max': 110000, 'median': 95000},
                    'mid': {'min': 100000, 'max': 150000, 'median': 125000},
                    'senior': {'min': 140000, 'max': 220000, 'median': 180000},
                    'lead': {'min': 200000, 'max': 350000, 'median': 275000}
                }
            },
            'location_multipliers': {
                'san_francisco': 1.4,
                'new_york': 1.3,
                'seattle': 1.2,
                'boston': 1.15,
                'austin': 1.1,
                'denver': 1.0,
                'remote': 0.9
            },
            'company_size_multipliers': {
                'startup': 0.8,
                'small': 0.9,
                'medium': 1.0,
                'large': 1.1,
                'enterprise': 1.2
            }
        }
    
    def _load_market_trends(self) -> Dict[str, Any]:
        """加载市场趋势数据"""
        return {
            'hot_skills': {
                'ai_ml': {'trend': 'hot', 'growth_rate': 0.35, 'demand_score': 0.9},
                'cloud_native': {'trend': 'growing', 'growth_rate': 0.25, 'demand_score': 0.8},
                'cybersecurity': {'trend': 'hot', 'growth_rate': 0.30, 'demand_score': 0.85},
                'devops': {'trend': 'stable', 'growth_rate': 0.15, 'demand_score': 0.75},
                'blockchain': {'trend': 'emerging', 'growth_rate': 0.20, 'demand_score': 0.6}
            },
            'industry_trends': {
                'technology': {'growth': 'high', 'hiring_rate': 0.8, 'competition': 'high'},
                'finance': {'growth': 'medium', 'hiring_rate': 0.6, 'competition': 'medium'},
                'healthcare': {'growth': 'high', 'hiring_rate': 0.7, 'competition': 'medium'},
                'education': {'growth': 'medium', 'hiring_rate': 0.5, 'competition': 'low'}
            },
            'emerging_roles': [
                'AI Ethics Specialist',
                'Cloud Security Architect',
                'DevOps Engineer',
                'Data Privacy Officer',
                'Sustainability Analyst'
            ]
        }
    
    def _load_learning_resources(self) -> Dict[str, List[str]]:
        """加载学习资源"""
        return {
            'programming': [
                'Coursera - Programming Fundamentals',
                'edX - Computer Science Courses',
                'Udacity - Programming Nanodegrees',
                'FreeCodeCamp - Web Development',
                'LeetCode - Algorithm Practice'
            ],
            'data_science': [
                'Kaggle Learn - Data Science Courses',
                'Coursera - Data Science Specialization',
                'edX - MIT Introduction to Data Science',
                'DataCamp - Interactive Learning',
                'Fast.ai - Practical Deep Learning'
            ],
            'cloud': [
                'AWS Training and Certification',
                'Google Cloud Training',
                'Microsoft Azure Learning',
                'Cloud Native Computing Foundation',
                'A Cloud Guru - Cloud Training'
            ],
            'leadership': [
                'LinkedIn Learning - Leadership Courses',
                'Coursera - Leadership Specializations',
                'Harvard Business Review - Management Tips',
                'Toastmasters - Communication Skills',
                'Project Management Institute - PMP'
            ]
        }
    
    async def recommend_jobs(self, user_profile: ResumeContent,
                           job_listings: List[JobAnalysisResult],
                           criteria: Optional[Dict[str, Any]] = None) -> JobRecommendationResult:
        """推荐职位
        
        Args:
            user_profile: 用户简历档案
            job_listings: 职位列表
            criteria: 推荐标准
        
        Returns:
            JobRecommendationResult: 推荐结果
        """
        start_time = time.time()
        
        logger.info(f"开始为用户推荐职位，分析 {len(job_listings)} 个职位")
        
        # 创建推荐结果对象
        result = JobRecommendationResult(
            recommendation_id=str(int(time.time())),
            user_profile=user_profile,
            recommendation_criteria=criteria or {},
            total_jobs_analyzed=len(job_listings)
        )
        
        try:
            # 1. 分析每个职位的匹配度
            recommendations = []
            for job in job_listings:
                recommendation = await self._analyze_job_match(user_profile, job)
                recommendations.append(recommendation)
            
            # 2. 排序和分类推荐
            recommendations.sort(key=lambda x: x.overall_match_score, reverse=True)
            result.recommendations = recommendations
            
            # 3. 分类推荐结果
            result.perfect_matches = [r for r in recommendations if r.match_level == MatchLevel.EXCELLENT]
            result.good_matches = [r for r in recommendations if r.match_level == MatchLevel.GOOD]
            result.growth_opportunities = await self._identify_growth_opportunities(user_profile, recommendations)
            result.stretch_goals = await self._identify_stretch_goals(user_profile, recommendations)
            
            # 4. 生成职业路径建议
            result.career_paths = await self._generate_career_paths(user_profile, recommendations)
            
            # 5. 技能发展计划
            result.skill_development_plan = await self._create_skill_development_plan(user_profile, recommendations)
            
            # 6. 市场洞察
            result.market_insights = await self._generate_market_insights(recommendations)
            result.industry_trends = self._get_industry_trends(user_profile.target_industry)
            result.emerging_roles = self.market_trends['emerging_roles']
            
            # 7. 个性化建议
            result.personalized_advice = await self._generate_personalized_advice(user_profile, result)
            result.next_steps = await self._generate_next_steps(user_profile, result)
            
            result.processing_duration = time.time() - start_time
            result.data_sources.append('job_recommendation')
            
            logger.info(f"职位推荐完成 (耗时: {result.processing_duration:.2f}s)")
            
            return result
            
        except Exception as e:
            logger.error(f"职位推荐失败: {e}")
            result.processing_duration = time.time() - start_time
            return result
    
    async def _analyze_job_match(self, user_profile: ResumeContent,
                               job: JobAnalysisResult) -> JobRecommendation:
        """分析职位匹配度"""
        recommendation = JobRecommendation(
            recommendation_id=f"{int(time.time())}_{hash(job.job_title)}",
            job_analysis=job
        )
        
        # 1. 技能匹配分析
        recommendation.skill_match_score = await self._calculate_skill_match(user_profile, job)
        recommendation.matched_skills, recommendation.skill_gaps = await self._analyze_skill_gaps(user_profile, job)
        
        # 2. 经验匹配分析
        recommendation.experience_match_score = await self._calculate_experience_match(user_profile, job)
        
        # 3. 文化匹配分析（如果有公司信息）
        recommendation.culture_match_score = 0.7  # 默认值
        
        # 4. 地理位置匹配
        recommendation.location_match_score = await self._calculate_location_match(user_profile, job)
        
        # 5. 计算总体匹配分数
        recommendation.overall_match_score = await self._calculate_overall_match(
            recommendation.skill_match_score,
            recommendation.experience_match_score,
            recommendation.culture_match_score,
            recommendation.location_match_score
        )
        
        # 6. 确定匹配等级
        recommendation.match_level = self._determine_match_level(recommendation.overall_match_score)
        
        # 7. 生成推荐原因
        recommendation.recommendation_reasons = await self._generate_recommendation_reasons(
            user_profile, job, recommendation
        )
        
        # 8. 优缺点分析
        recommendation.pros, recommendation.cons = await self._analyze_pros_cons(user_profile, job)
        
        # 9. 薪资预测
        recommendation.salary_prediction = await self._predict_salary(job, user_profile)
        
        # 10. 市场趋势
        recommendation.market_trend = self._get_job_market_trend(job)
        
        # 11. 申请建议
        recommendation.application_tips = await self._generate_application_tips(user_profile, job)
        recommendation.resume_optimization_tips = await self._generate_resume_tips(user_profile, job)
        recommendation.interview_preparation_tips = await self._generate_interview_tips(user_profile, job)
        
        # 12. 设置推荐置信度
        recommendation.recommendation_confidence = self._calculate_recommendation_confidence(recommendation)
        
        return recommendation
    
    async def _calculate_skill_match(self, user_profile: ResumeContent,
                                   job: JobAnalysisResult) -> float:
        """计算技能匹配度"""
        user_skills = set(skill.lower() for skill in 
                         user_profile.technical_skills + user_profile.soft_skills)
        
        # 收集职位要求的技能
        required_skills = set()
        for skill in job.required_skills:
            required_skills.add(skill.name.lower())
        
        for skill in job.preferred_skills:
            required_skills.add(skill.name.lower())
        
        if not required_skills:
            return 0.5  # 默认分数
        
        # 计算匹配的技能数量
        matched_skills = user_skills & required_skills
        match_ratio = len(matched_skills) / len(required_skills)
        
        # 考虑技能重要性权重
        weighted_score = 0.0
        total_weight = 0.0
        
        for skill in job.required_skills:
            weight = 1.0
            if skill.importance == 'critical':
                weight = 2.0
            elif skill.importance == 'high':
                weight = 1.5
            elif skill.importance == 'medium':
                weight = 1.0
            else:
                weight = 0.5
            
            total_weight += weight
            if skill.name.lower() in user_skills:
                weighted_score += weight
        
        if total_weight > 0:
            weighted_match = weighted_score / total_weight
        else:
            weighted_match = match_ratio
        
        # 综合评分
        final_score = (match_ratio * 0.4 + weighted_match * 0.6)
        return min(final_score, 1.0)
    
    async def _analyze_skill_gaps(self, user_profile: ResumeContent,
                                job: JobAnalysisResult) -> Tuple[List[str], List[SkillGap]]:
        """分析技能缺口"""
        user_skills = set(skill.lower() for skill in 
                         user_profile.technical_skills + user_profile.soft_skills)
        
        matched_skills = []
        skill_gaps = []
        
        # 分析必需技能
        for skill in job.required_skills:
            skill_name_lower = skill.name.lower()
            
            if skill_name_lower in user_skills:
                matched_skills.append(skill.name)
            else:
                # 创建技能缺口
                gap = SkillGap(
                    skill_name=skill.name,
                    importance=skill.importance,
                    current_level="none",
                    required_level=getattr(skill, 'proficiency_level', 'intermediate'),
                    gap_score=1.0,  # 完全缺失
                    learning_difficulty=self._estimate_learning_difficulty(skill.name),
                    estimated_learning_time=self._estimate_learning_time(skill.name),
                    learning_resources=self._get_learning_resources(skill.name)
                )
                skill_gaps.append(gap)
        
        return matched_skills, skill_gaps
    
    def _estimate_learning_difficulty(self, skill_name: str) -> str:
        """估算学习难度"""
        # 简化的难度评估
        hard_skills = ['machine learning', 'deep learning', 'kubernetes', 'system design']
        medium_skills = ['python', 'javascript', 'sql', 'git']
        
        skill_lower = skill_name.lower()
        
        if any(hard in skill_lower for hard in hard_skills):
            return 'hard'
        elif any(medium in skill_lower for medium in medium_skills):
            return 'medium'
        else:
            return 'easy'
    
    def _estimate_learning_time(self, skill_name: str) -> str:
        """估算学习时间"""
        difficulty = self._estimate_learning_difficulty(skill_name)
        
        if difficulty == 'hard':
            return '6-12 months'
        elif difficulty == 'medium':
            return '2-6 months'
        else:
            return '2-8 weeks'
    
    def _get_learning_resources(self, skill_name: str) -> List[str]:
        """获取学习资源"""
        skill_lower = skill_name.lower()
        
        # 根据技能类型返回相关资源
        if any(keyword in skill_lower for keyword in ['python', 'java', 'javascript', 'programming']):
            return self.learning_resources['programming']
        elif any(keyword in skill_lower for keyword in ['data', 'machine learning', 'ai']):
            return self.learning_resources['data_science']
        elif any(keyword in skill_lower for keyword in ['aws', 'azure', 'cloud']):
            return self.learning_resources['cloud']
        elif any(keyword in skill_lower for keyword in ['leadership', 'management']):
            return self.learning_resources['leadership']
        else:
            return ['在线搜索相关课程', '查看官方文档', '参加相关培训']
    
    async def _calculate_experience_match(self, user_profile: ResumeContent,
                                        job: JobAnalysisResult) -> float:
        """计算经验匹配度"""
        # 解析职位经验要求
        required_years = self._extract_experience_years(job.experience_requirements)
        user_years = getattr(user_profile, 'experience_years', 0)
        
        if required_years == 0:
            return 0.8  # 默认分数
        
        # 计算经验匹配度
        if user_years >= required_years:
            # 经验充足，但不要过度奖励
            excess_ratio = (user_years - required_years) / required_years
            if excess_ratio <= 0.5:  # 经验适中
                return 1.0
            else:  # 经验过多可能overqualified
                return max(0.8, 1.0 - (excess_ratio - 0.5) * 0.2)
        else:
            # 经验不足
            shortage_ratio = (required_years - user_years) / required_years
            return max(0.3, 1.0 - shortage_ratio)
    
    def _extract_experience_years(self, experience_text: str) -> int:
        """从经验描述中提取年数"""
        if not experience_text:
            return 0
        
        # 查找年数模式
        import re
        patterns = [
            r'(\d+)\+?\s*years?',
            r'(\d+)\+?\s*yrs?',
            r'(\d+)\+?\s*年'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, experience_text.lower())
            if match:
                return int(match.group(1))
        
        # 根据关键词估算
        if 'senior' in experience_text.lower():
            return 5
        elif 'junior' in experience_text.lower() or 'entry' in experience_text.lower():
            return 1
        elif 'mid' in experience_text.lower():
            return 3
        
        return 2  # 默认值
    
    async def _calculate_location_match(self, user_profile: ResumeContent,
                                      job: JobAnalysisResult) -> float:
        """计算地理位置匹配度"""
        # 简化的位置匹配
        if 'remote' in job.location.lower():
            return 1.0
        
        # 如果用户没有位置偏好，给中等分数
        return 0.7
    
    async def _calculate_overall_match(self, skill_score: float, experience_score: float,
                                     culture_score: float, location_score: float) -> float:
        """计算总体匹配分数"""
        # 权重配置
        weights = {
            'skills': 0.4,
            'experience': 0.3,
            'culture': 0.2,
            'location': 0.1
        }
        
        overall_score = (
            skill_score * weights['skills'] +
            experience_score * weights['experience'] +
            culture_score * weights['culture'] +
            location_score * weights['location']
        )
        
        return min(overall_score, 1.0)
    
    def _determine_match_level(self, score: float) -> MatchLevel:
        """确定匹配等级"""
        if score >= 0.9:
            return MatchLevel.EXCELLENT
        elif score >= 0.75:
            return MatchLevel.GOOD
        elif score >= 0.6:
            return MatchLevel.FAIR
        elif score >= 0.4:
            return MatchLevel.POOR
        else:
            return MatchLevel.VERY_POOR
    
    async def _generate_recommendation_reasons(self, user_profile: ResumeContent,
                                             job: JobAnalysisResult,
                                             recommendation: JobRecommendation) -> List[str]:
        """生成推荐原因"""
        reasons = []
        
        # 基于技能匹配
        if recommendation.skill_match_score >= 0.8:
            reasons.append(f"技能匹配度高达 {recommendation.skill_match_score:.0%}")
        
        # 基于经验匹配
        if recommendation.experience_match_score >= 0.8:
            reasons.append("经验要求与您的背景高度匹配")
        
        # 基于匹配的技能
        if len(recommendation.matched_skills) >= 5:
            reasons.append(f"您具备 {len(recommendation.matched_skills)} 项相关技能")
        
        # 基于职业发展
        if self._is_career_growth_opportunity(user_profile, job):
            reasons.append("这是一个很好的职业发展机会")
        
        # 基于市场趋势
        if recommendation.market_trend in [JobMarketTrend.HOT, JobMarketTrend.GROWING]:
            reasons.append("该职位在市场上需求旺盛")
        
        return reasons
    
    def _is_career_growth_opportunity(self, user_profile: ResumeContent,
                                    job: JobAnalysisResult) -> bool:
        """判断是否为职业发展机会"""
        # 简化的判断逻辑
        job_title_lower = job.job_title.lower()
        
        # 如果职位包含高级关键词
        growth_keywords = ['senior', 'lead', 'principal', 'manager', 'director']
        return any(keyword in job_title_lower for keyword in growth_keywords)
    
    async def _analyze_pros_cons(self, user_profile: ResumeContent,
                               job: JobAnalysisResult) -> Tuple[List[str], List[str]]:
        """分析优缺点"""
        pros = []
        cons = []
        
        # 分析优点
        if 'remote' in job.location.lower():
            pros.append("支持远程工作")
        
        if job.company_name and len(job.company_name) > 0:
            pros.append(f"在 {job.company_name} 工作的品牌价值")
        
        if len(job.required_skills) <= 5:
            pros.append("技能要求明确且合理")
        
        # 分析缺点
        if len(job.required_skills) > 10:
            cons.append("技能要求较多，可能需要额外学习")
        
        if not job.salary_range:
            cons.append("薪资信息不明确")
        
        return pros, cons
    
    async def _predict_salary(self, job: JobAnalysisResult,
                            user_profile: ResumeContent) -> SalaryPrediction:
        """预测薪资"""
        # 简化的薪资预测
        base_role = self._categorize_role(job.job_title)
        experience_level = self._determine_experience_level(user_profile)
        
        # 获取基础薪资数据
        salary_data = self.salary_data['base_salaries'].get(base_role, {
            'junior': {'min': 50000, 'max': 80000, 'median': 65000},
            'mid': {'min': 70000, 'max': 110000, 'median': 90000},
            'senior': {'min': 100000, 'max': 160000, 'median': 130000}
        })
        
        level_data = salary_data.get(experience_level, salary_data['mid'])
        
        # 应用位置调整
        location_multiplier = self._get_location_multiplier(job.location)
        
        prediction = SalaryPrediction(
            position_title=job.job_title,
            location=job.location,
            experience_level=experience_level,
            min_salary=level_data['min'] * location_multiplier,
            max_salary=level_data['max'] * location_multiplier,
            median_salary=level_data['median'] * location_multiplier,
            average_salary=level_data['median'] * location_multiplier * 1.05,
            market_percentile=0.6,
            salary_growth_rate=0.08,
            demand_level='medium',
            confidence_score=0.7
        )
        
        return prediction
    
    def _categorize_role(self, job_title: str) -> str:
        """分类职位角色"""
        title_lower = job_title.lower()
        
        if any(keyword in title_lower for keyword in ['engineer', 'developer', 'programmer']):
            return 'software_engineer'
        elif any(keyword in title_lower for keyword in ['data scientist', 'ml engineer', 'ai']):
            return 'data_scientist'
        elif any(keyword in title_lower for keyword in ['product manager', 'pm']):
            return 'product_manager'
        else:
            return 'software_engineer'  # 默认
    
    def _determine_experience_level(self, user_profile: ResumeContent) -> str:
        """确定经验级别"""
        years = getattr(user_profile, 'experience_years', 0)
        
        if years <= 2:
            return 'junior'
        elif years <= 5:
            return 'mid'
        else:
            return 'senior'
    
    def _get_location_multiplier(self, location: str) -> float:
        """获取位置薪资倍数"""
        location_lower = location.lower()
        
        for city, multiplier in self.salary_data['location_multipliers'].items():
            if city.replace('_', ' ') in location_lower:
                return multiplier
        
        return 1.0  # 默认倍数
    
    def _get_job_market_trend(self, job: JobAnalysisResult) -> JobMarketTrend:
        """获取职位市场趋势"""
        job_title_lower = job.job_title.lower()
        
        # 检查热门技能
        for skill_area, data in self.market_trends['hot_skills'].items():
            if skill_area.replace('_', ' ') in job_title_lower:
                trend_map = {
                    'hot': JobMarketTrend.HOT,
                    'growing': JobMarketTrend.GROWING,
                    'stable': JobMarketTrend.STABLE,
                    'emerging': JobMarketTrend.EMERGING
                }
                return trend_map.get(data['trend'], JobMarketTrend.STABLE)
        
        return JobMarketTrend.STABLE
    
    async def _generate_application_tips(self, user_profile: ResumeContent,
                                       job: JobAnalysisResult) -> List[str]:
        """生成申请建议"""
        tips = []
        
        # 基于技能匹配的建议
        user_skills = set(skill.lower() for skill in 
                         user_profile.technical_skills + user_profile.soft_skills)
        job_skills = set(skill.name.lower() for skill in job.required_skills)
        
        missing_skills = job_skills - user_skills
        if missing_skills:
            tips.append(f"在申请前考虑学习: {', '.join(list(missing_skills)[:3])}")
        
        # 通用申请建议
        tips.extend([
            "仔细阅读职位描述，确保简历与要求匹配",
            "准备具体的项目案例来展示相关技能",
            "研究公司文化和价值观",
            "准备针对性的求职信"
        ])
        
        return tips
    
    async def _generate_resume_tips(self, user_profile: ResumeContent,
                                  job: JobAnalysisResult) -> List[str]:
        """生成简历优化建议"""
        tips = []
        
        # 关键词优化
        job_keywords = [skill.name for skill in job.required_skills]
        tips.append(f"在简历中突出这些关键词: {', '.join(job_keywords[:5])}")
        
        # 经验描述优化
        tips.extend([
            "使用量化指标描述工作成就",
            "突出与目标职位相关的项目经验",
            "确保技能部分包含职位要求的技术栈",
            "调整简历格式以提高ATS兼容性"
        ])
        
        return tips
    
    async def _generate_interview_tips(self, user_profile: ResumeContent,
                                     job: JobAnalysisResult) -> List[str]:
        """生成面试准备建议"""
        tips = []
        
        # 技术准备
        technical_skills = [skill.name for skill in job.required_skills 
                          if skill.category in ['technical', 'programming']]
        if technical_skills:
            tips.append(f"准备 {', '.join(technical_skills[:3])} 相关的技术问题")
        
        # 行为面试准备
        tips.extend([
            "准备STAR格式的行为问题回答",
            "研究公司最近的新闻和发展",
            "准备关于职业目标的问题",
            "练习解释复杂技术概念的能力"
        ])
        
        return tips
    
    def _calculate_recommendation_confidence(self, recommendation: JobRecommendation) -> float:
        """计算推荐置信度"""
        factors = [
            recommendation.skill_match_score,
            recommendation.experience_match_score,
            0.8 if recommendation.salary_prediction else 0.5,
            0.9 if len(recommendation.matched_skills) >= 3 else 0.6
        ]
        
        return sum(factors) / len(factors)
    
    async def _identify_growth_opportunities(self, user_profile: ResumeContent,
                                           recommendations: List[JobRecommendation]) -> List[JobRecommendation]:
        """识别成长机会"""
        growth_opportunities = []
        
        for rec in recommendations:
            # 判断是否为成长机会
            job_title = rec.job_analysis.job_title.lower()
            
            # 包含成长关键词的职位
            growth_keywords = ['senior', 'lead', 'principal', 'manager']
            if any(keyword in job_title for keyword in growth_keywords):
                # 技能匹配度适中（有挑战但不过分）
                if 0.6 <= rec.skill_match_score <= 0.8:
                    growth_opportunities.append(rec)
        
        return growth_opportunities[:5]  # 返回前5个
    
    async def _identify_stretch_goals(self, user_profile: ResumeContent,
                                    recommendations: List[JobRecommendation]) -> List[JobRecommendation]:
        """识别挑战目标"""
        stretch_goals = []
        
        for rec in recommendations:
            # 技能匹配度较低但有发展潜力
            if 0.4 <= rec.skill_match_score < 0.6:
                # 市场趋势良好
                if rec.market_trend in [JobMarketTrend.HOT, JobMarketTrend.GROWING]:
                    stretch_goals.append(rec)
        
        return stretch_goals[:3]  # 返回前3个
    
    async def _generate_career_paths(self, user_profile: ResumeContent,
                                   recommendations: List[JobRecommendation]) -> List[CareerPath]:
        """生成职业路径"""
        career_paths = []
        
        # 基于当前角色生成路径
        current_role = user_profile.target_role or "Software Engineer"
        
        # 生成典型的职业发展路径
        path = CareerPath(
            path_id=f"path_{int(time.time())}",
            current_role=current_role,
            target_role="Senior " + current_role,
            estimated_duration="2-3 years",
            difficulty_level="medium",
            success_probability=0.7,
            intermediate_roles=["Mid-level " + current_role],
            recommended_actions=[
                "深化现有技能",
                "承担更多责任",
                "领导小型项目",
                "获得相关认证"
            ],
            market_demand="high",
            learning_milestones=[
                {"milestone": "掌握高级技术技能", "timeline": "6个月"},
                {"milestone": "领导项目经验", "timeline": "12个月"},
                {"milestone": "获得行业认证", "timeline": "18个月"}
            ]
        )
        
        career_paths.append(path)
        
        return career_paths
    
    async def _create_skill_development_plan(self, user_profile: ResumeContent,
                                           recommendations: List[JobRecommendation]) -> List[SkillGap]:
        """创建技能发展计划"""
        # 收集所有技能缺口
        all_skill_gaps = []
        skill_importance = {}
        
        for rec in recommendations[:10]:  # 只考虑前10个推荐
            for gap in rec.skill_gaps:
                if gap.skill_name not in skill_importance:
                    skill_importance[gap.skill_name] = 0
                    all_skill_gaps.append(gap)
                
                # 累计重要性分数
                importance_score = {
                    'critical': 3,
                    'high': 2,
                    'medium': 1,
                    'low': 0.5
                }.get(gap.importance, 1)
                
                skill_importance[gap.skill_name] += importance_score
        
        # 按重要性排序
        sorted_gaps = sorted(all_skill_gaps, 
                           key=lambda x: skill_importance[x.skill_name], 
                           reverse=True)
        
        return sorted_gaps[:10]  # 返回前10个最重要的技能缺口
    
    async def _generate_market_insights(self, recommendations: List[JobRecommendation]) -> Dict[str, Any]:
        """生成市场洞察"""
        insights = {
            'total_opportunities': len(recommendations),
            'high_match_count': len([r for r in recommendations if r.overall_match_score >= 0.8]),
            'average_match_score': sum(r.overall_match_score for r in recommendations) / len(recommendations) if recommendations else 0,
            'top_skills_in_demand': [],
            'salary_ranges': {},
            'location_distribution': {},
            'company_sizes': {},
            'market_trends': {}
        }
        
        # 分析需求最高的技能
        skill_demand = {}
        for rec in recommendations:
            for skill in rec.job_analysis.required_skills:
                skill_demand[skill.name] = skill_demand.get(skill.name, 0) + 1
        
        insights['top_skills_in_demand'] = sorted(skill_demand.items(), 
                                                 key=lambda x: x[1], 
                                                 reverse=True)[:10]
        
        # 薪资分析
        salaries = [rec.salary_prediction.median_salary for rec in recommendations 
                   if rec.salary_prediction]
        if salaries:
            insights['salary_ranges'] = {
                'min': min(salaries),
                'max': max(salaries),
                'average': sum(salaries) / len(salaries)
            }
        
        return insights
    
    def _get_industry_trends(self, industry: str) -> List[str]:
        """获取行业趋势"""
        if not industry:
            return []
        
        industry_lower = industry.lower()
        
        # 基于行业返回趋势
        if 'technology' in industry_lower or 'tech' in industry_lower:
            return [
                "AI和机器学习需求持续增长",
                "云原生技术成为主流",
                "远程工作模式普及",
                "网络安全重要性提升",
                "低代码/无代码平台兴起"
            ]
        elif 'finance' in industry_lower:
            return [
                "金融科技快速发展",
                "数字化转型加速",
                "监管科技需求增长",
                "区块链技术应用扩展",
                "ESG投资成为趋势"
            ]
        else:
            return [
                "数字化转型是各行业共同趋势",
                "数据驱动决策越来越重要",
                "自动化技术广泛应用",
                "用户体验设计受到重视"
            ]
    
    async def _generate_personalized_advice(self, user_profile: ResumeContent,
                                          result: JobRecommendationResult) -> List[str]:
        """生成个性化建议"""
        advice = []
        
        # 基于匹配结果的建议
        if len(result.perfect_matches) > 0:
            advice.append(f"您有 {len(result.perfect_matches)} 个高匹配度的职位机会，建议优先申请")
        
        if len(result.skill_development_plan) > 0:
            top_skill = result.skill_development_plan[0].skill_name
            advice.append(f"建议重点学习 {top_skill}，这将显著提升您的竞争力")
        
        # 基于经验水平的建议
        experience_years = getattr(user_profile, 'experience_years', 0)
        if experience_years < 2:
            advice.append("作为初级开发者，建议重点关注技能提升和项目经验积累")
        elif experience_years >= 5:
            advice.append("考虑申请高级或领导职位，发挥您的丰富经验")
        
        # 基于市场趋势的建议
        if result.market_insights.get('average_match_score', 0) < 0.6:
            advice.append("当前技能与市场需求存在差距，建议制定学习计划")
        
        return advice
    
    async def _generate_next_steps(self, user_profile: ResumeContent,
                                 result: JobRecommendationResult) -> List[str]:
        """生成下一步行动建议"""
        next_steps = []
        
        # 立即行动
        if result.perfect_matches:
            next_steps.append(f"立即申请 {len(result.perfect_matches)} 个高匹配度职位")
        
        # 技能提升
        if result.skill_development_plan:
            top_skills = [gap.skill_name for gap in result.skill_development_plan[:3]]
            next_steps.append(f"开始学习: {', '.join(top_skills)}")
        
        # 简历优化
        next_steps.append("根据目标职位优化简历关键词")
        
        # 网络建设
        next_steps.append("扩展专业网络，参加行业活动")
        
        # 持续学习
        next_steps.append("制定长期学习计划，跟上行业趋势")
        
        return next_steps


# 测试代码
if __name__ == "__main__":
    async def test_job_recommender():
        """测试职位推荐引擎"""
        print("=== 职位推荐引擎测试 ===")
        
        # 创建测试用户档案
        user_profile = ResumeContent(
            name="张三",
            technical_skills=["Python", "JavaScript", "React", "Node.js", "SQL"],
            soft_skills=["团队合作", "沟通能力", "问题解决"],
            work_experience=[
                {
                    "company": "科技公司A",
                    "position": "软件工程师",
                    "duration": "2年",
                    "responsibilities": ["开发Web应用", "维护数据库"]
                }
            ],
            target_role="高级软件工程师",
            target_industry="科技",
            experience_years=3
        )
        
        # 创建测试职位
        test_jobs = [
            JobAnalysisResult(
                job_title="高级前端工程师",
                required_skills=[
                    SkillRequirement(name="React", importance="critical", category="technical"),
                    SkillRequirement(name="JavaScript", importance="high", category="technical"),
                    SkillRequirement(name="TypeScript", importance="medium", category="technical")
                ],
                company_name="创新科技公司",
                experience_requirements="3-5年经验",
                salary_range="15-25万",
                location="北京",
                industry="科技"
            ),
            JobAnalysisResult(
                job_title="全栈工程师",
                required_skills=[
                    SkillRequirement(name="Python", importance="critical", category="technical"),
                    SkillRequirement(name="React", importance="high", category="technical"),
                    SkillRequirement(name="Node.js", importance="high", category="technical")
                ],
                company_name="快速发展公司",
                experience_requirements="2-4年经验",
                salary_range="12-20万",
                location="上海",
                industry="科技"
            )
        ]
        
        # 创建推荐引擎
        recommender = JobRecommendationEngine()
        
        # 执行推荐
        result = await recommender.recommend_jobs(
            user_profile=user_profile,
            job_listings=test_jobs,
            criteria={"location_preference": "北京", "salary_min": 150000}
        )
        
        # 输出结果
        print(f"\n分析了 {result.total_jobs_analyzed} 个职位")
        print(f"处理时间: {result.processing_duration:.2f}秒")
        
        print(f"\n=== 推荐结果 ===")
        for i, rec in enumerate(result.recommendations, 1):
            print(f"\n{i}. {rec.job_analysis.job_title}")
            print(f"   公司: {rec.job_analysis.company_name}")
            print(f"   匹配度: {rec.overall_match_score:.1%} ({rec.match_level.value})")
            print(f"   技能匹配: {rec.skill_match_score:.1%}")
            print(f"   经验匹配: {rec.experience_match_score:.1%}")
            print(f"   匹配技能: {', '.join(rec.matched_skills)}")
            
            if rec.skill_gaps:
                print(f"   技能缺口: {', '.join([gap.skill_name for gap in rec.skill_gaps[:3]])}")
            
            if rec.salary_prediction:
                print(f"   预期薪资: {rec.salary_prediction.min_salary:,.0f} - {rec.salary_prediction.max_salary:,.0f}")
        
        print(f"\n=== 分类推荐 ===")
        print(f"完美匹配: {len(result.perfect_matches)} 个")
        print(f"良好匹配: {len(result.good_matches)} 个")
        print(f"成长机会: {len(result.growth_opportunities)} 个")
        print(f"挑战目标: {len(result.stretch_goals)} 个")
        
        print(f"\n=== 技能发展计划 ===")
        for gap in result.skill_development_plan[:5]:
            print(f"- {gap.skill_name} ({gap.importance}) - 学习时间: {gap.estimated_learning_time}")
        
        print(f"\n=== 个性化建议 ===")
        for advice in result.personalized_advice:
            print(f"- {advice}")
        
        print(f"\n=== 下一步行动 ===")
        for step in result.next_steps:
            print(f"- {step}")
        
        print("\n=== 测试完成 ===")
    
    # 运行测试
    asyncio.run(test_job_recommender())