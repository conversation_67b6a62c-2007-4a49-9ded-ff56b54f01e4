#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP工具集成模块
处理与MCP服务器的交互和工具调用
"""

import asyncio
import json
import time
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass
from loguru import logger

try:
    from .mcp_config_manager import get_config_manager, MCPServerConfig
except ImportError:
    logger.warning("无法导入MCP配置管理器")
    get_config_manager = None
    MCPServerConfig = None

@dataclass
class MCPToolResult:
    """MCP工具调用结果"""
    success: bool
    data: Any = None
    error: str = None
    execution_time: float = 0.0
    server_name: str = None
    tool_name: str = None

class MCPToolsManager:
    """MCP工具管理器"""
    
    def __init__(self, config_manager=None):
        self.config_manager = config_manager or (get_config_manager() if get_config_manager else None)
        self.available_servers = {
            "mult-fetch-mcp-server": MultFetchMCPClient(),
            "context7-mcp": Context7MCPClient()
        }
        self.call_history = []
        self.rate_limiters = {}
        
        logger.info("MCP工具管理器初始化完成")
    
    async def call_tool(self, server_name: str, tool_name: str, args: Dict[str, Any]) -> MCPToolResult:
        """调用MCP工具"""
        start_time = time.time()
        
        try:
            # 检查服务器是否可用
            if server_name not in self.available_servers:
                return MCPToolResult(
                    success=False,
                    error=f"服务器 {server_name} 不可用",
                    server_name=server_name,
                    tool_name=tool_name
                )
            
            # 检查服务器是否启用
            if self.config_manager and not self.config_manager.is_server_enabled(server_name):
                return MCPToolResult(
                    success=False,
                    error=f"服务器 {server_name} 已禁用",
                    server_name=server_name,
                    tool_name=tool_name
                )
            
            # 应用速率限制
            await self._apply_rate_limit(server_name)
            
            # 调用工具
            client = self.available_servers[server_name]
            result = await client.call_tool(tool_name, args)
            
            execution_time = time.time() - start_time
            
            # 记录调用历史
            self._record_call(server_name, tool_name, args, result, execution_time)
            
            return MCPToolResult(
                success=True,
                data=result,
                execution_time=execution_time,
                server_name=server_name,
                tool_name=tool_name
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"调用工具失败: {str(e)}"
            logger.error(f"{server_name}.{tool_name} - {error_msg}")
            
            return MCPToolResult(
                success=False,
                error=error_msg,
                execution_time=execution_time,
                server_name=server_name,
                tool_name=tool_name
            )
    
    async def _apply_rate_limit(self, server_name: str):
        """应用速率限制"""
        if not self.config_manager:
            return
        
        server_config = self.config_manager.get_server_config(server_name)
        if not server_config or server_config.rate_limit <= 0:
            return
        
        current_time = time.time()
        last_call_time = self.rate_limiters.get(server_name, 0)
        
        time_since_last_call = current_time - last_call_time
        if time_since_last_call < server_config.rate_limit:
            sleep_time = server_config.rate_limit - time_since_last_call
            await asyncio.sleep(sleep_time)
        
        self.rate_limiters[server_name] = time.time()
    
    def _record_call(self, server_name: str, tool_name: str, args: Dict, result: Any, execution_time: float):
        """记录调用历史"""
        call_record = {
            "timestamp": time.time(),
            "server_name": server_name,
            "tool_name": tool_name,
            "args": args,
            "success": result is not None,
            "execution_time": execution_time
        }
        
        self.call_history.append(call_record)
        
        # 保持历史记录在合理范围内
        if len(self.call_history) > 1000:
            self.call_history = self.call_history[-500:]
    
    def get_call_statistics(self) -> Dict[str, Any]:
        """获取调用统计"""
        if not self.call_history:
            return {"total_calls": 0}
        
        total_calls = len(self.call_history)
        successful_calls = sum(1 for call in self.call_history if call["success"])
        failed_calls = total_calls - successful_calls
        
        avg_execution_time = sum(call["execution_time"] for call in self.call_history) / total_calls
        
        server_stats = {}
        for call in self.call_history:
            server = call["server_name"]
            if server not in server_stats:
                server_stats[server] = {"calls": 0, "success": 0, "total_time": 0}
            
            server_stats[server]["calls"] += 1
            if call["success"]:
                server_stats[server]["success"] += 1
            server_stats[server]["total_time"] += call["execution_time"]
        
        return {
            "total_calls": total_calls,
            "successful_calls": successful_calls,
            "failed_calls": failed_calls,
            "success_rate": successful_calls / total_calls if total_calls > 0 else 0,
            "average_execution_time": avg_execution_time,
            "server_statistics": server_stats
        }

class MultFetchMCPClient:
    """Mult-Fetch MCP客户端"""
    
    def __init__(self):
        self.server_name = "mult-fetch-mcp-server"
        self.available_tools = [
            "fetch_html", "fetch_json", "fetch_txt", 
            "fetch_markdown", "fetch_plaintext"
        ]
    
    async def call_tool(self, tool_name: str, args: Dict[str, Any]) -> Any:
        """调用工具"""
        if tool_name not in self.available_tools:
            raise ValueError(f"不支持的工具: {tool_name}")
        
        # 这里应该是实际的MCP调用逻辑
        # 由于我们无法直接调用MCP服务器，这里提供模拟实现
        logger.info(f"调用 {self.server_name}.{tool_name} - 参数: {args}")
        
        if tool_name == "fetch_html":
            return await self._fetch_html(args)
        elif tool_name == "fetch_json":
            return await self._fetch_json(args)
        elif tool_name == "fetch_txt":
            return await self._fetch_txt(args)
        elif tool_name == "fetch_markdown":
            return await self._fetch_markdown(args)
        elif tool_name == "fetch_plaintext":
            return await self._fetch_plaintext(args)
        else:
            raise ValueError(f"未实现的工具: {tool_name}")
    
    async def _fetch_html(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """获取HTML内容"""
        url = args.get("url")
        if not url:
            raise ValueError("缺少URL参数")
        
        # 模拟HTML获取
        await asyncio.sleep(0.1)  # 模拟网络延迟
        
        return {
            "url": url,
            "content": f"<html><body>模拟HTML内容 for {url}</body></html>",
            "status_code": 200,
            "content_type": "text/html",
            "fetchedBytes": 1024
        }
    
    async def _fetch_json(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """获取JSON内容"""
        url = args.get("url")
        if not url:
            raise ValueError("缺少URL参数")
        
        await asyncio.sleep(0.1)
        
        return {
            "url": url,
            "content": {"message": f"模拟JSON数据 for {url}", "status": "success"},
            "status_code": 200,
            "content_type": "application/json",
            "fetchedBytes": 512
        }
    
    async def _fetch_txt(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """获取文本内容"""
        url = args.get("url")
        if not url:
            raise ValueError("缺少URL参数")
        
        await asyncio.sleep(0.1)
        
        return {
            "url": url,
            "content": f"模拟文本内容 for {url}\n这是一个示例文本响应。",
            "status_code": 200,
            "content_type": "text/plain",
            "fetchedBytes": 256
        }
    
    async def _fetch_markdown(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """获取Markdown内容"""
        url = args.get("url")
        if not url:
            raise ValueError("缺少URL参数")
        
        await asyncio.sleep(0.1)
        
        return {
            "url": url,
            "content": f"# 模拟Markdown内容\n\n这是为 {url} 生成的示例Markdown内容。\n\n## 特性\n\n- 功能1\n- 功能2",
            "status_code": 200,
            "content_type": "text/markdown",
            "fetchedBytes": 384
        }
    
    async def _fetch_plaintext(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """获取纯文本内容"""
        url = args.get("url")
        if not url:
            raise ValueError("缺少URL参数")
        
        await asyncio.sleep(0.1)
        
        return {
            "url": url,
            "content": f"模拟纯文本内容 for {url}\n\n这是去除HTML标签后的纯文本内容。",
            "status_code": 200,
            "content_type": "text/plain",
            "fetchedBytes": 320
        }

class Context7MCPClient:
    """Context7 MCP客户端"""
    
    def __init__(self):
        self.server_name = "context7-mcp"
        self.available_tools = ["resolve-library-id", "get-library-docs"]
        self.library_cache = {}
    
    async def call_tool(self, tool_name: str, args: Dict[str, Any]) -> Any:
        """调用工具"""
        if tool_name not in self.available_tools:
            raise ValueError(f"不支持的工具: {tool_name}")
        
        logger.info(f"调用 {self.server_name}.{tool_name} - 参数: {args}")
        
        if tool_name == "resolve-library-id":
            return await self._resolve_library_id(args)
        elif tool_name == "get-library-docs":
            return await self._get_library_docs(args)
        else:
            raise ValueError(f"未实现的工具: {tool_name}")
    
    async def _resolve_library_id(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """解析库ID"""
        library_name = args.get("libraryName")
        if not library_name:
            raise ValueError("缺少libraryName参数")
        
        await asyncio.sleep(0.1)
        
        # 模拟库ID解析
        library_mappings = {
            "react": "/facebook/react",
            "vue": "/vuejs/vue",
            "angular": "/angular/angular",
            "node.js": "/nodejs/node",
            "express": "/expressjs/express",
            "django": "/django/django",
            "flask": "/pallets/flask",
            "fastapi": "/tiangolo/fastapi",
            "spring": "/spring-projects/spring-framework",
            "python": "/python/cpython"
        }
        
        library_id = library_mappings.get(library_name.lower(), f"/unknown/{library_name}")
        
        return {
            "libraryName": library_name,
            "libraryId": library_id,
            "matches": [
                {
                    "id": library_id,
                    "name": library_name,
                    "description": f"官方{library_name}库文档",
                    "trustScore": 9,
                    "codeSnippetCount": 150
                }
            ]
        }
    
    async def _get_library_docs(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """获取库文档"""
        library_id = args.get("context7CompatibleLibraryID")
        topic = args.get("topic", "")
        tokens = args.get("tokens", 10000)
        
        if not library_id:
            raise ValueError("缺少context7CompatibleLibraryID参数")
        
        await asyncio.sleep(0.2)
        
        # 检查缓存
        cache_key = f"{library_id}:{topic}:{tokens}"
        if cache_key in self.library_cache:
            return self.library_cache[cache_key]
        
        # 模拟文档获取
        docs_content = f"""
# {library_id} 文档

## 概述
这是 {library_id} 的官方文档。

## 主要特性
- 高性能
- 易于使用
- 丰富的API

## 安装
```bash
npm install {library_id.split('/')[-1]}
```

## 基本用法
```javascript
const lib = require('{library_id.split('/')[-1]}');

// 基本示例
lib.init();
```

## API参考
### 核心方法
- `init()`: 初始化库
- `configure(options)`: 配置选项
- `process(data)`: 处理数据

## 示例代码
```javascript
// 完整示例
const {library_id.split('/')[-1]} = require('{library_id.split('/')[-1]}');

{library_id.split('/')[-1]}.init({{
  option1: 'value1',
  option2: 'value2'
}});

const result = {library_id.split('/')[-1]}.process(inputData);
console.log(result);
```

## 最佳实践
1. 始终进行错误处理
2. 使用适当的配置选项
3. 定期更新到最新版本

## 故障排除
常见问题和解决方案...
"""
        
        result = {
            "libraryId": library_id,
            "topic": topic,
            "documentation": docs_content,
            "tokenCount": min(len(docs_content.split()), tokens),
            "cacheHit": False
        }
        
        # 缓存结果
        self.library_cache[cache_key] = result
        
        return result

# 便捷函数
async def fetch_webpage_content(url: str, format_type: str = "html", **kwargs) -> MCPToolResult:
    """获取网页内容"""
    tools_manager = MCPToolsManager()
    
    tool_mapping = {
        "html": "fetch_html",
        "json": "fetch_json",
        "txt": "fetch_txt",
        "text": "fetch_txt",
        "markdown": "fetch_markdown",
        "md": "fetch_markdown",
        "plaintext": "fetch_plaintext",
        "plain": "fetch_plaintext"
    }
    
    tool_name = tool_mapping.get(format_type.lower(), "fetch_html")
    
    args = {"url": url, "startCursor": 0, **kwargs}
    
    return await tools_manager.call_tool("mult-fetch-mcp-server", tool_name, args)

async def get_library_documentation(library_name: str, topic: str = "", max_tokens: int = 10000) -> MCPToolResult:
    """获取库文档"""
    tools_manager = MCPToolsManager()
    
    # 首先解析库ID
    resolve_result = await tools_manager.call_tool(
        "context7-mcp", 
        "resolve-library-id", 
        {"libraryName": library_name}
    )
    
    if not resolve_result.success:
        return resolve_result
    
    library_id = resolve_result.data.get("libraryId")
    if not library_id:
        return MCPToolResult(
            success=False,
            error="无法解析库ID",
            server_name="context7-mcp",
            tool_name="resolve-library-id"
        )
    
    # 获取文档
    docs_args = {
        "context7CompatibleLibraryID": library_id,
        "topic": topic,
        "tokens": max_tokens
    }
    
    return await tools_manager.call_tool("context7-mcp", "get-library-docs", docs_args)

# 创建全局工具管理器实例
_tools_manager = None

def get_tools_manager() -> MCPToolsManager:
    """获取工具管理器实例（单例模式）"""
    global _tools_manager
    if _tools_manager is None:
        _tools_manager = MCPToolsManager()
    return _tools_manager