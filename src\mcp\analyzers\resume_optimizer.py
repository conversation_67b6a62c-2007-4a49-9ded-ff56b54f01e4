#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简历优化模块 - 基于MCP的智能简历优化

此模块提供智能简历优化功能，包括：
- 简历内容分析
- 关键词优化
- ATS兼容性检查
- 职位匹配优化
- 格式和结构优化
- 个性化建议

作者: AI Assistant
创建时间: 2024
"""

import asyncio
import re
import json
import time
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
import logging
from pathlib import Path

# 导入MCP管理器
try:
    from src.mcp.core.mcp_manager import get_mcp_manager, MCPToolResult
except ImportError:
    # 如果导入失败，创建模拟实现
    class MCPToolResult:
        def __init__(self, success=True, result=None, error=None):
            self.success = success
            self.result = result
            self.error = error
    
    def get_mcp_manager():
        return None

# 导入职位分析器
try:
    from src.mcp.analyzers.job_analyzer import JobAnalysisResult, SkillRequirement
except ImportError:
    # 创建模拟类
    @dataclass
    class JobAnalysisResult:
        job_title: str = ""
        required_skills: List = field(default_factory=list)
        preferred_skills: List = field(default_factory=list)
    
    @dataclass
    class SkillRequirement:
        name: str = ""
        importance: str = ""

# 导入现有模块（保持兼容性）
try:
    from src.utils.logger import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)

try:
    from src.utils.config_loader import ConfigLoader
except ImportError:
    class ConfigLoader:
        @staticmethod
        def load_config(config_path: str) -> Dict[str, Any]:
            return {}


class OptimizationLevel(Enum):
    """优化级别"""
    BASIC = "basic"          # 基础优化
    STANDARD = "standard"    # 标准优化
    ADVANCED = "advanced"    # 高级优化
    EXPERT = "expert"        # 专家级优化


class ResumeSection(Enum):
    """简历章节"""
    CONTACT_INFO = "contact_info"
    SUMMARY = "summary"
    EXPERIENCE = "experience"
    EDUCATION = "education"
    SKILLS = "skills"
    PROJECTS = "projects"
    CERTIFICATIONS = "certifications"
    ACHIEVEMENTS = "achievements"
    LANGUAGES = "languages"
    INTERESTS = "interests"


class OptimizationType(Enum):
    """优化类型"""
    KEYWORD_OPTIMIZATION = "keyword_optimization"
    ATS_COMPATIBILITY = "ats_compatibility"
    CONTENT_ENHANCEMENT = "content_enhancement"
    STRUCTURE_IMPROVEMENT = "structure_improvement"
    FORMATTING = "formatting"
    PERSONALIZATION = "personalization"


@dataclass
class OptimizationSuggestion:
    """优化建议数据类"""
    section: ResumeSection
    optimization_type: OptimizationType
    priority: str  # high, medium, low
    title: str
    description: str
    original_content: str = ""
    suggested_content: str = ""
    reasoning: str = ""
    impact_score: float = 0.0
    implementation_difficulty: str = "easy"  # easy, medium, hard
    keywords_added: List[str] = field(default_factory=list)
    ats_improvement: bool = False


@dataclass
class ATSCompatibilityReport:
    """ATS兼容性报告"""
    overall_score: float = 0.0
    keyword_density: float = 0.0
    format_compatibility: float = 0.0
    structure_score: float = 0.0
    readability_score: float = 0.0
    
    issues: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    
    # 详细分析
    missing_keywords: List[str] = field(default_factory=list)
    overused_keywords: List[str] = field(default_factory=list)
    formatting_issues: List[str] = field(default_factory=list)
    structure_issues: List[str] = field(default_factory=list)


@dataclass
class ResumeAnalysis:
    """简历分析结果"""
    # 基础信息
    total_words: int = 0
    total_sections: int = 0
    estimated_reading_time: float = 0.0
    
    # 内容分析
    keyword_frequency: Dict[str, int] = field(default_factory=dict)
    skill_mentions: Dict[str, int] = field(default_factory=dict)
    action_verbs: List[str] = field(default_factory=list)
    quantified_achievements: List[str] = field(default_factory=list)
    
    # 结构分析
    section_lengths: Dict[str, int] = field(default_factory=dict)
    bullet_point_count: int = 0
    formatting_consistency: float = 0.0
    
    # 质量评分
    content_quality_score: float = 0.0
    keyword_optimization_score: float = 0.0
    structure_score: float = 0.0
    overall_score: float = 0.0


@dataclass
class ResumeOptimizationResult:
    """简历优化结果数据类"""
    resume_id: str
    optimization_level: OptimizationLevel
    timestamp: float = field(default_factory=time.time)
    
    # 原始分析
    original_analysis: Optional[ResumeAnalysis] = None
    
    # 职位匹配分析
    job_match_score: float = 0.0
    target_job_title: str = ""
    
    # ATS兼容性
    ats_report: Optional[ATSCompatibilityReport] = None
    
    # 优化建议
    suggestions: List[OptimizationSuggestion] = field(default_factory=list)
    
    # 优化后预期改进
    expected_improvements: Dict[str, float] = field(default_factory=dict)
    
    # 关键词分析
    recommended_keywords: List[str] = field(default_factory=list)
    keywords_to_remove: List[str] = field(default_factory=list)
    
    # 元数据
    optimization_duration: float = 0.0
    data_sources: List[str] = field(default_factory=list)
    confidence_score: float = 0.0


class ResumeOptimizer:
    """简历优化器主类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化简历优化器
        
        Args:
            config: 优化器配置
        """
        self.config = config or {}
        self.mcp_manager = get_mcp_manager()
        
        # 加载优化规则和模板
        self.optimization_rules = self._load_optimization_rules()
        self.ats_keywords = self._load_ats_keywords()
        self.action_verbs = self._load_action_verbs()
        
        logger.info("简历优化器初始化完成")
    
    def _load_optimization_rules(self) -> Dict[str, Any]:
        """加载优化规则"""
        return {
            'keyword_density': {
                'min_density': 0.02,  # 2%
                'max_density': 0.05,  # 5%
                'target_density': 0.03  # 3%
            },
            'section_lengths': {
                'summary': {'min': 50, 'max': 150, 'target': 100},
                'experience': {'min': 100, 'max': 300, 'target': 200},
                'skills': {'min': 20, 'max': 100, 'target': 50}
            },
            'bullet_points': {
                'min_per_job': 2,
                'max_per_job': 5,
                'target_per_job': 3
            },
            'quantification': {
                'target_percentage': 0.6  # 60%的成就应该量化
            }
        }
    
    def _load_ats_keywords(self) -> Dict[str, List[str]]:
        """加载ATS关键词库"""
        return {
            'technical_skills': [
                'python', 'java', 'javascript', 'react', 'angular', 'vue',
                'django', 'flask', 'spring', 'node.js', 'express',
                'mysql', 'postgresql', 'mongodb', 'redis',
                'aws', 'azure', 'gcp', 'docker', 'kubernetes',
                'git', 'jenkins', 'ci/cd', 'agile', 'scrum'
            ],
            'soft_skills': [
                'leadership', 'communication', 'teamwork', 'problem-solving',
                'analytical', 'creative', 'adaptable', 'detail-oriented',
                'self-motivated', 'collaborative', 'innovative'
            ],
            'action_verbs': [
                'achieved', 'developed', 'implemented', 'managed', 'led',
                'created', 'designed', 'optimized', 'improved', 'increased',
                'reduced', 'streamlined', 'collaborated', 'coordinated'
            ],
            'industry_terms': {
                'software_development': [
                    'software development', 'full-stack', 'backend', 'frontend',
                    'api development', 'microservices', 'database design',
                    'code review', 'testing', 'debugging', 'deployment'
                ],
                'data_science': [
                    'machine learning', 'data analysis', 'statistical modeling',
                    'data visualization', 'big data', 'predictive analytics',
                    'deep learning', 'neural networks', 'data mining'
                ],
                'project_management': [
                    'project management', 'stakeholder management', 'risk management',
                    'budget management', 'timeline management', 'resource allocation',
                    'cross-functional teams', 'deliverables', 'milestones'
                ]
            }
        }
    
    def _load_action_verbs(self) -> List[str]:
        """加载行动动词库"""
        return [
            'accelerated', 'accomplished', 'achieved', 'acquired', 'adapted',
            'addressed', 'administered', 'advanced', 'analyzed', 'appointed',
            'architected', 'assembled', 'assessed', 'assisted', 'attained',
            'automated', 'awarded', 'built', 'calculated', 'collaborated',
            'collected', 'communicated', 'compiled', 'completed', 'composed',
            'computed', 'conceived', 'conducted', 'configured', 'constructed',
            'consulted', 'coordinated', 'created', 'customized', 'debugged',
            'delivered', 'demonstrated', 'designed', 'developed', 'devised',
            'diagnosed', 'directed', 'documented', 'drove', 'educated',
            'eliminated', 'enhanced', 'established', 'evaluated', 'executed',
            'expanded', 'expedited', 'facilitated', 'formulated', 'generated',
            'guided', 'identified', 'implemented', 'improved', 'increased',
            'initiated', 'innovated', 'integrated', 'introduced', 'investigated',
            'launched', 'led', 'maintained', 'managed', 'maximized',
            'mentored', 'migrated', 'minimized', 'modernized', 'monitored',
            'negotiated', 'optimized', 'orchestrated', 'organized', 'overhauled',
            'participated', 'performed', 'pioneered', 'planned', 'presented',
            'prioritized', 'produced', 'programmed', 'promoted', 'proposed',
            'provided', 'published', 'recommended', 'reduced', 'refined',
            'reorganized', 'replaced', 'researched', 'resolved', 'restructured',
            'revamped', 'reviewed', 'revised', 'scaled', 'scheduled',
            'simplified', 'solved', 'spearheaded', 'standardized', 'streamlined',
            'strengthened', 'supervised', 'supported', 'surpassed', 'synthesized',
            'systematized', 'trained', 'transformed', 'translated', 'updated',
            'upgraded', 'utilized', 'validated', 'visualized'
        ]
    
    async def optimize_resume(self, resume_content: str, 
                             target_job: Optional[JobAnalysisResult] = None,
                             optimization_level: OptimizationLevel = OptimizationLevel.STANDARD,
                             user_preferences: Optional[Dict[str, Any]] = None) -> ResumeOptimizationResult:
        """优化简历
        
        Args:
            resume_content: 简历内容
            target_job: 目标职位分析结果
            optimization_level: 优化级别
            user_preferences: 用户偏好设置
        
        Returns:
            ResumeOptimizationResult: 优化结果
        """
        start_time = time.time()
        
        logger.info(f"开始优化简历 - 优化级别: {optimization_level.value}")
        
        # 创建优化结果对象
        result = ResumeOptimizationResult(
            resume_id=str(int(time.time())),
            optimization_level=optimization_level,
            target_job_title=target_job.job_title if target_job else ""
        )
        
        try:
            # 1. 分析原始简历
            result.original_analysis = await self._analyze_resume_content(resume_content)
            
            # 2. ATS兼容性检查
            result.ats_report = await self._check_ats_compatibility(resume_content, target_job)
            
            # 3. 职位匹配分析
            if target_job:
                result.job_match_score = await self._calculate_job_match_score(resume_content, target_job)
            
            # 4. 生成优化建议
            await self._generate_optimization_suggestions(result, resume_content, target_job, user_preferences)
            
            # 5. 关键词优化分析
            await self._analyze_keyword_optimization(result, resume_content, target_job)
            
            # 6. 预测优化效果
            self._calculate_expected_improvements(result)
            
            # 7. 高级优化（根据级别）
            if optimization_level in [OptimizationLevel.ADVANCED, OptimizationLevel.EXPERT]:
                await self._advanced_optimization(result, resume_content, target_job)
            
            if optimization_level == OptimizationLevel.EXPERT:
                await self._expert_optimization(result, resume_content, target_job)
            
            result.optimization_duration = time.time() - start_time
            result.confidence_score = self._calculate_confidence_score(result)
            
            logger.info(f"简历优化完成 (耗时: {result.optimization_duration:.2f}s)")
            
            return result
            
        except Exception as e:
            logger.error(f"简历优化失败: {e}")
            result.optimization_duration = time.time() - start_time
            return result
    
    async def _analyze_resume_content(self, resume_content: str) -> ResumeAnalysis:
        """分析简历内容"""
        analysis = ResumeAnalysis()
        
        # 基础统计
        words = resume_content.split()
        analysis.total_words = len(words)
        analysis.estimated_reading_time = len(words) / 200  # 假设每分钟200词
        
        # 章节分析
        sections = self._extract_resume_sections(resume_content)
        analysis.total_sections = len(sections)
        
        for section_name, content in sections.items():
            analysis.section_lengths[section_name] = len(content.split())
        
        # 关键词频率分析
        analysis.keyword_frequency = self._analyze_keyword_frequency(resume_content)
        
        # 技能提及分析
        analysis.skill_mentions = self._analyze_skill_mentions(resume_content)
        
        # 行动动词分析
        analysis.action_verbs = self._extract_action_verbs(resume_content)
        
        # 量化成就分析
        analysis.quantified_achievements = self._extract_quantified_achievements(resume_content)
        
        # 项目符号统计
        analysis.bullet_point_count = len(re.findall(r'^\s*[•\-\*]', resume_content, re.MULTILINE))
        
        # 格式一致性评分
        analysis.formatting_consistency = self._evaluate_formatting_consistency(resume_content)
        
        # 质量评分
        analysis.content_quality_score = self._calculate_content_quality_score(analysis)
        analysis.keyword_optimization_score = self._calculate_keyword_optimization_score(analysis)
        analysis.structure_score = self._calculate_structure_score(analysis)
        analysis.overall_score = (analysis.content_quality_score + 
                                analysis.keyword_optimization_score + 
                                analysis.structure_score) / 3
        
        return analysis
    
    def _extract_resume_sections(self, resume_content: str) -> Dict[str, str]:
        """提取简历章节"""
        sections = {}
        
        # 常见章节标题模式
        section_patterns = {
            'summary': r'(summary|profile|objective|about)\s*:?\s*\n([^\n]*(?:\n(?!\w+\s*:)[^\n]*)*)',
            'experience': r'(experience|work history|employment)\s*:?\s*\n([^\n]*(?:\n(?!\w+\s*:)[^\n]*)*)',
            'education': r'(education|academic)\s*:?\s*\n([^\n]*(?:\n(?!\w+\s*:)[^\n]*)*)',
            'skills': r'(skills|technical skills|competencies)\s*:?\s*\n([^\n]*(?:\n(?!\w+\s*:)[^\n]*)*)',
            'projects': r'(projects|portfolio)\s*:?\s*\n([^\n]*(?:\n(?!\w+\s*:)[^\n]*)*)',
            'certifications': r'(certifications?|licenses?)\s*:?\s*\n([^\n]*(?:\n(?!\w+\s*:)[^\n]*)*)',
        }
        
        for section_name, pattern in section_patterns.items():
            match = re.search(pattern, resume_content, re.IGNORECASE | re.MULTILINE)
            if match:
                sections[section_name] = match.group(2).strip()
        
        return sections
    
    def _analyze_keyword_frequency(self, content: str) -> Dict[str, int]:
        """分析关键词频率"""
        frequency = {}
        content_lower = content.lower()
        
        # 分析技术关键词
        for category, keywords in self.ats_keywords.items():
            if isinstance(keywords, list):
                for keyword in keywords:
                    count = len(re.findall(r'\b' + re.escape(keyword.lower()) + r'\b', content_lower))
                    if count > 0:
                        frequency[keyword] = count
            elif isinstance(keywords, dict):
                for subcategory, subkeywords in keywords.items():
                    for keyword in subkeywords:
                        count = len(re.findall(r'\b' + re.escape(keyword.lower()) + r'\b', content_lower))
                        if count > 0:
                            frequency[keyword] = count
        
        return frequency
    
    def _analyze_skill_mentions(self, content: str) -> Dict[str, int]:
        """分析技能提及次数"""
        skill_mentions = {}
        content_lower = content.lower()
        
        # 技能关键词
        all_skills = []
        for keywords in self.ats_keywords.values():
            if isinstance(keywords, list):
                all_skills.extend(keywords)
            elif isinstance(keywords, dict):
                for subkeywords in keywords.values():
                    all_skills.extend(subkeywords)
        
        for skill in all_skills:
            count = len(re.findall(r'\b' + re.escape(skill.lower()) + r'\b', content_lower))
            if count > 0:
                skill_mentions[skill] = count
        
        return skill_mentions
    
    def _extract_action_verbs(self, content: str) -> List[str]:
        """提取行动动词"""
        found_verbs = []
        content_lower = content.lower()
        
        for verb in self.action_verbs:
            if re.search(r'\b' + re.escape(verb.lower()) + r'\b', content_lower):
                found_verbs.append(verb)
        
        return found_verbs
    
    def _extract_quantified_achievements(self, content: str) -> List[str]:
        """提取量化成就"""
        quantified = []
        
        # 查找包含数字的句子
        sentences = re.split(r'[.!?\n]', content)
        
        for sentence in sentences:
            # 查找包含数字、百分比、货币等的句子
            if re.search(r'\d+[%$]?|\$\d+|\d+\s*(million|thousand|k\b)', sentence, re.IGNORECASE):
                quantified.append(sentence.strip())
        
        return quantified
    
    def _evaluate_formatting_consistency(self, content: str) -> float:
        """评估格式一致性"""
        consistency_score = 1.0
        
        # 检查项目符号一致性
        bullet_patterns = re.findall(r'^\s*([•\-\*])', content, re.MULTILINE)
        if bullet_patterns:
            unique_bullets = set(bullet_patterns)
            if len(unique_bullets) > 1:
                consistency_score -= 0.2
        
        # 检查日期格式一致性
        date_patterns = re.findall(r'\b\d{4}\b|\b\d{1,2}/\d{4}\b|\b\w+\s+\d{4}\b', content)
        if len(set(date_patterns)) > 3:  # 允许一些变化
            consistency_score -= 0.2
        
        # 检查标题格式一致性
        title_patterns = re.findall(r'^([A-Z][A-Z\s]+)$', content, re.MULTILINE)
        if len(title_patterns) > 1:
            # 检查是否都是大写
            all_upper = all(title.isupper() for title in title_patterns)
            if not all_upper:
                consistency_score -= 0.2
        
        return max(consistency_score, 0.0)
    
    def _calculate_content_quality_score(self, analysis: ResumeAnalysis) -> float:
        """计算内容质量评分"""
        score = 0.0
        
        # 行动动词使用
        if len(analysis.action_verbs) >= 10:
            score += 0.3
        elif len(analysis.action_verbs) >= 5:
            score += 0.2
        else:
            score += 0.1
        
        # 量化成就
        if len(analysis.quantified_achievements) >= 5:
            score += 0.3
        elif len(analysis.quantified_achievements) >= 3:
            score += 0.2
        else:
            score += 0.1
        
        # 词汇多样性
        unique_words = len(set(analysis.keyword_frequency.keys()))
        if unique_words >= 20:
            score += 0.2
        elif unique_words >= 10:
            score += 0.15
        else:
            score += 0.1
        
        # 长度适中
        if 300 <= analysis.total_words <= 600:
            score += 0.2
        elif 200 <= analysis.total_words <= 800:
            score += 0.15
        else:
            score += 0.05
        
        return min(score, 1.0)
    
    def _calculate_keyword_optimization_score(self, analysis: ResumeAnalysis) -> float:
        """计算关键词优化评分"""
        if not analysis.keyword_frequency:
            return 0.0
        
        total_keywords = sum(analysis.keyword_frequency.values())
        keyword_density = total_keywords / max(analysis.total_words, 1)
        
        target_density = self.optimization_rules['keyword_density']['target_density']
        min_density = self.optimization_rules['keyword_density']['min_density']
        max_density = self.optimization_rules['keyword_density']['max_density']
        
        if min_density <= keyword_density <= max_density:
            # 在理想范围内
            distance_from_target = abs(keyword_density - target_density)
            score = 1.0 - (distance_from_target / target_density)
        elif keyword_density < min_density:
            # 密度过低
            score = keyword_density / min_density * 0.5
        else:
            # 密度过高
            score = max_density / keyword_density * 0.5
        
        return max(min(score, 1.0), 0.0)
    
    def _calculate_structure_score(self, analysis: ResumeAnalysis) -> float:
        """计算结构评分"""
        score = 0.0
        
        # 章节完整性
        required_sections = ['experience', 'skills']
        present_sections = [s for s in required_sections if s in analysis.section_lengths]
        score += (len(present_sections) / len(required_sections)) * 0.4
        
        # 章节长度适中
        section_score = 0.0
        for section, length in analysis.section_lengths.items():
            if section in self.optimization_rules['section_lengths']:
                target = self.optimization_rules['section_lengths'][section]['target']
                min_len = self.optimization_rules['section_lengths'][section]['min']
                max_len = self.optimization_rules['section_lengths'][section]['max']
                
                if min_len <= length <= max_len:
                    section_score += 1.0
                else:
                    # 计算偏离程度
                    if length < min_len:
                        section_score += length / min_len
                    else:
                        section_score += max_len / length
        
        if analysis.section_lengths:
            score += (section_score / len(analysis.section_lengths)) * 0.3
        
        # 项目符号使用
        if analysis.bullet_point_count >= 5:
            score += 0.2
        elif analysis.bullet_point_count >= 3:
            score += 0.15
        else:
            score += 0.05
        
        # 格式一致性
        score += analysis.formatting_consistency * 0.1
        
        return min(score, 1.0)
    
    async def _check_ats_compatibility(self, resume_content: str, target_job: Optional[JobAnalysisResult]) -> ATSCompatibilityReport:
        """检查ATS兼容性"""
        report = ATSCompatibilityReport()
        
        # 关键词密度分析
        keyword_freq = self._analyze_keyword_frequency(resume_content)
        total_words = len(resume_content.split())
        total_keywords = sum(keyword_freq.values())
        
        if total_words > 0:
            report.keyword_density = total_keywords / total_words
        
        # 格式兼容性检查
        report.format_compatibility = self._check_format_compatibility(resume_content)
        
        # 结构评分
        report.structure_score = self._evaluate_ats_structure(resume_content)
        
        # 可读性评分
        report.readability_score = self._calculate_readability_score(resume_content)
        
        # 综合评分
        report.overall_score = (
            report.keyword_density * 0.3 +
            report.format_compatibility * 0.25 +
            report.structure_score * 0.25 +
            report.readability_score * 0.2
        )
        
        # 生成问题和建议
        await self._generate_ats_issues_and_recommendations(report, resume_content, target_job)
        
        return report
    
    def _check_format_compatibility(self, content: str) -> float:
        """检查格式兼容性"""
        score = 1.0
        
        # 检查特殊字符
        special_chars = re.findall(r'[^\w\s\-.,;:()\[\]{}"\'/\\@#$%&*+=<>?!~`|]', content)
        if special_chars:
            score -= 0.2
            
        # 检查表格或复杂格式
        if re.search(r'\|.*\|', content):  # 简单表格检测
            score -= 0.3
            
        # 检查过多的格式化
        if len(re.findall(r'\s{3,}', content)) > 10:  # 过多空格
            score -= 0.1
            
        return max(score, 0.0)
    
    def _evaluate_ats_structure(self, content: str) -> float:
        """评估ATS结构"""
        score = 0.0
        
        # 检查标准章节
        standard_sections = ['experience', 'education', 'skills']
        found_sections = 0
        
        for section in standard_sections:
            if re.search(rf'\b{section}\b', content, re.IGNORECASE):
                found_sections += 1
        
        score += (found_sections / len(standard_sections)) * 0.5
        
        # 检查联系信息
        if re.search(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', content):
            score += 0.2
        
        if re.search(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b', content):
            score += 0.2
        
        # 检查清晰的层次结构
        if len(re.findall(r'^[A-Z][A-Z\s]+$', content, re.MULTILINE)) >= 2:
            score += 0.1
        
        return min(score, 1.0)
    
    def _calculate_readability_score(self, content: str) -> float:
        """计算可读性评分"""
        sentences = re.split(r'[.!?]', content)
        words = content.split()
        
        if not sentences or not words:
            return 0.0
        
        avg_sentence_length = len(words) / len(sentences)
        
        # 理想句子长度为15-20词
        if 15 <= avg_sentence_length <= 20:
            length_score = 1.0
        elif 10 <= avg_sentence_length <= 25:
            length_score = 0.8
        else:
            length_score = 0.5
        
        # 检查段落结构
        paragraphs = content.split('\n\n')
        if len(paragraphs) >= 3:
            structure_score = 1.0
        elif len(paragraphs) >= 2:
            structure_score = 0.7
        else:
            structure_score = 0.4
        
        return (length_score + structure_score) / 2
    
    async def _generate_ats_issues_and_recommendations(self, report: ATSCompatibilityReport, 
                                                      content: str, target_job: Optional[JobAnalysisResult]) -> None:
        """生成ATS问题和建议"""
        # 关键词密度问题
        target_density = self.optimization_rules['keyword_density']['target_density']
        if report.keyword_density < target_density * 0.7:
            report.issues.append("关键词密度过低，可能影响ATS识别")
            report.recommendations.append("增加相关技术关键词的使用频率")
        elif report.keyword_density > target_density * 1.5:
            report.issues.append("关键词密度过高，可能被视为关键词堆砌")
            report.recommendations.append("减少重复关键词，使用同义词替换")
        
        # 格式问题
        if report.format_compatibility < 0.8:
            report.issues.append("简历格式可能不兼容ATS系统")
            report.recommendations.append("使用标准格式，避免复杂表格和特殊字符")
        
        # 结构问题
        if report.structure_score < 0.7:
            report.issues.append("简历结构不够清晰")
            report.recommendations.append("使用标准章节标题，确保层次结构清晰")
        
        # 可读性问题
        if report.readability_score < 0.6:
            report.issues.append("简历可读性需要改善")
            report.recommendations.append("优化句子长度和段落结构")
        
        # 基于目标职位的特定建议
        if target_job:
            await self._generate_job_specific_ats_recommendations(report, content, target_job)
    
    async def _generate_job_specific_ats_recommendations(self, report: ATSCompatibilityReport,
                                                        content: str, target_job: JobAnalysisResult) -> None:
        """生成针对特定职位的ATS建议"""
        # 分析目标职位的关键词
        job_keywords = set()
        for skill in target_job.required_skills + target_job.preferred_skills:
            job_keywords.add(skill.name.lower())
        
        # 检查简历中缺失的关键词
        content_lower = content.lower()
        missing_keywords = []
        
        for keyword in job_keywords:
            if keyword not in content_lower:
                missing_keywords.append(keyword)
        
        if missing_keywords:
            report.missing_keywords = missing_keywords[:10]  # 限制数量
            report.recommendations.append(f"考虑添加以下关键词: {', '.join(missing_keywords[:5])}")
        
        # 检查过度使用的关键词
        keyword_freq = self._analyze_keyword_frequency(content)
        overused = [k for k, v in keyword_freq.items() if v > 5]
        
        if overused:
            report.overused_keywords = overused
            report.recommendations.append(f"以下关键词使用过度: {', '.join(overused[:3])}")
    
    async def _calculate_job_match_score(self, resume_content: str, target_job: JobAnalysisResult) -> float:
        """计算职位匹配度"""
        if not target_job:
            return 0.0
        
        content_lower = resume_content.lower()
        
        # 必需技能匹配
        required_skills = set(skill.name.lower() for skill in target_job.required_skills)
        matched_required = sum(1 for skill in required_skills if skill in content_lower)
        required_score = matched_required / max(len(required_skills), 1)
        
        # 优选技能匹配
        preferred_skills = set(skill.name.lower() for skill in target_job.preferred_skills)
        matched_preferred = sum(1 for skill in preferred_skills if skill in content_lower)
        preferred_score = matched_preferred / max(len(preferred_skills), 1)
        
        # 综合评分
        match_score = required_score * 0.7 + preferred_score * 0.3
        
        return min(match_score, 1.0)
    
    async def _generate_optimization_suggestions(self, result: ResumeOptimizationResult,
                                               resume_content: str, target_job: Optional[JobAnalysisResult],
                                               user_preferences: Optional[Dict[str, Any]]) -> None:
        """生成优化建议"""
        suggestions = []
        
        # 关键词优化建议
        keyword_suggestions = await self._generate_keyword_suggestions(resume_content, target_job)
        suggestions.extend(keyword_suggestions)
        
        # 内容增强建议
        content_suggestions = await self._generate_content_suggestions(result.original_analysis, target_job)
        suggestions.extend(content_suggestions)
        
        # 结构改进建议
        structure_suggestions = await self._generate_structure_suggestions(result.original_analysis)
        suggestions.extend(structure_suggestions)
        
        # ATS优化建议
        if result.ats_report:
            ats_suggestions = await self._generate_ats_suggestions(result.ats_report)
            suggestions.extend(ats_suggestions)
        
        # 根据用户偏好过滤和排序
        if user_preferences:
            suggestions = self._filter_suggestions_by_preferences(suggestions, user_preferences)
        
        # 按优先级排序
        suggestions.sort(key=lambda x: self._get_priority_score(x.priority), reverse=True)
        
        result.suggestions = suggestions
    
    async def _generate_keyword_suggestions(self, content: str, target_job: Optional[JobAnalysisResult]) -> List[OptimizationSuggestion]:
        """生成关键词优化建议"""
        suggestions = []
        
        if not target_job:
            return suggestions
        
        content_lower = content.lower()
        
        # 分析缺失的关键技能
        missing_skills = []
        for skill in target_job.required_skills:
            if skill.name.lower() not in content_lower:
                missing_skills.append(skill.name)
        
        if missing_skills:
            suggestion = OptimizationSuggestion(
                section=ResumeSection.SKILLS,
                optimization_type=OptimizationType.KEYWORD_OPTIMIZATION,
                priority="high",
                title="添加缺失的必需技能",
                description=f"简历中缺少以下必需技能: {', '.join(missing_skills[:5])}",
                reasoning="这些技能是职位要求的必需技能，添加后可显著提高ATS匹配度",
                impact_score=0.8,
                keywords_added=missing_skills[:5],
                ats_improvement=True
            )
            suggestions.append(suggestion)
        
        # 分析优选技能
        missing_preferred = []
        for skill in target_job.preferred_skills:
            if skill.name.lower() not in content_lower:
                missing_preferred.append(skill.name)
        
        if missing_preferred:
            suggestion = OptimizationSuggestion(
                section=ResumeSection.SKILLS,
                optimization_type=OptimizationType.KEYWORD_OPTIMIZATION,
                priority="medium",
                title="添加优选技能",
                description=f"考虑添加以下优选技能: {', '.join(missing_preferred[:3])}",
                reasoning="这些技能虽非必需，但可以增加竞争优势",
                impact_score=0.5,
                keywords_added=missing_preferred[:3],
                ats_improvement=True
            )
            suggestions.append(suggestion)
        
        return suggestions
    
    async def _generate_content_suggestions(self, analysis: ResumeAnalysis, target_job: Optional[JobAnalysisResult]) -> List[OptimizationSuggestion]:
        """生成内容增强建议"""
        suggestions = []
        
        # 量化成就建议
        if len(analysis.quantified_achievements) < 3:
            suggestion = OptimizationSuggestion(
                section=ResumeSection.EXPERIENCE,
                optimization_type=OptimizationType.CONTENT_ENHANCEMENT,
                priority="high",
                title="增加量化成就",
                description="添加更多包含具体数字、百分比或金额的成就描述",
                reasoning="量化的成就更有说服力，能够具体展示您的贡献和影响",
                impact_score=0.7,
                implementation_difficulty="medium"
            )
            suggestions.append(suggestion)
        
        # 行动动词建议
        if len(analysis.action_verbs) < 8:
            suggestion = OptimizationSuggestion(
                section=ResumeSection.EXPERIENCE,
                optimization_type=OptimizationType.CONTENT_ENHANCEMENT,
                priority="medium",
                title="使用更多行动动词",
                description="在工作经历中使用更多强有力的行动动词开头",
                reasoning="行动动词能够更好地展示您的主动性和执行能力",
                impact_score=0.5,
                implementation_difficulty="easy"
            )
            suggestions.append(suggestion)
        
        # 技能章节建议
        if 'skills' not in analysis.section_lengths or analysis.section_lengths['skills'] < 20:
            suggestion = OptimizationSuggestion(
                section=ResumeSection.SKILLS,
                optimization_type=OptimizationType.CONTENT_ENHANCEMENT,
                priority="high",
                title="扩展技能章节",
                description="详细列出您的技术技能、工具和方法论",
                reasoning="完整的技能列表有助于ATS识别和HR筛选",
                impact_score=0.8,
                implementation_difficulty="easy"
            )
            suggestions.append(suggestion)
        
        return suggestions
    
    async def _generate_structure_suggestions(self, analysis: ResumeAnalysis) -> List[OptimizationSuggestion]:
        """生成结构改进建议"""
        suggestions = []
        
        # 章节顺序建议
        if analysis.total_sections < 4:
            suggestion = OptimizationSuggestion(
                section=ResumeSection.SUMMARY,
                optimization_type=OptimizationType.STRUCTURE_IMPROVEMENT,
                priority="medium",
                title="添加缺失章节",
                description="考虑添加专业总结、项目经历或认证章节",
                reasoning="完整的章节结构能够全面展示您的背景和能力",
                impact_score=0.6,
                implementation_difficulty="medium"
            )
            suggestions.append(suggestion)
        
        # 项目符号建议
        if analysis.bullet_point_count < 5:
            suggestion = OptimizationSuggestion(
                section=ResumeSection.EXPERIENCE,
                optimization_type=OptimizationType.STRUCTURE_IMPROVEMENT,
                priority="medium",
                title="使用项目符号",
                description="在工作经历中使用项目符号来组织信息",
                reasoning="项目符号使内容更易读，便于快速扫描",
                impact_score=0.4,
                implementation_difficulty="easy"
            )
            suggestions.append(suggestion)
        
        # 格式一致性建议
        if analysis.formatting_consistency < 0.8:
            suggestion = OptimizationSuggestion(
                section=ResumeSection.SUMMARY,
                optimization_type=OptimizationType.FORMATTING,
                priority="low",
                title="改善格式一致性",
                description="统一日期格式、项目符号样式和标题格式",
                reasoning="一致的格式显示专业性和注重细节",
                impact_score=0.3,
                implementation_difficulty="easy"
            )
            suggestions.append(suggestion)
        
        return suggestions
    
    async def _generate_ats_suggestions(self, ats_report: ATSCompatibilityReport) -> List[OptimizationSuggestion]:
        """生成ATS优化建议"""
        suggestions = []
        
        # 基于ATS报告的问题生成建议
        for issue in ats_report.issues:
            if "关键词密度" in issue:
                suggestion = OptimizationSuggestion(
                    section=ResumeSection.SKILLS,
                    optimization_type=OptimizationType.ATS_COMPATIBILITY,
                    priority="high",
                    title="优化关键词密度",
                    description=issue,
                    reasoning="适当的关键词密度有助于ATS系统识别和排名",
                    impact_score=0.7,
                    ats_improvement=True
                )
                suggestions.append(suggestion)
            
            elif "格式" in issue:
                suggestion = OptimizationSuggestion(
                    section=ResumeSection.SUMMARY,
                    optimization_type=OptimizationType.ATS_COMPATIBILITY,
                    priority="high",
                    title="改善ATS格式兼容性",
                    description=issue,
                    reasoning="ATS兼容的格式确保简历能被正确解析",
                    impact_score=0.8,
                    ats_improvement=True
                )
                suggestions.append(suggestion)
        
        return suggestions
    
    def _filter_suggestions_by_preferences(self, suggestions: List[OptimizationSuggestion], 
                                         preferences: Dict[str, Any]) -> List[OptimizationSuggestion]:
        """根据用户偏好过滤建议"""
        filtered = []
        
        # 优先级过滤
        min_priority = preferences.get('min_priority', 'low')
        priority_order = {'high': 3, 'medium': 2, 'low': 1}
        min_priority_score = priority_order.get(min_priority, 1)
        
        for suggestion in suggestions:
            suggestion_priority_score = priority_order.get(suggestion.priority, 1)
            if suggestion_priority_score >= min_priority_score:
                filtered.append(suggestion)
        
        # 实施难度过滤
        max_difficulty = preferences.get('max_difficulty', 'hard')
        difficulty_order = {'easy': 1, 'medium': 2, 'hard': 3}
        max_difficulty_score = difficulty_order.get(max_difficulty, 3)
        
        final_filtered = []
        for suggestion in filtered:
            suggestion_difficulty_score = difficulty_order.get(suggestion.implementation_difficulty, 1)
            if suggestion_difficulty_score <= max_difficulty_score:
                final_filtered.append(suggestion)
        
        return final_filtered
    
    def _get_priority_score(self, priority: str) -> int:
        """获取优先级分数"""
        priority_scores = {'high': 3, 'medium': 2, 'low': 1}
        return priority_scores.get(priority, 1)
    
    async def _analyze_keyword_optimization(self, result: ResumeOptimizationResult,
                                          content: str, target_job: Optional[JobAnalysisResult]) -> None:
        """分析关键词优化"""
        if not target_job:
            return
        
        content_lower = content.lower()
        
        # 推荐关键词
        recommended = []
        for skill in target_job.required_skills + target_job.preferred_skills:
            if skill.name.lower() not in content_lower:
                recommended.append(skill.name)
        
        result.recommended_keywords = recommended[:15]  # 限制数量
        
        # 需要移除的关键词（过度使用）
        keyword_freq = self._analyze_keyword_frequency(content)
        to_remove = [k for k, v in keyword_freq.items() if v > 5]
        result.keywords_to_remove = to_remove
    
    def _calculate_expected_improvements(self, result: ResumeOptimizationResult) -> None:
        """计算预期改进"""
        improvements = {
            'ats_score': 0.0,
            'keyword_optimization': 0.0,
            'content_quality': 0.0,
            'job_match_score': 0.0
        }
        
        # 基于建议计算预期改进
        for suggestion in result.suggestions:
            if suggestion.ats_improvement:
                improvements['ats_score'] += suggestion.impact_score * 0.1
            
            if suggestion.optimization_type == OptimizationType.KEYWORD_OPTIMIZATION:
                improvements['keyword_optimization'] += suggestion.impact_score * 0.15
            
            if suggestion.optimization_type == OptimizationType.CONTENT_ENHANCEMENT:
                improvements['content_quality'] += suggestion.impact_score * 0.1
        
        # 职位匹配改进
        if result.recommended_keywords:
            improvements['job_match_score'] = len(result.recommended_keywords) * 0.05
        
        # 限制改进幅度
        for key in improvements:
            improvements[key] = min(improvements[key], 0.3)  # 最大30%改进
        
        result.expected_improvements = improvements
    
    async def _advanced_optimization(self, result: ResumeOptimizationResult,
                                   content: str, target_job: Optional[JobAnalysisResult]) -> None:
        """高级优化"""
        # 使用MCP进行深度分析
        if self.mcp_manager and target_job:
            try:
                # 获取行业特定的优化建议
                industry_analysis = await self.mcp_manager.call_tool(
                    'context7',
                    'get-library-docs',
                    {
                        'context7CompatibleLibraryID': f'/industry/{target_job.job_title.lower()}',
                        'topic': 'resume optimization',
                        'tokens': 5000
                    }
                )
                
                if industry_analysis.success:
                    # 处理行业特定建议
                    self._process_industry_optimization(result, industry_analysis.result)
                    result.data_sources.append('industry_analysis')
                    
            except Exception as e:
                logger.warning(f"高级优化分析失败: {e}")
        
        # 竞争分析
        await self._competitive_analysis(result, content, target_job)
        
        result.data_sources.append('advanced_optimization')
    
    def _process_industry_optimization(self, result: ResumeOptimizationResult, analysis_data: Dict[str, Any]) -> None:
        """处理行业特定优化"""
        # 这里可以根据Context7返回的行业数据进行处理
        # 示例实现
        if 'recommendations' in analysis_data:
            for rec in analysis_data['recommendations']:
                suggestion = OptimizationSuggestion(
                    section=ResumeSection.SUMMARY,
                    optimization_type=OptimizationType.PERSONALIZATION,
                    priority="medium",
                    title=f"行业特定优化: {rec.get('title', '优化建议')}",
                    description=rec.get('description', ''),
                    reasoning="基于行业最佳实践的优化建议",
                    impact_score=0.6
                )
                result.suggestions.append(suggestion)
    
    async def _competitive_analysis(self, result: ResumeOptimizationResult,
                                  content: str, target_job: Optional[JobAnalysisResult]) -> None:
        """竞争分析"""
        # 分析同类职位的简历特点
        # 这里可以添加更多竞争分析逻辑
        pass
    
    async def _expert_optimization(self, result: ResumeOptimizationResult,
                                 content: str, target_job: Optional[JobAnalysisResult]) -> None:
        """专家级优化"""
        # 个性化优化
        await self._personalized_optimization(result, content, target_job)
        
        # 心理学优化
        await self._psychological_optimization(result, content)
        
        # 趋势分析
        await self._trend_analysis_optimization(result, target_job)
        
        result.data_sources.append('expert_optimization')
    
    async def _personalized_optimization(self, result: ResumeOptimizationResult,
                                       content: str, target_job: Optional[JobAnalysisResult]) -> None:
        """个性化优化"""
        # 基于用户背景和目标职位的个性化建议
        pass
    
    async def _psychological_optimization(self, result: ResumeOptimizationResult, content: str) -> None:
        """心理学优化"""
        # 基于认知心理学的简历优化
        pass
    
    async def _trend_analysis_optimization(self, result: ResumeOptimizationResult, target_job: Optional[JobAnalysisResult]) -> None:
        """趋势分析优化"""
        # 基于行业趋势的优化建议
        pass
    
    def _calculate_confidence_score(self, result: ResumeOptimizationResult) -> float:
        """计算置信度评分"""
        confidence_factors = []
        
        # 数据源数量
        confidence_factors.append(min(len(result.data_sources) / 3, 1.0))
        
        # 建议数量和质量
        if result.suggestions:
            avg_impact = sum(s.impact_score for s in result.suggestions) / len(result.suggestions)
            confidence_factors.append(avg_impact)
        
        # ATS报告完整性
        if result.ats_report:
            ats_completeness = 1.0 if result.ats_report.overall_score > 0 else 0.5
            confidence_factors.append(ats_completeness)
        
        # 原始分析质量
        if result.original_analysis:
            analysis_quality = result.original_analysis.overall_score
            confidence_factors.append(analysis_quality)
        
        return sum(confidence_factors) / len(confidence_factors) if confidence_factors else 0.0


# 便捷函数
async def optimize_resume_for_job(resume_content: str, target_job: JobAnalysisResult,
                                 optimization_level: OptimizationLevel = OptimizationLevel.STANDARD,
                                 user_preferences: Optional[Dict[str, Any]] = None,
                                 config: Optional[Dict[str, Any]] = None) -> ResumeOptimizationResult:
    """为特定职位优化简历
    
    Args:
        resume_content: 简历内容
        target_job: 目标职位分析结果
        optimization_level: 优化级别
        user_preferences: 用户偏好
        config: 优化器配置
    
    Returns:
        ResumeOptimizationResult: 优化结果
    """
    optimizer = ResumeOptimizer(config)
    return await optimizer.optimize_resume(resume_content, target_job, optimization_level, user_preferences)


if __name__ == "__main__":
    # 测试代码
    async def test_resume_optimizer():
        """测试简历优化器"""
        # 示例简历内容
        sample_resume = """
        John Doe
        Software Engineer
        <EMAIL>
        (555) 123-4567
        
        SUMMARY
        Experienced software engineer with 5 years of experience in web development.
        
        EXPERIENCE
        Senior Developer at Tech Corp (2020-2023)
        - Developed web applications
        - Worked with team members
        - Fixed bugs and issues
        
        EDUCATION
        Bachelor of Computer Science
        University of Technology (2015-2019)
        
        SKILLS
        Python, JavaScript, HTML, CSS
        """
        
        # 示例目标职位
        from src.mcp.analyzers.job_analyzer import JobAnalysisResult, SkillRequirement
        
        target_job = JobAnalysisResult(
            job_title="Full Stack Developer",
            required_skills=[
                SkillRequirement(name="Python", importance="high"),
                SkillRequirement(name="React", importance="high"),
                SkillRequirement(name="Django", importance="medium"),
                SkillRequirement(name="PostgreSQL", importance="medium")
            ],
            preferred_skills=[
                SkillRequirement(name="AWS", importance="low"),
                SkillRequirement(name="Docker", importance="low")
            ]
        )
        
        # 创建优化器并运行测试
        optimizer = ResumeOptimizer()
        
        print("开始简历优化测试...")
        result = await optimizer.optimize_resume(
            resume_content=sample_resume,
            target_job=target_job,
            optimization_level=OptimizationLevel.ADVANCED
        )
        
        print(f"\n=== 简历优化结果 ===")
        print(f"优化级别: {result.optimization_level.value}")
        print(f"职位匹配度: {result.job_match_score:.2f}")
        print(f"置信度: {result.confidence_score:.2f}")
        print(f"优化耗时: {result.optimization_duration:.2f}秒")
        
        if result.ats_report:
            print(f"\n=== ATS兼容性报告 ===")
            print(f"总体评分: {result.ats_report.overall_score:.2f}")
            print(f"关键词密度: {result.ats_report.keyword_density:.3f}")
            print(f"格式兼容性: {result.ats_report.format_compatibility:.2f}")
            print(f"结构评分: {result.ats_report.structure_score:.2f}")
            
            if result.ats_report.issues:
                print("\n发现的问题:")
                for issue in result.ats_report.issues:
                    print(f"  - {issue}")
        
        if result.suggestions:
            print(f"\n=== 优化建议 ({len(result.suggestions)}条) ===")
            for i, suggestion in enumerate(result.suggestions[:5], 1):
                print(f"{i}. [{suggestion.priority.upper()}] {suggestion.title}")
                print(f"   章节: {suggestion.section.value}")
                print(f"   描述: {suggestion.description}")
                print(f"   影响分数: {suggestion.impact_score:.2f}")
                print()
        
        if result.recommended_keywords:
            print(f"推荐添加的关键词: {', '.join(result.recommended_keywords[:10])}")
        
        if result.expected_improvements:
            print(f"\n=== 预期改进 ===")
            for metric, improvement in result.expected_improvements.items():
                print(f"{metric}: +{improvement:.1%}")
        
        print("\n简历优化测试完成!")
    
    # 运行测试
    import asyncio
    asyncio.run(test_resume_optimizer())