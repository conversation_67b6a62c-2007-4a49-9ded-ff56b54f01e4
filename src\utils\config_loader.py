# -*- coding: utf-8 -*-
"""
配置加载模块
提供统一的配置文件加载和管理功能
"""

import json
import yaml
import os
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
import logging

# 尝试导入相关模块
try:
    from .logger import logger
except ImportError:
    logger = logging.getLogger(__name__)


@dataclass
class ConfigPaths:
    """配置文件路径"""
    config_dir: Path = field(default_factory=lambda: Path("config"))
    secrets_dir: Path = field(default_factory=lambda: Path("secrets"))
    data_dir: Path = field(default_factory=lambda: Path("data"))
    
    def __post_init__(self):
        # 确保目录存在
        for dir_path in [self.config_dir, self.secrets_dir, self.data_dir]:
            dir_path.mkdir(exist_ok=True)


class ConfigLoader:
    """配置加载器"""
    
    def __init__(self, base_path: Optional[Union[str, Path]] = None):
        self.base_path = Path(base_path) if base_path else Path.cwd()
        self.paths = ConfigPaths()
        self._config_cache: Dict[str, Any] = {}
        
    def load_config(self, config_name: str, config_type: str = "auto") -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            config_name: 配置文件名（不含扩展名）
            config_type: 配置文件类型 ('json', 'yaml', 'auto')
            
        Returns:
            Dict[str, Any]: 配置数据
        """
        # 检查缓存
        cache_key = f"{config_name}_{config_type}"
        if cache_key in self._config_cache:
            return self._config_cache[cache_key]
        
        config_data = {}
        
        try:
            if config_type == "auto":
                # 自动检测文件类型
                config_data = self._load_auto_detect(config_name)
            elif config_type == "json":
                config_data = self._load_json(config_name)
            elif config_type == "yaml":
                config_data = self._load_yaml(config_name)
            else:
                raise ValueError(f"不支持的配置文件类型: {config_type}")
            
            # 缓存配置
            self._config_cache[cache_key] = config_data
            logger.info(f"成功加载配置文件: {config_name}")
            
        except Exception as e:
            logger.error(f"加载配置文件失败 {config_name}: {e}")
            # 返回默认配置
            config_data = self._get_default_config(config_name)
        
        return config_data
    
    def _load_auto_detect(self, config_name: str) -> Dict[str, Any]:
        """自动检测并加载配置文件"""
        # 尝试不同的文件扩展名和位置
        search_paths = [
            self.base_path / "config",
            self.base_path,
            self.paths.config_dir
        ]
        
        extensions = [".json", ".yaml", ".yml"]
        
        for search_path in search_paths:
            for ext in extensions:
                config_file = search_path / f"{config_name}{ext}"
                if config_file.exists():
                    if ext == ".json":
                        return self._load_json_file(config_file)
                    else:
                        return self._load_yaml_file(config_file)
        
        raise FileNotFoundError(f"找不到配置文件: {config_name}")
    
    def _load_json(self, config_name: str) -> Dict[str, Any]:
        """加载JSON配置文件"""
        config_file = self._find_config_file(config_name, ".json")
        return self._load_json_file(config_file)
    
    def _load_yaml(self, config_name: str) -> Dict[str, Any]:
        """加载YAML配置文件"""
        for ext in [".yaml", ".yml"]:
            try:
                config_file = self._find_config_file(config_name, ext)
                return self._load_yaml_file(config_file)
            except FileNotFoundError:
                continue
        raise FileNotFoundError(f"找不到YAML配置文件: {config_name}")
    
    def _find_config_file(self, config_name: str, extension: str) -> Path:
        """查找配置文件"""
        search_paths = [
            self.base_path / "config",
            self.base_path,
            self.paths.config_dir
        ]
        
        for search_path in search_paths:
            config_file = search_path / f"{config_name}{extension}"
            if config_file.exists():
                return config_file
        
        raise FileNotFoundError(f"找不到配置文件: {config_name}{extension}")
    
    def _load_json_file(self, file_path: Path) -> Dict[str, Any]:
        """加载JSON文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _load_yaml_file(self, file_path: Path) -> Dict[str, Any]:
        """加载YAML文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except ImportError:
            logger.warning("PyYAML未安装，无法加载YAML文件")
            return {}
    
    def _get_default_config(self, config_name: str) -> Dict[str, Any]:
        """获取默认配置"""
        default_configs = {
            "linkedin": {
                "login_url": "https://www.linkedin.com/login",
                "jobs_url": "https://www.linkedin.com/jobs/search/",
                "timeout": 30,
                "retry_count": 3
            },
            "mcp": {
                "servers": [],
                "timeout": 30,
                "retry_count": 3,
                "rate_limit": 10
            },
            "job_analysis": {
                "min_match_score": 0.7,
                "required_skills_weight": 0.4,
                "experience_weight": 0.3,
                "culture_weight": 0.3
            },
            "application": {
                "auto_apply": False,
                "cover_letter_required": True,
                "max_applications_per_day": 10
            }
        }
        
        return default_configs.get(config_name, {})
    
    def save_config(self, config_name: str, config_data: Dict[str, Any], 
                   config_type: str = "json") -> bool:
        """
        保存配置文件
        
        Args:
            config_name: 配置文件名
            config_data: 配置数据
            config_type: 配置文件类型
            
        Returns:
            bool: 是否保存成功
        """
        try:
            config_dir = self.base_path / "config"
            config_dir.mkdir(exist_ok=True)
            
            if config_type == "json":
                config_file = config_dir / f"{config_name}.json"
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, indent=2, ensure_ascii=False)
            elif config_type in ["yaml", "yml"]:
                config_file = config_dir / f"{config_name}.yaml"
                try:
                    with open(config_file, 'w', encoding='utf-8') as f:
                        yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
                except ImportError:
                    logger.error("PyYAML未安装，无法保存YAML文件")
                    return False
            else:
                raise ValueError(f"不支持的配置文件类型: {config_type}")
            
            # 更新缓存
            cache_key = f"{config_name}_{config_type}"
            self._config_cache[cache_key] = config_data
            
            logger.info(f"配置文件保存成功: {config_file}")
            return True
            
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            return False
    
    def get_env_config(self, prefix: str = "AIHAWK_") -> Dict[str, Any]:
        """
        从环境变量加载配置
        
        Args:
            prefix: 环境变量前缀
            
        Returns:
            Dict[str, Any]: 环境变量配置
        """
        env_config = {}
        
        for key, value in os.environ.items():
            if key.startswith(prefix):
                config_key = key[len(prefix):].lower()
                # 尝试转换数据类型
                try:
                    # 尝试转换为数字
                    if value.isdigit():
                        env_config[config_key] = int(value)
                    elif value.replace('.', '').isdigit():
                        env_config[config_key] = float(value)
                    elif value.lower() in ['true', 'false']:
                        env_config[config_key] = value.lower() == 'true'
                    else:
                        env_config[config_key] = value
                except ValueError:
                    env_config[config_key] = value
        
        return env_config
    
    def merge_configs(self, *configs: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并多个配置字典
        
        Args:
            *configs: 配置字典列表
            
        Returns:
            Dict[str, Any]: 合并后的配置
        """
        merged = {}
        
        for config in configs:
            if isinstance(config, dict):
                merged.update(config)
        
        return merged
    
    def clear_cache(self):
        """清空配置缓存"""
        self._config_cache.clear()
        logger.info("配置缓存已清空")


# 全局配置加载器实例
config_loader = ConfigLoader()


# 便捷函数
def load_config(config_name: str, config_type: str = "auto") -> Dict[str, Any]:
    """加载配置文件的便捷函数"""
    return config_loader.load_config(config_name, config_type)


def save_config(config_name: str, config_data: Dict[str, Any], 
               config_type: str = "json") -> bool:
    """保存配置文件的便捷函数"""
    return config_loader.save_config(config_name, config_data, config_type)


def get_env_config(prefix: str = "AIHAWK_") -> Dict[str, Any]:
    """获取环境变量配置的便捷函数"""
    return config_loader.get_env_config(prefix)


def get_linkedin_config() -> Dict[str, Any]:
    """获取LinkedIn配置"""
    return load_config("linkedin")


def get_mcp_config() -> Dict[str, Any]:
    """获取MCP配置"""
    return load_config("mcp")


def get_job_analysis_config() -> Dict[str, Any]:
    """获取职位分析配置"""
    return load_config("job_analysis")


def get_application_config() -> Dict[str, Any]:
    """获取申请配置"""
    return load_config("application")


# 测试函数
def test_config_loader():
    """测试配置加载器"""
    print("=== 配置加载器测试 ===")
    
    # 测试默认配置
    linkedin_config = get_linkedin_config()
    print(f"LinkedIn配置: {linkedin_config}")
    
    mcp_config = get_mcp_config()
    print(f"MCP配置: {mcp_config}")
    
    # 测试环境变量配置
    env_config = get_env_config()
    print(f"环境变量配置: {env_config}")
    
    # 测试保存配置
    test_config = {"test_key": "test_value", "test_number": 42}
    success = save_config("test", test_config)
    print(f"保存测试配置: {success}")
    
    if success:
        loaded_test = load_config("test")
        print(f"加载测试配置: {loaded_test}")


if __name__ == "__main__":
    test_config_loader()