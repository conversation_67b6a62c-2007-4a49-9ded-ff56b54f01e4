# MCP增强功能依赖包
# 此文件包含MCP集成所需的额外依赖包

# 核心MCP依赖
mcp>=1.0.0                    # Model Context Protocol核心库
aiohttp>=3.8.0               # 异步HTTP客户端
aiofiles>=23.0.0             # 异步文件操作

# 数据处理和分析
pandas>=2.0.0                # 数据分析库
numpy>=1.24.0                # 数值计算库
scipy>=1.10.0                # 科学计算库
scikit-learn>=1.3.0          # 机器学习库

# 文本处理和NLP
nltk>=3.8.0                  # 自然语言处理工具包
spacy>=3.6.0                 # 高级NLP库
textblob>=0.17.0             # 简单文本处理
fuzzy-wuzzy>=0.18.0          # 模糊字符串匹配
python-Levenshtein>=0.21.0   # 字符串距离计算

# 网页内容处理
beautifulsoup4>=4.12.0       # HTML解析
lxml>=4.9.0                  # XML/HTML解析器
readability-lxml>=0.8.0      # 网页内容提取
markdown>=3.4.0              # Markdown处理
html2text>=2020.1.16         # HTML转文本

# 配置和数据格式
PyYAML>=6.0.0                # YAML配置文件处理
toml>=0.10.0                 # TOML配置文件处理
jsonschema>=4.17.0           # JSON模式验证
pydantic>=2.0.0              # 数据验证和设置管理

# 缓存和存储
redis>=4.5.0                 # Redis客户端
diskcache>=5.6.0             # 磁盘缓存
cachetools>=5.3.0            # 内存缓存工具

# 异步和并发
asyncio-throttle>=1.0.2      # 异步限流
aioredis>=2.0.0              # 异步Redis客户端
aiolimiter>=1.1.0            # 异步速率限制

# 日志和监控
loguru>=0.7.0                # 高级日志库
structlog>=23.1.0            # 结构化日志
prometheus-client>=0.16.0    # Prometheus监控指标

# 测试依赖
pytest>=7.4.0                # 测试框架
pytest-asyncio>=0.21.0       # 异步测试支持
pytest-mock>=3.11.0          # 测试模拟
pytest-cov>=4.1.0            # 测试覆盖率
pytest-xdist>=3.3.0          # 并行测试
factory-boy>=3.3.0           # 测试数据工厂
faker>=19.0.0                # 假数据生成

# 开发工具
black>=23.7.0                # 代码格式化
isort>=5.12.0               # 导入排序
flake8>=6.0.0               # 代码检查
mypy>=1.5.0                 # 类型检查
pre-commit>=3.3.0           # Git钩子

# 性能优化
uvloop>=0.17.0              # 高性能事件循环（Unix系统）
orjson>=3.9.0               # 快速JSON处理
ujson>=5.8.0                # 超快JSON处理
cython>=3.0.0               # Python C扩展

# 安全和加密
cryptography>=41.0.0        # 加密库
bcrypt>=4.0.0               # 密码哈希
PyJWT>=2.8.0                # JWT令牌处理

# 网络和HTTP
httpx>=0.24.0               # 现代HTTP客户端
requests>=2.31.0            # HTTP库
urllib3>=2.0.0              # HTTP客户端

# 数据库（可选）
sqlalchemy>=2.0.0           # SQL工具包
alembic>=1.11.0             # 数据库迁移
psycopg2-binary>=2.9.0      # PostgreSQL适配器
PyMySQL>=1.1.0              # MySQL适配器

# 时间和日期处理
python-dateutil>=2.8.0      # 日期工具
pytz>=2023.3                # 时区处理
arrow>=1.2.0                # 现代日期时间库

# 环境和配置管理
python-dotenv>=1.0.0        # 环境变量管理
click>=8.1.0                # 命令行界面
rich>=13.5.0                # 富文本终端输出

# 图像处理（如果需要处理图像内容）
Pillow>=10.0.0              # 图像处理库

# 机器学习和AI（可选高级功能）
transformers>=4.30.0        # Hugging Face变换器
torch>=2.0.0                # PyTorch深度学习框架
tensorflow>=2.13.0          # TensorFlow机器学习框架

# 数据可视化（可选）
matplotlib>=3.7.0           # 绘图库
seaborn>=0.12.0             # 统计数据可视化
plotly>=5.15.0              # 交互式图表

# 文档生成
sphinx>=7.1.0               # 文档生成
sphinx-rtd-theme>=1.3.0     # Read the Docs主题
mkdocs>=1.5.0               # Markdown文档生成

# 版本控制和部署
gitpython>=3.1.0            # Git操作
fabric>=3.2.0               # 部署自动化

# 监控和调试
memory-profiler>=0.61.0     # 内存分析
line-profiler>=4.1.0        # 行级性能分析
py-spy>=0.3.0               # Python性能分析

# 特定于Windows的依赖（如果在Windows上运行）
colorama>=0.4.0             # Windows终端颜色支持
win32-setctime>=1.1.0       # Windows文件时间设置

# API客户端（如果需要集成外部服务）
google-api-python-client>=2.95.0  # Google API客户端
boto3>=1.28.0               # AWS SDK
azure-storage-blob>=12.17.0 # Azure存储客户端

# 消息队列（如果需要异步任务处理）
celery>=5.3.0               # 分布式任务队列
redis-py-cluster>=2.1.0     # Redis集群支持

# 配置验证和模式
cerberus>=1.3.0             # 数据验证
voluptuous>=0.13.0          # 数据验证

# 实用工具
tqdm>=4.65.0                # 进度条
click-spinner>=0.1.0        # 命令行旋转器
humanize>=4.7.0             # 人性化数据显示

# 特殊用途库
fuzzywuzzy[speedup]>=0.18.0 # 模糊匹配加速版本
chardet>=5.1.0              # 字符编码检测
langdetect>=1.0.0           # 语言检测

# 开发和调试工具
ipdb>=0.13.0                # 增强调试器
pudb>=2023.1                # 全屏调试器
icecream>=2.1.0             # 调试打印工具

# 注意事项：
# 1. 某些包可能有平台特定的要求
# 2. 版本号可能需要根据实际环境调整
# 3. 可选依赖可以根据需要安装
# 4. 建议使用虚拟环境安装这些依赖