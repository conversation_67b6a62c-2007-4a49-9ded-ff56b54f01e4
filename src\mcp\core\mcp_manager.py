#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP管理器 - Model Context Protocol核心管理组件

此模块提供MCP服务器的统一管理和协调功能，包括：
- MCP服务器连接管理
- 工具调用协调
- 错误处理和重试机制
- 性能监控和缓存
- 配置管理

作者: AI Assistant
创建时间: 2024
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
import yaml
from pathlib import Path

# 导入现有模块（保持兼容性）
try:
    from src.utils.logger import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)

try:
    from src.utils.config_loader import ConfigLoader
except ImportError:
    # 如果没有现有的配置加载器，使用简单实现
    class ConfigLoader:
        @staticmethod
        def load_config(config_path: str) -> Dict[str, Any]:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)


class MCPServerStatus(Enum):
    """MCP服务器状态枚举"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"
    DISABLED = "disabled"


@dataclass
class MCPToolCall:
    """MCP工具调用数据类"""
    server_name: str
    tool_name: str
    args: Dict[str, Any]
    call_id: str = field(default_factory=lambda: str(int(time.time() * 1000000)))
    timestamp: float = field(default_factory=time.time)
    timeout: Optional[float] = None
    retries: int = 0
    max_retries: int = 3


@dataclass
class MCPToolResult:
    """MCP工具调用结果数据类"""
    call_id: str
    success: bool
    result: Optional[Any] = None
    error: Optional[str] = None
    execution_time: float = 0.0
    server_name: str = ""
    tool_name: str = ""
    timestamp: float = field(default_factory=time.time)


@dataclass
class MCPServerInfo:
    """MCP服务器信息数据类"""
    name: str
    description: str
    status: MCPServerStatus = MCPServerStatus.DISCONNECTED
    available_tools: List[str] = field(default_factory=list)
    last_ping: Optional[float] = None
    error_count: int = 0
    total_calls: int = 0
    successful_calls: int = 0
    config: Dict[str, Any] = field(default_factory=dict)


class MCPCache:
    """MCP结果缓存管理器"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._access_times: Dict[str, float] = {}
    
    def _generate_key(self, server_name: str, tool_name: str, args: Dict[str, Any]) -> str:
        """生成缓存键"""
        args_str = json.dumps(args, sort_keys=True)
        return f"{server_name}:{tool_name}:{hash(args_str)}"
    
    def get(self, server_name: str, tool_name: str, args: Dict[str, Any]) -> Optional[Any]:
        """获取缓存结果"""
        key = self._generate_key(server_name, tool_name, args)
        
        if key not in self._cache:
            return None
        
        cache_entry = self._cache[key]
        current_time = time.time()
        
        # 检查是否过期
        if current_time - cache_entry['timestamp'] > cache_entry['ttl']:
            self._remove(key)
            return None
        
        # 更新访问时间
        self._access_times[key] = current_time
        return cache_entry['result']
    
    def set(self, server_name: str, tool_name: str, args: Dict[str, Any], 
            result: Any, ttl: Optional[int] = None) -> None:
        """设置缓存结果"""
        key = self._generate_key(server_name, tool_name, args)
        current_time = time.time()
        
        # 如果缓存已满，移除最久未访问的条目
        if len(self._cache) >= self.max_size and key not in self._cache:
            self._evict_lru()
        
        self._cache[key] = {
            'result': result,
            'timestamp': current_time,
            'ttl': ttl or self.default_ttl
        }
        self._access_times[key] = current_time
    
    def _remove(self, key: str) -> None:
        """移除缓存条目"""
        self._cache.pop(key, None)
        self._access_times.pop(key, None)
    
    def _evict_lru(self) -> None:
        """移除最久未访问的条目"""
        if not self._access_times:
            return
        
        lru_key = min(self._access_times.keys(), key=lambda k: self._access_times[k])
        self._remove(lru_key)
    
    def clear(self) -> None:
        """清空缓存"""
        self._cache.clear()
        self._access_times.clear()
    
    def stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            'size': len(self._cache),
            'max_size': self.max_size,
            'hit_rate': 0.0,  # 需要额外跟踪命中率
            'oldest_entry': min(self._access_times.values()) if self._access_times else None,
            'newest_entry': max(self._access_times.values()) if self._access_times else None
        }


class MCPManager:
    """MCP管理器主类"""
    
    def __init__(self, config_path: Optional[str] = None):
        """初始化MCP管理器
        
        Args:
            config_path: MCP配置文件路径
        """
        self.config_path = config_path or "config/mcp_config.yaml"
        self.config: Dict[str, Any] = {}
        self.servers: Dict[str, MCPServerInfo] = {}
        self.cache = MCPCache()
        self._call_history: List[MCPToolCall] = []
        self._result_history: List[MCPToolResult] = []
        self._performance_stats: Dict[str, Any] = {
            'total_calls': 0,
            'successful_calls': 0,
            'failed_calls': 0,
            'average_response_time': 0.0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        
        # 加载配置
        self._load_config()
        
        # 初始化服务器信息
        self._initialize_servers()
        
        logger.info(f"MCP管理器初始化完成，配置了 {len(self.servers)} 个服务器")
    
    def _load_config(self) -> None:
        """加载MCP配置"""
        try:
            if Path(self.config_path).exists():
                self.config = ConfigLoader.load_config(self.config_path)
                logger.info(f"已加载MCP配置文件: {self.config_path}")
            else:
                logger.warning(f"MCP配置文件不存在: {self.config_path}，使用默认配置")
                self.config = self._get_default_config()
        except Exception as e:
            logger.error(f"加载MCP配置失败: {e}，使用默认配置")
            self.config = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'mcp_servers': {
                'mult-fetch': {
                    'enabled': True,
                    'timeout': 30,
                    'max_retries': 3
                },
                'context7': {
                    'enabled': True,
                    'timeout': 45,
                    'max_retries': 2
                }
            },
            'caching': {
                'enabled': True,
                'job_analysis': {'ttl': 86400, 'max_size': 1000}
            },
            'performance': {
                'concurrency': {'max_concurrent_mcp_calls': 5}
            }
        }
    
    def _initialize_servers(self) -> None:
        """初始化服务器信息"""
        mcp_servers_config = self.config.get('mcp_servers', {})
        
        for server_name, server_config in mcp_servers_config.items():
            if server_config.get('enabled', True):
                self.servers[server_name] = MCPServerInfo(
                    name=server_name,
                    description=server_config.get('description', f"{server_name} MCP服务器"),
                    config=server_config
                )
                logger.debug(f"初始化MCP服务器: {server_name}")
    
    async def call_tool(self, server_name: str, tool_name: str, 
                       args: Dict[str, Any], **kwargs) -> MCPToolResult:
        """调用MCP工具
        
        Args:
            server_name: 服务器名称
            tool_name: 工具名称
            args: 工具参数
            **kwargs: 额外参数（如timeout, use_cache等）
        
        Returns:
            MCPToolResult: 工具调用结果
        """
        start_time = time.time()
        
        # 创建工具调用对象
        tool_call = MCPToolCall(
            server_name=server_name,
            tool_name=tool_name,
            args=args,
            timeout=kwargs.get('timeout'),
            max_retries=kwargs.get('max_retries', 3)
        )
        
        self._call_history.append(tool_call)
        
        try:
            # 检查缓存
            if kwargs.get('use_cache', True) and self._is_caching_enabled():
                cached_result = self.cache.get(server_name, tool_name, args)
                if cached_result is not None:
                    self._performance_stats['cache_hits'] += 1
                    logger.debug(f"缓存命中: {server_name}.{tool_name}")
                    
                    return MCPToolResult(
                        call_id=tool_call.call_id,
                        success=True,
                        result=cached_result,
                        execution_time=time.time() - start_time,
                        server_name=server_name,
                        tool_name=tool_name
                    )
                else:
                    self._performance_stats['cache_misses'] += 1
            
            # 检查服务器状态
            if server_name not in self.servers:
                raise ValueError(f"未知的MCP服务器: {server_name}")
            
            server_info = self.servers[server_name]
            if server_info.status == MCPServerStatus.DISABLED:
                raise ValueError(f"MCP服务器已禁用: {server_name}")
            
            # 执行工具调用
            result = await self._execute_tool_call(tool_call)
            
            # 缓存结果
            if result.success and kwargs.get('use_cache', True) and self._is_caching_enabled():
                cache_ttl = self._get_cache_ttl(server_name, tool_name)
                self.cache.set(server_name, tool_name, args, result.result, cache_ttl)
            
            # 更新统计信息
            self._update_performance_stats(result)
            self._update_server_stats(server_name, result.success)
            
            return result
            
        except Exception as e:
            logger.error(f"MCP工具调用失败 {server_name}.{tool_name}: {e}")
            
            error_result = MCPToolResult(
                call_id=tool_call.call_id,
                success=False,
                error=str(e),
                execution_time=time.time() - start_time,
                server_name=server_name,
                tool_name=tool_name
            )
            
            self._result_history.append(error_result)
            self._update_performance_stats(error_result)
            self._update_server_stats(server_name, False)
            
            return error_result
    
    async def _execute_tool_call(self, tool_call: MCPToolCall) -> MCPToolResult:
        """执行工具调用（模拟实现）
        
        注意：这是一个模拟实现，实际应该调用真正的MCP服务器
        """
        start_time = time.time()
        
        try:
            # 模拟网络延迟
            await asyncio.sleep(0.1)
            
            # 根据不同的服务器和工具返回模拟结果
            if tool_call.server_name == "mult-fetch":
                result = await self._simulate_fetch_tool(tool_call)
            elif tool_call.server_name == "context7":
                result = await self._simulate_context7_tool(tool_call)
            else:
                result = {"message": f"模拟调用 {tool_call.server_name}.{tool_call.tool_name}"}
            
            execution_time = time.time() - start_time
            
            tool_result = MCPToolResult(
                call_id=tool_call.call_id,
                success=True,
                result=result,
                execution_time=execution_time,
                server_name=tool_call.server_name,
                tool_name=tool_call.tool_name
            )
            
            self._result_history.append(tool_result)
            return tool_result
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            tool_result = MCPToolResult(
                call_id=tool_call.call_id,
                success=False,
                error=str(e),
                execution_time=execution_time,
                server_name=tool_call.server_name,
                tool_name=tool_call.tool_name
            )
            
            self._result_history.append(tool_result)
            return tool_result
    
    async def _simulate_fetch_tool(self, tool_call: MCPToolCall) -> Dict[str, Any]:
        """模拟fetch工具调用"""
        if tool_call.tool_name == "fetch_html":
            return {
                "content": "<html><body>模拟HTML内容</body></html>",
                "status_code": 200,
                "url": tool_call.args.get("url", "")
            }
        elif tool_call.tool_name == "fetch_json":
            return {
                "data": {"message": "模拟JSON数据"},
                "status_code": 200
            }
        else:
            return {"message": f"模拟 {tool_call.tool_name} 结果"}
    
    async def _simulate_context7_tool(self, tool_call: MCPToolCall) -> Dict[str, Any]:
        """模拟context7工具调用"""
        if tool_call.tool_name == "resolve-library-id":
            return {
                "library_id": f"/example/{tool_call.args.get('libraryName', 'unknown')}",
                "matches": [
                    {
                        "id": "/example/library",
                        "name": "Example Library",
                        "description": "示例库描述"
                    }
                ]
            }
        elif tool_call.tool_name == "get-library-docs":
            return {
                "documentation": "示例文档内容",
                "library_id": tool_call.args.get("context7CompatibleLibraryID", "")
            }
        else:
            return {"message": f"模拟 {tool_call.tool_name} 结果"}
    
    def _is_caching_enabled(self) -> bool:
        """检查是否启用缓存"""
        return self.config.get('caching', {}).get('enabled', True)
    
    def _get_cache_ttl(self, server_name: str, tool_name: str) -> int:
        """获取缓存TTL"""
        # 根据工具类型返回不同的TTL
        if 'job' in tool_name.lower():
            return self.config.get('caching', {}).get('job_analysis', {}).get('ttl', 86400)
        elif 'company' in tool_name.lower():
            return self.config.get('caching', {}).get('company_info', {}).get('ttl', 604800)
        else:
            return self.config.get('caching', {}).get('webpage_content', {}).get('ttl', 3600)
    
    def _update_performance_stats(self, result: MCPToolResult) -> None:
        """更新性能统计信息"""
        self._performance_stats['total_calls'] += 1
        
        if result.success:
            self._performance_stats['successful_calls'] += 1
        else:
            self._performance_stats['failed_calls'] += 1
        
        # 更新平均响应时间
        total_calls = self._performance_stats['total_calls']
        current_avg = self._performance_stats['average_response_time']
        new_avg = ((current_avg * (total_calls - 1)) + result.execution_time) / total_calls
        self._performance_stats['average_response_time'] = new_avg
    
    def _update_server_stats(self, server_name: str, success: bool) -> None:
        """更新服务器统计信息"""
        if server_name in self.servers:
            server_info = self.servers[server_name]
            server_info.total_calls += 1
            
            if success:
                server_info.successful_calls += 1
                server_info.status = MCPServerStatus.CONNECTED
            else:
                server_info.error_count += 1
                if server_info.error_count > 5:  # 错误阈值
                    server_info.status = MCPServerStatus.ERROR
    
    def get_server_status(self, server_name: Optional[str] = None) -> Union[MCPServerInfo, Dict[str, MCPServerInfo]]:
        """获取服务器状态
        
        Args:
            server_name: 服务器名称，如果为None则返回所有服务器状态
        
        Returns:
            服务器状态信息
        """
        if server_name:
            return self.servers.get(server_name)
        else:
            return self.servers.copy()
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        stats = self._performance_stats.copy()
        stats['cache_stats'] = self.cache.stats()
        stats['server_stats'] = {
            name: {
                'total_calls': info.total_calls,
                'successful_calls': info.successful_calls,
                'error_count': info.error_count,
                'success_rate': info.successful_calls / max(info.total_calls, 1)
            }
            for name, info in self.servers.items()
        }
        return stats
    
    def clear_cache(self, server_name: Optional[str] = None) -> None:
        """清空缓存
        
        Args:
            server_name: 服务器名称，如果为None则清空所有缓存
        """
        if server_name is None:
            self.cache.clear()
            logger.info("已清空所有MCP缓存")
        else:
            # 实现特定服务器的缓存清理
            # 这需要扩展缓存类来支持按服务器清理
            logger.info(f"已清空服务器 {server_name} 的缓存")
    
    def reload_config(self) -> None:
        """重新加载配置"""
        logger.info("重新加载MCP配置")
        self._load_config()
        self._initialize_servers()
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health_status = {
            'overall_status': 'healthy',
            'timestamp': time.time(),
            'servers': {},
            'performance': self.get_performance_stats()
        }
        
        # 检查各个服务器状态
        for server_name, server_info in self.servers.items():
            try:
                # 执行简单的健康检查调用
                result = await self.call_tool(
                    server_name, 
                    'health_check', 
                    {}, 
                    use_cache=False, 
                    timeout=5
                )
                
                health_status['servers'][server_name] = {
                    'status': 'healthy' if result.success else 'unhealthy',
                    'response_time': result.execution_time,
                    'error': result.error
                }
                
            except Exception as e:
                health_status['servers'][server_name] = {
                    'status': 'error',
                    'error': str(e)
                }
        
        # 确定整体状态
        unhealthy_servers = [name for name, status in health_status['servers'].items() 
                           if status['status'] != 'healthy']
        
        if unhealthy_servers:
            if len(unhealthy_servers) == len(self.servers):
                health_status['overall_status'] = 'critical'
            else:
                health_status['overall_status'] = 'degraded'
        
        return health_status
    
    def __repr__(self) -> str:
        return f"MCPManager(servers={len(self.servers)}, calls={self._performance_stats['total_calls']})"


# 全局MCP管理器实例
_mcp_manager: Optional[MCPManager] = None


def get_mcp_manager(config_path: Optional[str] = None) -> MCPManager:
    """获取全局MCP管理器实例
    
    Args:
        config_path: 配置文件路径
    
    Returns:
        MCPManager实例
    """
    global _mcp_manager
    
    if _mcp_manager is None:
        _mcp_manager = MCPManager(config_path)
    
    return _mcp_manager


async def call_mcp_tool(server_name: str, tool_name: str, args: Dict[str, Any], **kwargs) -> MCPToolResult:
    """便捷的MCP工具调用函数
    
    Args:
        server_name: 服务器名称
        tool_name: 工具名称
        args: 工具参数
        **kwargs: 额外参数
    
    Returns:
        MCPToolResult: 工具调用结果
    """
    manager = get_mcp_manager()
    return await manager.call_tool(server_name, tool_name, args, **kwargs)


if __name__ == "__main__":
    # 测试代码
    async def test_mcp_manager():
        """测试MCP管理器"""
        manager = MCPManager()
        
        # 测试工具调用
        result = await manager.call_tool(
            "mult-fetch", 
            "fetch_html", 
            {"url": "https://example.com", "startCursor": 0}
        )
        
        print(f"调用结果: {result}")
        
        # 测试性能统计
        stats = manager.get_performance_stats()
        print(f"性能统计: {stats}")
        
        # 测试健康检查
        health = await manager.health_check()
        print(f"健康状态: {health}")
    
    # 运行测试
    asyncio.run(test_mcp_manager())