# 用户技能配置文件
# 此文件定义用户的技能、经验和偏好，用于职位匹配分析

# 个人基本信息
user_profile:
  name: "Your Name"  # 用户姓名
  title: "Software Developer"  # 当前职位
  experience_years: 5  # 总工作经验年数
  location: "San Francisco, CA"  # 当前位置
  preferred_locations:  # 偏好工作地点
    - "San Francisco, CA"
    - "Remote"
    - "New York, NY"
  
  # 薪资期望
  salary_expectations:
    currency: "USD"
    min_salary: 100000  # 最低期望薪资
    max_salary: 150000  # 最高期望薪资
    preferred_salary: 125000  # 理想薪资
  
  # 工作偏好
  work_preferences:
    employment_type:  # 就业类型偏好
      - "full-time"
      - "contract"
    work_arrangement:  # 工作安排偏好
      - "remote"
      - "hybrid"
      - "on-site"
    company_size:  # 公司规模偏好
      - "startup"
      - "mid-size"
      - "enterprise"

# 技术技能
technical_skills:
  # 编程语言
  programming_languages:
    - name: "Python"
      proficiency: "expert"  # beginner, intermediate, advanced, expert
      years_experience: 5
      last_used: "2024"
      certifications: []
      
    - name: "JavaScript"
      proficiency: "advanced"
      years_experience: 4
      last_used: "2024"
      certifications: []
      
    - name: "TypeScript"
      proficiency: "advanced"
      years_experience: 3
      last_used: "2024"
      certifications: []
      
    - name: "Java"
      proficiency: "intermediate"
      years_experience: 2
      last_used: "2023"
      certifications: []
      
    - name: "Go"
      proficiency: "beginner"
      years_experience: 1
      last_used: "2024"
      certifications: []
  
  # 框架和库
  frameworks:
    - name: "Django"
      proficiency: "expert"
      years_experience: 4
      last_used: "2024"
      
    - name: "FastAPI"
      proficiency: "advanced"
      years_experience: 2
      last_used: "2024"
      
    - name: "React"
      proficiency: "advanced"
      years_experience: 3
      last_used: "2024"
      
    - name: "Vue.js"
      proficiency: "intermediate"
      years_experience: 2
      last_used: "2023"
      
    - name: "Node.js"
      proficiency: "advanced"
      years_experience: 3
      last_used: "2024"
      
    - name: "Express.js"
      proficiency: "advanced"
      years_experience: 3
      last_used: "2024"
      
    - name: "Flask"
      proficiency: "intermediate"
      years_experience: 2
      last_used: "2023"
  
  # 数据库
  databases:
    - name: "PostgreSQL"
      proficiency: "advanced"
      years_experience: 4
      last_used: "2024"
      
    - name: "MySQL"
      proficiency: "intermediate"
      years_experience: 3
      last_used: "2024"
      
    - name: "MongoDB"
      proficiency: "intermediate"
      years_experience: 2
      last_used: "2024"
      
    - name: "Redis"
      proficiency: "intermediate"
      years_experience: 2
      last_used: "2024"
      
    - name: "Elasticsearch"
      proficiency: "beginner"
      years_experience: 1
      last_used: "2023"
  
  # 云平台和DevOps
  cloud_devops:
    - name: "AWS"
      proficiency: "advanced"
      years_experience: 3
      last_used: "2024"
      services:
        - "EC2"
        - "S3"
        - "RDS"
        - "Lambda"
        - "CloudFormation"
      
    - name: "Docker"
      proficiency: "advanced"
      years_experience: 3
      last_used: "2024"
      
    - name: "Kubernetes"
      proficiency: "intermediate"
      years_experience: 2
      last_used: "2024"
      
    - name: "Jenkins"
      proficiency: "intermediate"
      years_experience: 2
      last_used: "2024"
      
    - name: "GitHub Actions"
      proficiency: "advanced"
      years_experience: 2
      last_used: "2024"
      
    - name: "Terraform"
      proficiency: "beginner"
      years_experience: 1
      last_used: "2024"
  
  # 工具和技术
  tools_technologies:
    - name: "Git"
      proficiency: "expert"
      years_experience: 5
      last_used: "2024"
      
    - name: "Linux"
      proficiency: "advanced"
      years_experience: 4
      last_used: "2024"
      
    - name: "Nginx"
      proficiency: "intermediate"
      years_experience: 2
      last_used: "2024"
      
    - name: "Apache"
      proficiency: "beginner"
      years_experience: 1
      last_used: "2023"
      
    - name: "GraphQL"
      proficiency: "intermediate"
      years_experience: 2
      last_used: "2024"
      
    - name: "REST API"
      proficiency: "expert"
      years_experience: 4
      last_used: "2024"
  
  # 测试
  testing:
    - name: "pytest"
      proficiency: "advanced"
      years_experience: 3
      last_used: "2024"
      
    - name: "Jest"
      proficiency: "intermediate"
      years_experience: 2
      last_used: "2024"
      
    - name: "Selenium"
      proficiency: "intermediate"
      years_experience: 2
      last_used: "2024"
      
    - name: "Cypress"
      proficiency: "beginner"
      years_experience: 1
      last_used: "2023"

# 软技能
soft_skills:
  communication:
    - "Technical Writing"
    - "Presentation Skills"
    - "Cross-functional Collaboration"
    - "Client Communication"
  
  leadership:
    - "Team Leadership"
    - "Mentoring"
    - "Project Management"
    - "Code Review"
  
  problem_solving:
    - "Analytical Thinking"
    - "Debugging"
    - "System Design"
    - "Performance Optimization"
  
  adaptability:
    - "Learning New Technologies"
    - "Agile Methodologies"
    - "Remote Work"
    - "Change Management"

# 行业经验
industry_experience:
  - name: "Fintech"
    years: 2
    description: "Financial technology applications and payment systems"
    
  - name: "E-commerce"
    years: 2
    description: "Online retail platforms and marketplace solutions"
    
  - name: "SaaS"
    years: 3
    description: "Software as a Service applications and platforms"
    
  - name: "Healthcare"
    years: 1
    description: "Healthcare management systems and patient data platforms"

# 教育背景
education:
  - degree: "Bachelor of Science"
    field: "Computer Science"
    institution: "University of California, Berkeley"
    graduation_year: 2019
    gpa: 3.7
    
  - degree: "Master of Science"
    field: "Software Engineering"
    institution: "Stanford University"
    graduation_year: 2021
    gpa: 3.8

# 认证
certifications:
  - name: "AWS Certified Solutions Architect"
    issuer: "Amazon Web Services"
    issue_date: "2023-06"
    expiry_date: "2026-06"
    credential_id: "AWS-SAA-123456"
    
  - name: "Certified Kubernetes Administrator"
    issuer: "Cloud Native Computing Foundation"
    issue_date: "2023-09"
    expiry_date: "2026-09"
    credential_id: "CKA-789012"

# 项目经验
project_experience:
  - name: "E-commerce Platform Redesign"
    role: "Lead Developer"
    duration_months: 8
    team_size: 6
    technologies:
      - "Python"
      - "Django"
      - "React"
      - "PostgreSQL"
      - "AWS"
    description: "Led the complete redesign of a high-traffic e-commerce platform"
    
  - name: "Microservices Migration"
    role: "Senior Developer"
    duration_months: 12
    team_size: 4
    technologies:
      - "Python"
      - "FastAPI"
      - "Docker"
      - "Kubernetes"
      - "MongoDB"
    description: "Migrated monolithic application to microservices architecture"
    
  - name: "Real-time Analytics Dashboard"
    role: "Full Stack Developer"
    duration_months: 6
    team_size: 3
    technologies:
      - "Node.js"
      - "React"
      - "WebSocket"
      - "Redis"
      - "Elasticsearch"
    description: "Built real-time analytics dashboard for business intelligence"

# 语言能力
languages:
  - name: "English"
    proficiency: "native"
    
  - name: "Spanish"
    proficiency: "intermediate"
    
  - name: "Mandarin"
    proficiency: "beginner"

# 职业目标和偏好
career_goals:
  short_term:  # 1-2年目标
    - "Advance to Senior/Lead Developer role"
    - "Gain expertise in cloud-native technologies"
    - "Lead larger development teams"
    
  long_term:  # 3-5年目标
    - "Transition to Engineering Manager or Principal Engineer"
    - "Contribute to open source projects"
    - "Speak at technical conferences"
  
  interests:
    - "Machine Learning and AI"
    - "Blockchain Technology"
    - "IoT and Edge Computing"
    - "Sustainable Technology"

# 工作环境偏好
work_environment_preferences:
  company_culture:
    - "Innovation-focused"
    - "Collaborative"
    - "Learning-oriented"
    - "Diverse and Inclusive"
    
  team_structure:
    - "Cross-functional teams"
    - "Agile/Scrum methodology"
    - "Code review culture"
    - "Continuous learning"
    
  benefits_priorities:
    - "Competitive salary"
    - "Health insurance"
    - "Professional development budget"
    - "Flexible work arrangements"
    - "Stock options/equity"
    - "Retirement benefits"

# 技能权重配置（用于匹配算法）
skill_weights:
  programming_languages: 0.3
  frameworks: 0.25
  databases: 0.15
  cloud_devops: 0.2
  tools_technologies: 0.1

# 学习计划（当前正在学习的技能）
learning_plan:
  current_learning:
    - name: "Rust"
      target_proficiency: "intermediate"
      estimated_completion: "2024-12"
      
    - name: "Machine Learning"
      target_proficiency: "intermediate"
      estimated_completion: "2025-03"
      
    - name: "GraphQL"
      target_proficiency: "advanced"
      estimated_completion: "2024-09"
  
  planned_learning:
    - name: "Blockchain Development"
      target_proficiency: "intermediate"
      planned_start: "2025-01"
      
    - name: "System Design"
      target_proficiency: "advanced"
      planned_start: "2024-10"