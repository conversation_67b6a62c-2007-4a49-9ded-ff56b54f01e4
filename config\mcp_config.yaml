# MCP增强功能配置文件
# 此文件配置所有MCP相关的设置和参数

# MCP服务器配置
mcp_servers:
  # Mult-Fetch MCP服务器配置
  mult-fetch:
    enabled: true
    timeout: 30  # 请求超时时间（秒）
    max_retries: 3  # 最大重试次数
    retry_delay: 1  # 重试延迟（秒）
    default_params:
      contentSizeLimit: 50000  # 内容大小限制（字节）
      enableContentSplitting: true  # 启用内容分割
      autoDetectMode: true  # 自动检测模式
      extractContent: false  # 提取主要内容
      includeMetadata: false  # 包含元数据
      fallbackToOriginal: true  # 回退到原始内容
      useBrowser: false  # 使用浏览器模式
      saveCookies: true  # 保存Cookie
    
  # Context7 MCP服务器配置
  context7:
    enabled: true
    timeout: 45  # 请求超时时间（秒）
    max_retries: 2  # 最大重试次数
    retry_delay: 2  # 重试延迟（秒）
    default_params:
      tokens: 10000  # 默认文档令牌数
    cache:
      enabled: true  # 启用缓存
      ttl: 3600  # 缓存生存时间（秒）
      max_size: 100  # 最大缓存条目数

# 职位分析配置
job_analysis:
  enabled: true
  
  # 技术栈分析配置
  tech_stack:
    enabled: true
    confidence_threshold: 0.7  # 技术识别置信度阈值
    
  # 公司信息分析配置
  company_info:
    enabled: true
    fetch_additional_info: true  # 获取额外公司信息
    info_sources:
      - linkedin
      - company_website
      - glassdoor
    
  # 薪资分析配置
  salary_analysis:
    enabled: true
    market_data_sources:
      - glassdoor
      - payscale
      - levels_fyi
    currency: "USD"  # 默认货币
    
  # 技能匹配配置
  skills_matching:
    enabled: true
    user_skills_file: "config/user_skills.yaml"  # 用户技能文件路径
    weight_factors:
      required_skills: 1.0
      preferred_skills: 0.7
      nice_to_have: 0.3
    
  # 兼容性评分配置
  compatibility_scoring:
    enabled: true
    weights:
      skills_match: 0.4  # 技能匹配权重
      experience_level: 0.2  # 经验水平权重
      location_preference: 0.15  # 地理位置权重
      salary_expectation: 0.15  # 薪资期望权重
      company_culture: 0.1  # 公司文化权重
    minimum_score: 0.6  # 最低兼容性评分

# LinkedIn自动化增强配置
linkedin_automation:
  mcp_integration:
    enabled: true
    
  # 职位搜索增强
  enhanced_search:
    enabled: true
    max_concurrent_analysis: 3  # 最大并发分析数
    analysis_timeout: 60  # 分析超时时间（秒）
    
  # 智能申请流程
  intelligent_application:
    enabled: true
    auto_analyze_jobs: true  # 自动分析职位
    min_compatibility_score: 0.7  # 最低兼容性评分要求
    max_applications_per_session: 10  # 每次会话最大申请数
    application_delay: 30  # 申请间隔（秒）
    
  # 个性化简历和求职信
  personalization:
    enabled: true
    auto_customize_resume: false  # 自动定制简历（需要额外配置）
    auto_customize_cover_letter: false  # 自动定制求职信（需要额外配置）

# 缓存配置
caching:
  enabled: true
  
  # 职位分析缓存
  job_analysis:
    enabled: true
    ttl: 86400  # 24小时（秒）
    max_size: 1000  # 最大缓存条目数
    
  # 公司信息缓存
  company_info:
    enabled: true
    ttl: 604800  # 7天（秒）
    max_size: 500
    
  # 网页内容缓存
  webpage_content:
    enabled: true
    ttl: 3600  # 1小时（秒）
    max_size: 200

# 日志配置
logging:
  level: "INFO"  # 日志级别: DEBUG, INFO, WARNING, ERROR
  
  # MCP工具调用日志
  mcp_calls:
    enabled: true
    log_requests: true  # 记录请求
    log_responses: false  # 记录响应（可能包含大量数据）
    log_errors: true  # 记录错误
    
  # 性能监控日志
  performance:
    enabled: true
    log_execution_time: true  # 记录执行时间
    log_memory_usage: false  # 记录内存使用

# 错误处理配置
error_handling:
  # 重试配置
  retry:
    max_attempts: 3  # 最大重试次数
    backoff_factor: 2  # 退避因子
    max_delay: 60  # 最大延迟（秒）
    
  # 降级策略
  fallback:
    enabled: true
    disable_mcp_on_failure: false  # 失败时禁用MCP
    use_basic_analysis: true  # 使用基础分析作为后备

# 性能优化配置
performance:
  # 并发控制
  concurrency:
    max_concurrent_mcp_calls: 5  # 最大并发MCP调用数
    max_concurrent_job_analysis: 3  # 最大并发职位分析数
    
  # 资源限制
  limits:
    max_content_size: 1048576  # 最大内容大小（1MB）
    max_analysis_time: 120  # 最大分析时间（秒）
    
  # 优化选项
  optimization:
    enable_content_compression: true  # 启用内容压缩
    enable_request_batching: false  # 启用请求批处理
    enable_lazy_loading: true  # 启用延迟加载

# 安全配置
security:
  # API密钥管理
  api_keys:
    encryption_enabled: false  # API密钥加密（需要额外配置）
    
  # 数据隐私
  privacy:
    anonymize_logs: false  # 匿名化日志
    data_retention_days: 30  # 数据保留天数
    
  # 访问控制
  access_control:
    rate_limiting: true  # 启用速率限制
    requests_per_minute: 60  # 每分钟请求数限制

# 实验性功能
experimental:
  # AI增强分析
  ai_enhanced_analysis:
    enabled: false  # 启用AI增强分析（实验性）
    
  # 预测性匹配
  predictive_matching:
    enabled: false  # 启用预测性匹配（实验性）
    
  # 自动学习
  auto_learning:
    enabled: false  # 启用自动学习（实验性）

# 调试配置
debug:
  enabled: false  # 启用调试模式
  verbose_logging: false  # 详细日志
  save_debug_data: false  # 保存调试数据
  debug_output_dir: "debug_output"  # 调试输出目录