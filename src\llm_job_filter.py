# -*- coding: utf-8 -*-
"""
LLM职位过滤器
使用Gemini API智能过滤推荐职位，保留主要职位
"""

import json
import re
import logging
from typing import List, Dict, Optional, Union
from datetime import datetime

logger = logging.getLogger(__name__)

class LLMJobFilter:
    """
    使用LLM智能过滤职位的类
    主要功能：区分主要职位和推荐职位
    """
    
    def __init__(self, llm_parser=None):
        self.llm_parser = llm_parser
        
        # 推荐职位的关键词模式
        self.recommendation_patterns = [
            r'Top job picks for you',
            r'Based on your profile',
            r'Your job alert',
            r'Remote opportunities',
            r'Hybrid opportunities',
            r'Because you expressed interest',
            r'More jobs for you',
            r'Recommended for you',
            r'推荐职位',
            r'为您推荐',
            r'Similar jobs',
            r'You might be interested',
            r'Other jobs you may like'
        ]
        
        # LLM提示模板
        self.filter_prompt_template = """
你是一个LinkedIn职位分析专家。请分析以下职位列表，识别并过滤掉"推荐职位"，只保留"主要搜索结果"。

判断标准：
1. 推荐职位通常包含以下特征：
   - 标题或描述中包含"推荐"、"为您推荐"、"Based on your profile"等字样
   - 位于页面特定区域（如底部推荐区域）
   - HTML中包含推荐相关的class或data属性
   - 与用户搜索条件不完全匹配的职位

2. 主要职位特征：
   - 直接匹配搜索条件
   - 位于主要搜索结果区域
   - 没有明显的推荐标识

请返回JSON格式的结果，包含：
- main_jobs: 主要职位列表（保留原始数据结构）
- recommended_jobs: 推荐职位列表
- filter_summary: 过滤总结

职位数据：
{jobs_data}

请严格按照JSON格式返回结果。
"""
    
    def filter_jobs(self, jobs: List[Dict]) -> Dict[str, Union[List[Dict], Dict]]:
        """
        过滤职位，区分主要职位和推荐职位
        
        Args:
            jobs: 职位列表
            
        Returns:
            Dict: 包含过滤结果的字典
        """
        if not jobs:
            return self._create_empty_filter_result()
        
        try:
            logger.info(f"🤖 开始LLM职位过滤，输入职位数: {len(jobs)}")
            
            # 首先使用规则过滤
            rule_filtered = self._rule_based_filter(jobs)
            
            # 如果有LLM，进一步使用LLM过滤
            if self.llm_parser:
                llm_filtered = self._llm_based_filter(rule_filtered['main_jobs'])
                
                # 合并结果
                final_result = {
                    'main_jobs': llm_filtered['main_jobs'],
                    'recommended_jobs': rule_filtered['recommended_jobs'] + llm_filtered['recommended_jobs'],
                    'filter_summary': {
                        'total_input': len(jobs),
                        'rule_filtered_main': len(rule_filtered['main_jobs']),
                        'rule_filtered_recommended': len(rule_filtered['recommended_jobs']),
                        'llm_filtered_main': len(llm_filtered['main_jobs']),
                        'llm_filtered_recommended': len(llm_filtered['recommended_jobs']),
                        'final_main': len(llm_filtered['main_jobs']),
                        'final_recommended': len(rule_filtered['recommended_jobs'] + llm_filtered['recommended_jobs']),
                        'filter_time': datetime.now().isoformat(),
                        'method': 'rule_and_llm'
                    }
                }
            else:
                final_result = rule_filtered
                final_result['filter_summary']['method'] = 'rule_only'
            
            logger.info(f"✅ 职位过滤完成: {final_result['filter_summary']['final_main']} 主要职位, {final_result['filter_summary']['final_recommended']} 推荐职位")
            return final_result
            
        except Exception as e:
            logger.error(f"❌ 职位过滤失败: {e}")
            return {
                'main_jobs': jobs,  # 失败时返回所有职位
                'recommended_jobs': [],
                'filter_summary': {
                    'total_input': len(jobs),
                    'final_main': len(jobs),
                    'final_recommended': 0,
                    'filter_time': datetime.now().isoformat(),
                    'method': 'fallback',
                    'error': str(e)
                }
            }
    
    def _rule_based_filter(self, jobs: List[Dict]) -> Dict[str, Union[List[Dict], Dict]]:
        """
        基于规则的职位过滤
        """
        main_jobs = []
        recommended_jobs = []
        
        for job in jobs:
            if self._is_recommended_job(job):
                recommended_jobs.append(job)
            else:
                main_jobs.append(job)
        
        return {
            'main_jobs': main_jobs,
            'recommended_jobs': recommended_jobs,
            'filter_summary': {
                'total_input': len(jobs),
                'rule_filtered_main': len(main_jobs),
                'rule_filtered_recommended': len(recommended_jobs),
                'filter_time': datetime.now().isoformat()
            }
        }
    
    def _is_recommended_job(self, job: Dict) -> bool:
        """
        判断是否为推荐职位
        """
        # 检查HTML内容
        html_content = job.get('raw_html', '')
        
        # 检查推荐关键词
        for pattern in self.recommendation_patterns:
            if re.search(pattern, html_content, re.IGNORECASE):
                return True
        
        # 检查职位标题和公司名
        title = job.get('title', '').lower()
        company = job.get('company', '').lower()
        
        # 一些启发式规则
        if 'recommend' in title or 'recommend' in company:
            return True
        
        # 检查状态标签
        status_tags = job.get('status_tags', [])
        for tag in status_tags:
            if 'recommend' in tag.lower():
                return True
        
        # 检查HTML中的CSS类名
        if 'recommendation' in html_content.lower():
            return True
        
        return False
    
    def _llm_based_filter(self, jobs: List[Dict]) -> Dict[str, Union[List[Dict], Dict]]:
        """
        基于LLM的职位过滤
        """
        if not self.llm_parser or not jobs:
            return {
                'main_jobs': jobs,
                'recommended_jobs': [],
                'filter_summary': {
                    'llm_filtered_main': len(jobs),
                    'llm_filtered_recommended': 0
                }
            }
        
        try:
            # 准备LLM输入数据
            jobs_data = self._prepare_jobs_for_llm(jobs)
            
            # 构建提示
            prompt = self.filter_prompt_template.format(jobs_data=jobs_data)
            
            # 调用LLM
            llm_response = self._call_llm(prompt)
            
            # 解析LLM响应
            filter_result = self._parse_llm_response(llm_response, jobs)
            
            return filter_result
            
        except Exception as e:
            logger.warning(f"LLM过滤失败，返回原始数据: {e}")
            return {
                'main_jobs': jobs,
                'recommended_jobs': [],
                'filter_summary': {
                    'llm_filtered_main': len(jobs),
                    'llm_filtered_recommended': 0,
                    'llm_error': str(e)
                }
            }
    
    def _prepare_jobs_for_llm(self, jobs: List[Dict]) -> str:
        """
        为LLM准备职位数据
        """
        simplified_jobs = []
        
        for i, job in enumerate(jobs):
            simplified_job = {
                'index': i,
                'job_id': job.get('job_id'),
                'title': job.get('title'),
                'company': job.get('company'),
                'location': job.get('location'),
                'status_tags': job.get('status_tags', []),
                'is_promoted': job.get('is_promoted', False),
                'posted_time': job.get('posted_time'),
                'html_snippet': self._extract_relevant_html_snippet(job.get('raw_html', ''))
            }
            simplified_jobs.append(simplified_job)
        
        return json.dumps(simplified_jobs, ensure_ascii=False, indent=2)
    
    def _extract_relevant_html_snippet(self, html: str) -> str:
        """
        提取相关的HTML片段用于LLM分析
        """
        if not html:
            return ''
        
        # 提取关键部分，限制长度
        snippet = html[:800]  # 限制在800字符内
        
        # 移除一些不必要的HTML标签
        snippet = re.sub(r'<script[^>]*>.*?</script>', '', snippet, flags=re.DOTALL)
        snippet = re.sub(r'<style[^>]*>.*?</style>', '', snippet, flags=re.DOTALL)
        
        return snippet
    
    def _call_llm(self, prompt: str) -> str:
        """
        调用LLM API
        """
        try:
            # 这里需要实现具体的LLM调用逻辑
            # 暂时返回模拟响应
            if hasattr(self.llm_parser, 'generate_response'):
                return self.llm_parser.generate_response(prompt)
            elif hasattr(self.llm_parser, 'invoke'):
                return self.llm_parser.invoke(prompt)
            else:
                # 如果没有可用的LLM方法，返回默认响应
                return self._create_default_llm_response()
                
        except Exception as e:
            logger.error(f"LLM调用失败: {e}")
            raise
    
    def _create_default_llm_response(self) -> str:
        """
        创建默认的LLM响应（当LLM不可用时）
        """
        return json.dumps({
            'main_jobs': [],
            'recommended_jobs': [],
            'filter_summary': {
                'note': 'LLM not available, using rule-based filtering only'
            }
        }, ensure_ascii=False)
    
    def _parse_llm_response(self, response: str, original_jobs: List[Dict]) -> Dict[str, Union[List[Dict], Dict]]:
        """
        解析LLM响应
        """
        try:
            # 尝试解析JSON响应
            parsed_response = json.loads(response)
            
            # 验证响应格式
            if 'main_jobs' not in parsed_response:
                raise ValueError("LLM响应缺少main_jobs字段")
            
            # 根据索引重建完整的职位对象
            main_jobs = []
            recommended_jobs = []
            
            main_indices = set()
            recommended_indices = set()
            
            # 收集主要职位索引
            for job_data in parsed_response.get('main_jobs', []):
                if isinstance(job_data, dict) and 'index' in job_data:
                    main_indices.add(job_data['index'])
            
            # 收集推荐职位索引
            for job_data in parsed_response.get('recommended_jobs', []):
                if isinstance(job_data, dict) and 'index' in job_data:
                    recommended_indices.add(job_data['index'])
            
            # 重建职位列表
            for i, job in enumerate(original_jobs):
                if i in main_indices:
                    main_jobs.append(job)
                elif i in recommended_indices:
                    recommended_jobs.append(job)
                else:
                    # 默认归类为主要职位
                    main_jobs.append(job)
            
            return {
                'main_jobs': main_jobs,
                'recommended_jobs': recommended_jobs,
                'filter_summary': {
                    'llm_filtered_main': len(main_jobs),
                    'llm_filtered_recommended': len(recommended_jobs),
                    'llm_response_summary': parsed_response.get('filter_summary', {})
                }
            }
            
        except Exception as e:
            logger.warning(f"解析LLM响应失败: {e}")
            # 解析失败时，返回所有职位作为主要职位
            return {
                'main_jobs': original_jobs,
                'recommended_jobs': [],
                'filter_summary': {
                    'llm_filtered_main': len(original_jobs),
                    'llm_filtered_recommended': 0,
                    'parse_error': str(e)
                }
            }
    
    def _create_empty_filter_result(self) -> Dict:
        """
        创建空的过滤结果
        """
        return {
            'main_jobs': [],
            'recommended_jobs': [],
            'filter_summary': {
                'total_input': 0,
                'final_main': 0,
                'final_recommended': 0,
                'filter_time': datetime.now().isoformat(),
                'method': 'empty_input'
            }
        }
    
    def get_filter_statistics(self, filter_result: Dict) -> Dict[str, Union[int, float, str]]:
        """
        获取过滤统计信息
        """
        summary = filter_result.get('filter_summary', {})
        
        total_input = summary.get('total_input', 0)
        final_main = summary.get('final_main', 0)
        final_recommended = summary.get('final_recommended', 0)
        
        stats = {
            'total_jobs_processed': total_input,
            'main_jobs_count': final_main,
            'recommended_jobs_count': final_recommended,
            'main_jobs_percentage': (final_main / total_input * 100) if total_input > 0 else 0,
            'recommended_jobs_percentage': (final_recommended / total_input * 100) if total_input > 0 else 0,
            'filter_method': summary.get('method', 'unknown'),
            'filter_time': summary.get('filter_time', ''),
            'has_errors': 'error' in summary or 'llm_error' in summary or 'parse_error' in summary
        }
        
        return stats