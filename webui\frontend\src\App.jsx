import React, { useState, useEffect } from 'react'
import { <PERSON>rowserRouter as Router, Routes, Route, Link, useLocation } from 'react-router-dom'
import ReactMarkdown from 'react-markdown'
import {
  ThemeProvider,
  createTheme,
  CssBaseline,
  AppBar,
  Toolbar,
  Typography,
  Container,
  Box,
  Card,
  CardContent,
  Button,
  Grid,
  TextField,
  Alert,
  Chip,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  CircularProgress,
  Snackbar,
  Tooltip,
  Fade,
  Tabs,
  Tab,
  Dialog,
  DialogTitle,
  DialogContent,
  InputAdornment,
  Paper
} from '@mui/material'
import {
  Work as WorkIcon,
  Description as ResumeIcon,
  Email as CoverLetterIcon,
  Dashboard as DashboardIcon,
  Menu as MenuIcon,
  CheckCircle as CheckIcon,
  Info as InfoIcon,
  MenuBook as MenuBookIcon,

  Download as DownloadIcon,
  CloudUpload as UploadIcon,
  Email as EmailIcon,
  Psychology as AIIcon,
  Storage as DataIcon,
  Settings as SystemIcon,
  Extension as AdvancedIcon,
  Search as SearchIcon,
  Close as CloseIcon
} from '@mui/icons-material'
import axios from 'axios'
import LinkedInAutomation from './LinkedInAutomation'

import GuidePage from './GuidePage'
import { LinkedInProvider } from './LinkedInContext'
import { SettingsProvider } from './SettingsContext'
import Settings from './Settings'
import ErrorBoundary from './ErrorBoundary'

// LinkedIn Logo SVG Component
const LinkedInIcon = () => (
  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
  </svg>
)

// Dark theme configuration
const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#00d4ff',
      light: '#4de6ff',
      dark: '#0099cc'
    },
    secondary: {
      main: '#ff6b35',
      light: '#ff9566',
      dark: '#cc4a1a'
    },
    background: {
      default: '#0a0a0a',
      paper: '#1a1a1a'
    },
    text: {
      primary: '#ffffff',
      secondary: '#b0b0b0'
    }
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700,
      fontSize: '2.5rem'
    },
    h2: {
      fontWeight: 600,
      fontSize: '2rem'
    },
    h3: {
      fontWeight: 600,
      fontSize: '1.5rem'
    }
  },
  components: {
    MuiCard: {
      styleOverrides: {
        root: {
          background: 'linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%)',
          border: '1px solid #333',
          borderRadius: '12px',
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: '0 8px 32px rgba(0, 212, 255, 0.2)'
          }
        }
      }
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: '8px',
          textTransform: 'none',
          fontWeight: 600
        }
      }
    },
    MuiTooltip: {
      styleOverrides: {
        tooltip: {
          backgroundColor: 'transparent',
          color: '#00d4ff',
          fontSize: '0.875rem',
          fontWeight: 600,
          padding: '4px 8px',
          border: 'none',
          boxShadow: 'none',
          textShadow: '0 0 8px rgba(0, 212, 255, 0.6)',
          maxWidth: 'none'
        },
        arrow: {
          display: 'none'
        }
      }
    }
  }
})

// 文档搜索组件
function DocumentSearchBox() {
  const [searchQuery, setSearchQuery] = React.useState('');
  const [searchResults, setSearchResults] = React.useState([]);
  const [isSearching, setIsSearching] = React.useState(false);
  const [searchDialogOpen, setSearchDialogOpen] = React.useState(false);
  const [docContentDialogOpen, setDocContentDialogOpen] = React.useState(false);
  const [currentDocContent, setCurrentDocContent] = React.useState('');
  const [currentDocFilename, setCurrentDocFilename] = React.useState('');
  const [isLoadingDoc, setIsLoadingDoc] = React.useState(false);

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    
    setIsSearching(true);
    try {
      const response = await axios.post('/api/search-docs', {
        query: searchQuery,
        docsPath: 'D:\\Jobs_Applier_AI_Agent_AIHawk-main\\docs'
      });
      setSearchResults(response.data.results || []);
      setSearchDialogOpen(true);
    } catch (error) {
      console.error('搜索失败:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };

  const handleFileNameClick = async (filename) => {
    setIsLoadingDoc(true);
    setCurrentDocFilename(filename);
    try {
      const response = await axios.get(`/api/get-doc-content/${encodeURIComponent(filename)}`);
      if (response.data.success) {
        setCurrentDocContent(response.data.content);
        setDocContentDialogOpen(true);
      } else {
        console.error('获取文档内容失败:', response.data.error);
        setCurrentDocContent('获取文档内容失败: ' + response.data.error);
        setDocContentDialogOpen(true);
      }
    } catch (error) {
      console.error('获取文档内容失败:', error);
      setCurrentDocContent('获取文档内容失败: ' + error.message);
      setDocContentDialogOpen(true);
    } finally {
      setIsLoadingDoc(false);
    }
  };

  return (
    <>
      <Box sx={{ display: 'flex', alignItems: 'center', mr: 0.25, marginLeft: '75.6px !important' }}>
        <TextField
          size="small"
          placeholder="搜索文档..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyDown={handleKeyPress}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon sx={{ color: '#ffffff', fontSize: '1.1rem' }} />
              </InputAdornment>
            ),
            sx: {
              backgroundColor: 'rgba(255, 255, 255, 0.05)',
              borderRadius: '8px',
              color: '#ffffff',
              fontFamily: '"SF Pro Display", "Inter", "Roboto", sans-serif',
              fontSize: '0.875rem',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              '&:hover': {
                backgroundColor: 'rgba(255, 255, 255, 0.08)',
              },
              '&.Mui-focused': {
                backgroundColor: 'rgba(0, 212, 255, 0.12)',
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#00d4ff',
                }
              },
              '& .MuiOutlinedInput-notchedOutline': {
                borderColor: 'rgba(255, 255, 255, 0.2)',
              },
              '& input::placeholder': {
                color: '#b0b0b0',
                opacity: 1
              }
            }
          }}
          sx={{
            minWidth: 250,
            '& .MuiInputBase-input': {
              padding: '8px 12px',
              color: '#ffffff'
            }
          }}
        />
        {isSearching && (
          <CircularProgress size={20} sx={{ ml: 1, color: '#00d4ff' }} />
        )}
      </Box>

      <Dialog
        open={searchDialogOpen}
        onClose={() => setSearchDialogOpen(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            bgcolor: 'background.paper',
            borderRadius: '12px',
            border: '1px solid #333'
          }
        }}
      >
        <DialogTitle sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          borderBottom: '1px solid #333',
          pb: 2
        }}>
          <Typography variant="h6" sx={{ color: 'text.primary' }}>
            搜索结果: "{searchQuery}"
          </Typography>
          <IconButton onClick={() => setSearchDialogOpen(false)} sx={{ color: 'text.secondary' }}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          {searchResults.length > 0 ? (
            <Box sx={{ maxHeight: '60vh', overflow: 'auto' }}>
              {searchResults.map((result, index) => (
                <Paper
                  key={index}
                  elevation={0}
                  sx={{
                    p: 3,
                    m: 2,
                    bgcolor: 'rgba(255, 255, 255, 0.02)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: '8px',
                    '&:hover': {
                      bgcolor: 'rgba(0, 212, 255, 0.05)',
                      borderColor: 'rgba(0, 212, 255, 0.3)'
                    }
                  }}
                >
                  <Typography 
                    variant="subtitle1" 
                    sx={{ 
                      color: 'primary.main', 
                      mb: 1, 
                      fontFamily: '"SF Pro Display", "Segoe UI", "Roboto", "Helvetica Neue", "Arial", sans-serif',
                      fontSize: '1.1rem',
                      fontWeight: 600,
                      letterSpacing: '0.02em',
                      cursor: 'pointer',
                      textDecoration: 'underline',
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        color: 'primary.light',
                        transform: 'translateY(-1px)'
                      }
                    }}
                    onClick={() => handleFileNameClick(result.filename)}
                  >
                    📄 {result.filename}
                  </Typography>
                  <Typography 
                    variant="body2" 
                    sx={{ 
                      color: 'text.secondary', 
                      mb: 2,
                      fontFamily: '"SF Pro Text", "Segoe UI", "Roboto", "Helvetica Neue", "Arial", sans-serif',
                      fontSize: '0.875rem',
                      fontWeight: 500,
                      letterSpacing: '0.01em'
                    }}
                  >
                    第 {result.lineNumber} 行
                  </Typography>
                  <Box sx={{ 
                    bgcolor: 'rgba(0, 0, 0, 0.3)', 
                    p: 2, 
                    borderRadius: '6px',
                    border: '1px solid rgba(255, 255, 255, 0.1)'
                  }}>
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        color: 'text.primary',
                        fontFamily: '"SF Pro Text", "Segoe UI", "Roboto", "Helvetica Neue", "Arial", sans-serif',
                        fontSize: '0.95rem',
                        fontWeight: 400,
                        lineHeight: 1.7,
                        letterSpacing: '0.01em',
                        whiteSpace: 'pre-wrap'
                      }}
                      dangerouslySetInnerHTML={{ __html: result.context }}
                    />
                  </Box>
                </Paper>
              ))}
            </Box>
          ) : (
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <Typography variant="body1" sx={{ color: 'text.secondary' }}>
                {searchQuery ? '未找到相关文档内容' : '请输入搜索关键词'}
              </Typography>
            </Box>
          )}
        </DialogContent>
      </Dialog>

      {/* 文档全文查看弹窗 */}
      <Dialog
        open={docContentDialogOpen}
        onClose={() => setDocContentDialogOpen(false)}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            bgcolor: 'background.paper',
            borderRadius: '12px',
            border: '1px solid #333',
            maxHeight: '90vh'
          }
        }}
      >
        <DialogTitle sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          borderBottom: '1px solid #333',
          pb: 2
        }}>
          <Typography 
            variant="h6" 
            sx={{ 
              color: 'text.primary',
              fontFamily: '"SF Pro Display", "Segoe UI", "Roboto", "Helvetica Neue", "Arial", sans-serif',
              fontSize: '1.25rem',
              fontWeight: 600,
              letterSpacing: '0.02em'
            }}
          >
            📄 {currentDocFilename}
          </Typography>
          <IconButton onClick={() => setDocContentDialogOpen(false)} sx={{ color: 'text.secondary' }}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          {isLoadingDoc ? (
            <Box sx={{ p: 4, textAlign: 'center' }}>
              <CircularProgress sx={{ color: '#00d4ff' }} />
              <Typography 
                variant="body1" 
                sx={{ 
                  color: 'text.secondary', 
                  mt: 2,
                  fontFamily: '"SF Pro Text", "Segoe UI", "Roboto", "Helvetica Neue", "Arial", sans-serif',
                  fontSize: '1rem',
                  fontWeight: 400,
                  letterSpacing: '0.01em'
                }}
              >
                正在加载文档内容...
              </Typography>
            </Box>
          ) : (
            <Box sx={{ p: 3, maxHeight: '70vh', overflow: 'auto' }}>
              <Paper
                elevation={0}
                sx={{
                  p: 3,
                  bgcolor: 'rgba(0, 0, 0, 0.3)',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                  borderRadius: '8px'
                }}
              >
                <ReactMarkdown
                  components={{
                    p: ({ children }) => (
                      <Typography 
                        variant="body1" 
                        sx={{ 
                          color: 'text.primary',
                          fontFamily: '"SF Pro Text", "Segoe UI", "Roboto", "Helvetica Neue", "Arial", sans-serif',
                          fontSize: '1rem',
                          fontWeight: 400,
                          lineHeight: 1.8,
                          letterSpacing: '0.015em',
                          mb: 2
                        }}
                      >
                        {children}
                      </Typography>
                    ),
                    h1: ({ children }) => (
                      <Typography 
                        variant="h4" 
                        sx={{ 
                          color: 'text.primary',
                          fontFamily: '"SF Pro Display", "Segoe UI", "Roboto", "Helvetica Neue", "Arial", sans-serif',
                          fontWeight: 600,
                          mb: 3,
                          mt: 4
                        }}
                      >
                        {children}
                      </Typography>
                    ),
                    h2: ({ children }) => (
                      <Typography 
                        variant="h5" 
                        sx={{ 
                          color: 'text.primary',
                          fontFamily: '"SF Pro Display", "Segoe UI", "Roboto", "Helvetica Neue", "Arial", sans-serif',
                          fontWeight: 600,
                          mb: 2,
                          mt: 3
                        }}
                      >
                        {children}
                      </Typography>
                    ),
                    h3: ({ children }) => (
                      <Typography 
                        variant="h6" 
                        sx={{ 
                          color: 'text.primary',
                          fontFamily: '"SF Pro Display", "Segoe UI", "Roboto", "Helvetica Neue", "Arial", sans-serif',
                          fontWeight: 600,
                          mb: 2,
                          mt: 2
                        }}
                      >
                        {children}
                      </Typography>
                    ),
                    code: ({ inline, children }) => (
                      inline ? (
                        <Box 
                          component="span" 
                          sx={{ 
                            bgcolor: 'rgba(255, 255, 255, 0.1)',
                            color: '#00d4ff',
                            px: 0.5,
                            py: 0.2,
                            borderRadius: '4px',
                            fontFamily: 'monospace',
                            fontSize: '0.9em'
                          }}
                        >
                          {children}
                        </Box>
                      ) : (
                        <Paper 
                          sx={{ 
                            bgcolor: 'rgba(0, 0, 0, 0.5)',
                            border: '1px solid rgba(255, 255, 255, 0.1)',
                            borderRadius: '8px',
                            p: 2,
                            mb: 2,
                            overflow: 'auto'
                          }}
                        >
                          <Typography 
                            component="pre" 
                            sx={{ 
                              color: '#00d4ff',
                              fontFamily: 'monospace',
                              fontSize: '0.9rem',
                              lineHeight: 1.5,
                              m: 0
                            }}
                          >
                            {children}
                          </Typography>
                        </Paper>
                      )
                    ),
                    ul: ({ children }) => (
                      <Box component="ul" sx={{ pl: 3, mb: 2 }}>
                        {children}
                      </Box>
                    ),
                    ol: ({ children }) => (
                      <Box component="ol" sx={{ pl: 3, mb: 2 }}>
                        {children}
                      </Box>
                    ),
                    li: ({ children }) => (
                      <Typography 
                        component="li" 
                        sx={{ 
                          color: 'text.primary',
                          fontFamily: '"SF Pro Text", "Segoe UI", "Roboto", "Helvetica Neue", "Arial", sans-serif',
                          fontSize: '1rem',
                          lineHeight: 1.6,
                          mb: 0.5
                        }}
                      >
                        {children}
                      </Typography>
                    ),
                    blockquote: ({ children }) => (
                      <Paper 
                        sx={{ 
                          bgcolor: 'rgba(0, 212, 255, 0.1)',
                          border: '1px solid rgba(0, 212, 255, 0.3)',
                          borderLeft: '4px solid #00d4ff',
                          borderRadius: '4px',
                          p: 2,
                          mb: 2,
                          fontStyle: 'italic'
                        }}
                      >
                        {children}
                      </Paper>
                    )
                  }}
                >
                  {currentDocContent}
                </ReactMarkdown>
              </Paper>
            </Box>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}

// 顶部设置Tabs组件（右对齐）
function SettingsTabsInAppBar() {
  const [activeTab, setActiveTab] = React.useState(-1);
  const handleTabChange = (_, newValue) => setActiveTab(newValue);
  const tabs = [
    { label: 'LinkedIn问答', icon: <LinkedInIcon /> },
    { label: '数据管理', icon: <DataIcon /> },
    { label: '系统设置', icon: <SystemIcon /> },
    { label: '高级功能', icon: <AdvancedIcon /> }
  ];
  return (
    <Box sx={{ display: 'flex' }}>
      <Tabs
        value={activeTab}
        onChange={handleTabChange}
        variant="scrollable"
        scrollButtons="auto"
        sx={{ 
          '.MuiTab-root': { 
             minWidth: 120,
             position: 'relative',
             color: '#e0e0e0',
             fontFamily: '"SF Pro Display", "Inter", "Roboto", sans-serif',
             fontWeight: 500,
             fontSize: '0.875rem',
             letterSpacing: '0.02em',
             textTransform: 'none',
             transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
             '&.Mui-selected': {
               color: '#ffffff',
               backgroundColor: 'rgba(0, 212, 255, 0.12)',
               borderRadius: '8px',
               fontWeight: 600,
               '& .MuiSvgIcon-root, & svg': {
                 color: '#00d4ff'
               }
             },
             '&:hover': {
               color: '#ffffff',
               backgroundColor: 'rgba(255, 255, 255, 0.05)',
               borderRadius: '8px',
               '&::after': {
                 content: '""',
                 position: 'absolute',
                 bottom: 0,
                 left: '50%',
                 transform: 'translateX(-50%)',
                 width: '60%',
                 height: '2px',
                 backgroundColor: '#00d4ff',
                 borderRadius: '1px',
                 opacity: 0.8
               }
             },
             '& .MuiSvgIcon-root, & svg': {
               fontSize: '1.1rem',
               transition: 'color 0.3s ease'
             }
           },
          '.MuiTabs-indicator': {
            display: 'none'
          }
        }}
      >
        {tabs.map((tab, index) => (
          <Tab
            key={index}
            icon={tab.icon}
            label={tab.label}
            iconPosition="start"
            sx={{ 
              display: 'flex', 
              flexDirection: 'row', 
              gap: 1.2,
              padding: '8px 16px',
              minHeight: '48px'
            }}
          />
        ))}
      </Tabs>
    </Box>
  );
}

// Navigation component
function Navigation({ mobileOpen, handleDrawerToggle, drawerOpen, toggleDrawer }) {
  const location = useLocation()
  
  const menuItems = [
    { text: '仪表板', icon: <DashboardIcon />, path: '/' },
    { text: '简历生成', icon: <ResumeIcon />, path: '/resume' },
    { text: '求职信生成', icon: <CoverLetterIcon />, path: '/cover-letter' },
    { text: 'LinkedIn自动化', icon: <LinkedInIcon />, path: '/linkedin' },
    { text: '使用指南', icon: <MenuBookIcon />, path: '/guide' }
    // 移除设置项
  ];

  const drawer = (
    <Box sx={{ width: drawerOpen ? 210 : 70, height: '100%', bgcolor: 'background.paper', transition: 'width 0.3s ease' }}>
      <Box sx={{ p: 2, borderBottom: '1px solid #333', display: 'flex', justifyContent: drawerOpen ? 'space-between' : 'center', alignItems: 'center', height: '64px' }}>
        {drawerOpen ? (
          <>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
              <Box>
                <Typography variant="h6" sx={{ color: 'primary.main', fontWeight: 700 }}>
                  JOBOT
                </Typography>
                <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                  智能求职助手
                </Typography>
              </Box>
              <img 
                src="/image.png" 
                alt="AIHawk Logo" 
                style={{ width: '48px', height: '48px', objectFit: 'contain' }}
              />
            </Box>
            <IconButton onMouseEnter={toggleDrawer} sx={{ color: 'text.secondary' }}>
              <MenuIcon />
            </IconButton>
          </>
        ) : (
          <IconButton onMouseEnter={toggleDrawer} sx={{ color: 'primary.main' }}>
            <MenuIcon />
          </IconButton>
        )}
      </Box>
      <List>
        {menuItems.map((item) => {
          const listItem = (
            <ListItem
              key={item.text}
              component={Link}
              to={item.path}
              sx={{
                color: location.pathname === item.path ? 'primary.main' : 'text.primary',
                bgcolor: location.pathname === item.path ? 'rgba(0, 212, 255, 0.1)' : 'transparent',
                '&:hover': {
                  bgcolor: 'rgba(0, 212, 255, 0.08)',
                  transform: !drawerOpen ? 'translateX(6px)' : 'none',
                  transition: 'all 0.25s cubic-bezier(0.4, 0, 0.2, 1)',
                  '& .MuiListItemIcon-root': {
                    transform: !drawerOpen ? 'scale(1.15)' : 'scale(1.05)',
                    color: '#00d4ff'
                  }
                },
                justifyContent: drawerOpen ? 'flex-start' : 'center',
                px: drawerOpen ? 2 : 1,
                py: 1.5,
                borderRadius: drawerOpen ? '0 25px 25px 0' : '8px',
                mx: drawerOpen ? 0 : 1,
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                position: 'relative',
                overflow: 'hidden',
                '&::before': !drawerOpen ? {
                  content: '""',
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  width: '3px',
                  height: '100%',
                  background: 'linear-gradient(180deg, transparent, #00d4ff, transparent)',
                  opacity: 0,
                  transition: 'opacity 0.3s ease'
                } : {},
                '&:hover::before': !drawerOpen ? {
                  opacity: 1
                } : {}
              }}
            >
              <ListItemIcon sx={{
                color: 'inherit',
                minWidth: drawerOpen ? 40 : 'auto',
                mr: drawerOpen ? 2 : 0,
                transition: 'all 0.25s cubic-bezier(0.4, 0, 0.2, 1)'
              }}>
                {item.icon}
              </ListItemIcon>
              {drawerOpen && <ListItemText primary={item.text} />}
            </ListItem>
          )

          // 当侧边栏收起时，为图标添加现代化的tooltip
          if (!drawerOpen) {
            return (
              <Tooltip
                key={item.text}
                title={item.text}
                placement="right"
                arrow={false}
                TransitionComponent={Fade}
                TransitionProps={{ timeout: 200 }}
                enterDelay={100}
                leaveDelay={50}
              >
                {listItem}
              </Tooltip>
            )
          }

          return listItem
        })}
      </List>
    </Box>
  )

  return (
    <>
      <Drawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{ keepMounted: true }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': { boxSizing: 'border-box', width: 210 }
        }}
      >
        {drawer}
      </Drawer>
      <Drawer
        variant="permanent"
        sx={{
          display: { xs: 'none', md: 'block' },
          '& .MuiDrawer-paper': { 
            boxSizing: 'border-box', 
            width: drawerOpen ? 210 : 70,
            transition: 'width 0.3s ease',
            overflowX: 'hidden'
          }
        }}
        open
      >
        {drawer}
      </Drawer>
    </>
  )
}

// Dashboard component
function Dashboard() {
  const [apiStatus, setApiStatus] = useState('')
  const [loading, setLoading] = useState(false)
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' })

  const checkBackendHealth = async () => {
    setLoading(true)
    try {
      await axios.get('http://localhost:8003/api/health')
      setApiStatus('连接正常')
      setSnackbar({ open: true, message: '后端服务连接成功！', severity: 'success' })
    } catch (error) {
      setApiStatus('连接失败')
      setSnackbar({ open: true, message: `连接失败: ${error.message}`, severity: 'error' })
    } finally {
      setLoading(false)
    }
  }

  const features = [
    {
      title: '智能简历生成',
      description: '基于职位描述自动生成匹配的简历',
      icon: <ResumeIcon sx={{ fontSize: 40, color: 'primary.main' }} />,
      path: '/resume'
    },
    {
      title: '求职信生成',
      description: '为特定职位创建个性化求职信',
      icon: <CoverLetterIcon sx={{ fontSize: 40, color: 'secondary.main' }} />,
      path: '/cover-letter'
    },
    {
      title: 'LinkedIn自动化',
      description: '自动化LinkedIn求职流程和职位申请',
      icon: <LinkedInIcon sx={{ fontSize: 40, color: '#4caf50' }} />,
      path: '/linkedin'
    }
  ]

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4, paddingTop: '2cm' }}>
      <Box sx={{ mb: 4, textAlign: 'center' }}>
        <Typography variant="h1" sx={{ mb: 2, background: 'linear-gradient(45deg, #00d4ff, #ff6b35)', WebkitBackgroundClip: 'text', WebkitTextFillColor: 'transparent', display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
          <AIIcon sx={{ fontSize: '2.5rem', color: '#00d4ff' }} />
          Jobot in | Assistant & Automation 领英求职助手
        </Typography>
        <Typography variant="h6" sx={{ color: 'text.secondary', mb: 4 }}>
          Your jobing mate AI ✨ 助力您的自动化求职之旅，打造完美简历及求职信
        </Typography>
      </Box>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <InfoIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">系统状态</Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Button 
                  variant="contained" 
                  onClick={checkBackendHealth}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <CheckIcon />}
                >
                  {loading ? '检测中...' : '检测后端连接'}
                </Button>
                {apiStatus && (
                  <Chip 
                    label={apiStatus}
                    color={apiStatus === '连接正常' ? 'success' : 'error'}
                    variant="outlined"
                  />
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <WorkIcon sx={{ mr: 1, color: 'secondary.main' }} />
                <Typography variant="h6">快速开始</Typography>
              </Box>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mb: 2 }}>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  1. 输入职位URL或描述
                </Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  2. 生成个性化简历和求职信
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Typography variant="h4" sx={{ mb: 3, color: 'text.primary' }}>核心功能</Typography>
      <Grid container spacing={3} justifyContent="center">
        {features.map((feature, index) => {
          // 为每个卡片定制装饰线颜色
          const getDecorationColors = (index) => {
            switch(index) {
              case 0: // 智能简历生成
                return {
                  left: 'linear-gradient(180deg, transparent 0%, rgba(0, 212, 255, 0.6) 50%, transparent 100%)',
                  right: 'linear-gradient(180deg, transparent 0%, rgba(0, 212, 255, 0.3) 50%, transparent 100%)'
                }
              case 1: // 求职信生成
                return {
                  left: 'linear-gradient(180deg, transparent 0%, rgba(255, 107, 53, 0.6) 50%, transparent 100%)',
                  right: 'linear-gradient(180deg, transparent 0%, rgba(255, 107, 53, 0.3) 50%, transparent 100%)'
                }
              case 2: // LinkedIn自动化
                return {
                  left: 'linear-gradient(180deg, transparent 0%, rgba(76, 175, 80, 0.6) 50%, transparent 100%)',
                  right: 'linear-gradient(180deg, transparent 0%, rgba(76, 175, 80, 0.3) 50%, transparent 100%)'
                }
              default:
                return {
                  left: 'linear-gradient(180deg, transparent 0%, rgba(0, 212, 255, 0.6) 50%, transparent 100%)',
                  right: 'linear-gradient(180deg, transparent 0%, rgba(255, 107, 53, 0.6) 50%, transparent 100%)'
                }
            }
          }

          const colors = getDecorationColors(index)

          return (
            <Grid item xs={12} md={4} key={index}>
              <Card
                sx={{
                  height: '100%',
                  cursor: 'pointer',
                  position: 'relative',
                  overflow: 'hidden',
                  '&::before': {
                    content: '""',
                    position: 'absolute',
                    left: 0,
                    top: '20%',
                    width: '1px',
                    height: '60%',
                    background: colors.left,
                    zIndex: 1
                  },
                  '&::after': {
                    content: '""',
                    position: 'absolute',
                    right: 0,
                    top: '20%',
                    width: '1px',
                    height: '60%',
                    background: colors.right,
                    zIndex: 1
                  }
                }}
                component={Link}
                to={feature.path}
              >
                <CardContent sx={{ textAlign: 'center', p: 3, position: 'relative', zIndex: 2 }}>
                  <Box sx={{ mb: 2 }}>
                    {feature.icon}
                  </Box>
                  <Typography variant="h6" sx={{ mb: 1 }}>
                    {feature.title}
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                    {feature.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          )
        })}
      </Grid>

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  )
}

// Resume Generator component
function ResumeGenerator() {
  const [jobUrl, setJobUrl] = useState('')
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState(null)
  const [pdfLoading, setPdfLoading] = useState(false)
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' })
  const [uploadedResume, setUploadedResume] = useState(null)
  const [resumeUploading, setResumeUploading] = useState(false)
  const [optimizedResumeHtml, setOptimizedResumeHtml] = useState(null)

  // 清除页面内容的函数
  const clearPageContent = () => {
    setJobUrl('')
    setResult(null)
    setUploadedResume(null)
    setOptimizedResumeHtml(null)
    // 清除sessionStorage中的相关数据
    sessionStorage.removeItem('resumeJobUrl')
    sessionStorage.removeItem('resumeResult')
    sessionStorage.removeItem('uploadedResume')
    sessionStorage.removeItem('optimizedResumeHtml')
    sessionStorage.removeItem('jobInfo')
    sessionStorage.removeItem('coverLetterHtml')
  }



  // 从sessionStorage恢复状态
  useEffect(() => {
    const savedJobUrl = sessionStorage.getItem('resumeJobUrl')
    const savedResult = sessionStorage.getItem('resumeResult')
    const savedUploadedResume = sessionStorage.getItem('uploadedResume')
    const savedOptimizedResumeHtml = sessionStorage.getItem('optimizedResumeHtml')

    if (savedJobUrl) {
      setJobUrl(savedJobUrl)
    }
    if (savedResult) {
      try {
        setResult(JSON.parse(savedResult))
      } catch (e) {
        console.error('Failed to parse saved result:', e)
      }
    }
    if (savedUploadedResume) {
      try {
        setUploadedResume(JSON.parse(savedUploadedResume))
      } catch (e) {
        console.error('Failed to parse saved uploaded resume:', e)
      }
    }
    if (savedOptimizedResumeHtml) {
      setOptimizedResumeHtml(savedOptimizedResumeHtml)
    }
  }, [])



  const handleResumeUpload = async (event) => {
    const file = event.target.files[0]
    if (!file) return

    if (file.type !== 'application/pdf') {
      setSnackbar({ open: true, message: '请上传PDF格式的简历文件', severity: 'warning' })
      return
    }

    setResumeUploading(true)
    try {
      const formData = new FormData()
      formData.append('resume', file)

      const response = await axios.post('http://localhost:8003/api/resume/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })

      if (response.data.status === 'success') {
        setUploadedResume(response.data.resume_data)
        // 保存到localStorage
        localStorage.setItem('uploadedResume', JSON.stringify(response.data.resume_data))
        setSnackbar({ open: true, message: '简历上传并解析成功！', severity: 'success' })
      } else {
        setSnackbar({ open: true, message: response.data.message, severity: 'error' })
      }
    } catch (error) {
      setSnackbar({ open: true, message: `简历上传失败: ${error.message}`, severity: 'error' })
    } finally {
      setResumeUploading(false)
    }
  }

  const handleGenerate = async () => {
    if (!jobUrl.trim()) {
      setSnackbar({ open: true, message: '请输入职位URL', severity: 'warning' })
      return
    }

    // 保存jobUrl到sessionStorage
    sessionStorage.setItem('resumeJobUrl', jobUrl)

    setLoading(true)
    try {
      const requestData = { job_url: jobUrl }
      if (uploadedResume) {
        requestData.user_resume = uploadedResume
      }

      const response = await axios.post('http://localhost:8003/api/resume/generate', requestData)
      setResult(response.data)

      // 保存result到sessionStorage
      sessionStorage.setItem('resumeResult', JSON.stringify(response.data))

      // 如果有优化后的简历内容，保存它
      if (response.data.optimized_resume) {
        setOptimizedResumeHtml(response.data.optimized_resume)
        // 保存到sessionStorage供求职信生成使用
        sessionStorage.setItem('optimizedResumeHtml', response.data.optimized_resume)
      }

      // 保存职位信息到sessionStorage供求职信生成使用
      if (response.data.job_info) {
        sessionStorage.setItem('jobInfo', JSON.stringify(response.data.job_info))
      }

      if (response.data.status === 'success') {
        const message = uploadedResume && response.data.optimized_resume
          ? '简历优化成功！已生成针对性简历内容。'
          : '职位信息解析成功！'
        setSnackbar({ open: true, message, severity: 'success' })
      } else {
        setSnackbar({ open: true, message: response.data.message, severity: 'error' })
      }
    } catch (error) {
      setSnackbar({ open: true, message: `生成失败: ${error.message}`, severity: 'error' })
    } finally {
      setLoading(false)
    }
  }

  const handleGeneratePDF = async () => {
    if (!result || result.status !== 'success') {
      setSnackbar({ open: true, message: '请先解析职位信息', severity: 'warning' })
      return
    }

    setPdfLoading(true)
    try {
      const requestData = {}

      // 如果有优化后的HTML内容，使用它
      if (optimizedResumeHtml) {
        requestData.optimized_resume_html = optimizedResumeHtml
      }

      const response = await axios.post('http://localhost:8003/api/resume/generate-pdf', requestData)

      if (response.data.status === 'success') {
        // 下载PDF文件
        const pdfData = response.data.pdf_data
        const filename = response.data.filename

        // 创建下载链接
        const byteCharacters = atob(pdfData)
        const byteNumbers = Array.from({ length: byteCharacters.length })
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i)
        }
        const byteArray = new Uint8Array(byteNumbers)
        const blob = new Blob([byteArray], { type: 'application/pdf' })

        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = filename
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        setSnackbar({ open: true, message: 'PDF简历生成并下载成功！', severity: 'success' })
      } else {
        setSnackbar({ open: true, message: response.data.message, severity: 'error' })
      }
    } catch (error) {
      setSnackbar({ open: true, message: `PDF生成失败: ${error.message}`, severity: 'error' })
    } finally {
      setPdfLoading(false)
    }
  }

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h2" sx={{ color: 'primary.main' }}>
          智能简历生成
        </Typography>
        <Button
          variant="outlined"
          color="secondary"
          onClick={clearPageContent}
          sx={{ minWidth: 120 }}
        >
          清除内容
        </Button>
      </Box>
      
      {/* 简历上传区域 */}
      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3 }}>上传现有简历（可选）</Typography>
          <Typography variant="body2" sx={{ mb: 2, color: 'text.secondary' }}>
            上传您的现有简历，AI将结合职位要求为您生成更有针对性的优化简历
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Button
              variant="outlined"
              component="label"
              startIcon={resumeUploading ? <CircularProgress size={20} /> : <UploadIcon />}
              disabled={resumeUploading}
              sx={{ minWidth: 150 }}
            >
              {resumeUploading ? '上传中...' : '上传PDF简历'}
              <input
                type="file"
                hidden
                accept=".pdf"
                onChange={handleResumeUpload}
              />
            </Button>

            {uploadedResume && (
              <Chip
                label="简历已上传"
                color="success"
                variant="outlined"
                onDelete={() => {
                  setUploadedResume(null)
                  sessionStorage.removeItem('uploadedResume')
                }}
              />
            )}
          </Box>

          {uploadedResume && (
            <Alert severity="success" sx={{ mb: 2 }}>
              简历解析成功！AI将基于您的简历和目标职位生成优化版本。
            </Alert>
          )}
        </CardContent>
      </Card>

      <Card sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 3 }}>输入职位信息</Typography>
          <TextField
            fullWidth
            label="职位URL"
            placeholder="请输入职位链接..."
            value={jobUrl}
            onChange={(e) => setJobUrl(e.target.value)}
            sx={{ mb: 3 }}
            variant="outlined"
          />
          <Button
            variant="contained"
            size="large"
            onClick={handleGenerate}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <ResumeIcon />}
            sx={{ minWidth: 150 }}
          >
            {loading ? (uploadedResume ? '优化中...' : '解析中...') : (uploadedResume ? '生成优化简历' : '解析职位')}
          </Button>
        </CardContent>
      </Card>

      {result && (
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>生成结果</Typography>
            {result.status === 'success' ? (
              <Box>
                <Alert
                  severity={result.fallback_mode ? "warning" : "success"}
                  sx={{ mb: 2 }}
                >
                  {result.message}
                  {result.fallback_mode && (
                    <Box sx={{ mt: 1 }}>
                      <Typography variant="caption" display="block">
                        注意：由于网络连接问题，系统使用了降级模式进行解析，结果可能不够精确。
                      </Typography>
                    </Box>
                  )}
                </Alert>
                {result.job_info && (
                  <Box>
                    <Typography variant="subtitle1" sx={{ mb: 2 }}>📋 解析的职位信息：</Typography>
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" sx={{ mb: 1 }}><strong>🏢 公司：</strong>{result.job_info.company}</Typography>
                      <Typography variant="body2" sx={{ mb: 1 }}><strong>💼 职位：</strong>{result.job_info.role}</Typography>
                      <Typography variant="body2" sx={{ mb: 1 }}><strong>📍 地点：</strong>{result.job_info.location}</Typography>
                      {result.job_info.url && (
                        <Typography variant="body2" sx={{ mb: 2 }}>
                          <strong>🔗 链接：</strong>
                          <a href={result.job_info.url} target="_blank" rel="noopener noreferrer" style={{ color: '#1976d2', textDecoration: 'none' }}>
                            {result.job_info.url.length > 50 ? result.job_info.url.substring(0, 50) + '...' : result.job_info.url}
                          </a>
                        </Typography>
                      )}
                    </Box>
                    {result.job_info.description && result.job_info.description !== "职位描述信息不完整" && (
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="subtitle2" sx={{ mb: 1 }}>📝 职位描述与要求：</Typography>
                        <Box sx={{
                          p: 2,
                          bgcolor: 'white',
                          borderRadius: 1,
                          border: '1px solid #e0e0e0',
                          maxHeight: '300px',
                          overflowY: 'auto'
                        }}>
                          <Typography variant="body2" sx={{
                            whiteSpace: 'pre-wrap',
                            lineHeight: 1.6,
                            color: '#333333'
                          }}>
                            {result.job_info.description}
                          </Typography>
                        </Box>
                      </Box>
                    )}
                    {result.fallback_mode && (
                      <Box sx={{ mt: 2, p: 2, bgcolor: 'warning.light', borderRadius: 1 }}>
                        <Typography variant="caption" sx={{ color: 'warning.dark' }}>
                          💡 提示：为获得更准确的结果，请检查网络连接后重试，或确保Google API服务可正常访问。
                        </Typography>
                      </Box>
                    )}

                    {/* 优化简历预览 */}
                    {optimizedResumeHtml && (
                      <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #333' }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                          <Typography variant="subtitle1">优化后的简历预览</Typography>
                          <Box>
                            <Button
                              variant="outlined"
                              size="small"
                              onClick={() => {
                                const newWindow = window.open('', '_blank');
                                newWindow.document.write(optimizedResumeHtml);
                                newWindow.document.close();
                              }}
                              sx={{ mr: 1 }}
                            >
                              新窗口预览
                            </Button>
                            <Button
                              variant="outlined"
                              size="small"
                              onClick={() => {
                                const blob = new Blob([optimizedResumeHtml], { type: 'text/html' });
                                const url = URL.createObjectURL(blob);
                                const a = document.createElement('a');
                                a.href = url;
                                a.download = 'resume.html';
                                a.click();
                                URL.revokeObjectURL(url);
                              }}
                            >
                              下载HTML
                            </Button>
                          </Box>
                        </Box>
                        <Box
                          sx={{
                            maxHeight: 500,
                            overflow: 'auto',
                            border: '1px solid #555',
                            borderRadius: 1,
                            bgcolor: 'white',
                            '& iframe': {
                              width: '100%',
                              height: '500px',
                              border: 'none'
                            }
                          }}
                        >
                          <iframe
                            srcDoc={optimizedResumeHtml}
                            style={{ width: '100%', height: '500px', border: 'none' }}
                            title="简历预览"
                          />
                        </Box>
                      </Box>
                    )}

                    {/* PDF生成区域 */}
                    <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #333' }}>
                      <Typography variant="subtitle1" sx={{ mb: 2 }}>生成PDF简历</Typography>



                      {/* PDF生成按钮 */}
                      <Button
                        variant="contained"
                        color="secondary"
                        size="large"
                        onClick={handleGeneratePDF}
                        disabled={pdfLoading}
                        startIcon={pdfLoading ? <CircularProgress size={20} /> : <DownloadIcon />}
                        sx={{ minWidth: 150 }}
                      >
                        {pdfLoading ? '生成中...' : '生成PDF简历'}
                      </Button>
                    </Box>
                  </Box>
                )}
              </Box>
            ) : result.status === 'warning' ? (
              <Box>
                <Alert severity="warning" sx={{ mb: 2 }}>
                  {result.message}
                </Alert>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  系统将尝试使用基础解析模式继续处理您的请求。
                </Typography>
              </Box>
            ) : (
              <Box>
                <Alert severity="error" sx={{ mb: 2 }}>
                  {result.message}
                </Alert>
                <Box sx={{ mt: 2, p: 2, bgcolor: 'error.light', borderRadius: 1 }}>
                  <Typography variant="subtitle2" sx={{ color: 'error.dark', mb: 1 }}>
                    可能的解决方案：
                  </Typography>
                  <Typography variant="caption" sx={{ color: 'error.dark' }} component="div">
                    • 检查网络连接是否正常<br/>
                    • 确认职位URL是否有效<br/>
                    • 稍后重试或联系技术支持<br/>
                    • 检查防火墙或代理设置
                  </Typography>
                </Box>
              </Box>
            )}
          </CardContent>
        </Card>
      )}

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  )
}

// Placeholder components for other routes
function CoverLetterGenerator() {
  const [jobInfo, setJobInfo] = useState(null)
  const [optimizedResumeHtml, setOptimizedResumeHtml] = useState('')
  const [coverLetterHtml, setCoverLetterHtml] = useState('')
  const [loading, setLoading] = useState(false)
  const [pdfLoading, setPdfLoading] = useState(false)

  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' })

  // 清除页面内容的函数
  const clearPageContent = () => {
    setJobInfo(null)
    setOptimizedResumeHtml('')
    setCoverLetterHtml('')
    // 清除sessionStorage中的相关数据
    sessionStorage.removeItem('jobInfo')
    sessionStorage.removeItem('optimizedResumeHtml')
    sessionStorage.removeItem('coverLetterHtml')
    sessionStorage.removeItem('resumeJobUrl')
    sessionStorage.removeItem('resumeResult')
    sessionStorage.removeItem('uploadedResume')
  }



  // 从sessionStorage获取之前的数据
  useEffect(() => {
    const savedJobInfo = sessionStorage.getItem('jobInfo')
    const savedOptimizedResume = sessionStorage.getItem('optimizedResumeHtml')
    const savedCoverLetter = sessionStorage.getItem('coverLetterHtml')

    if (savedJobInfo) {
      try {
        const parsedJobInfo = JSON.parse(savedJobInfo)
        console.log('求职信页面 - 加载的jobInfo:', parsedJobInfo)
        console.log('求职信页面 - 职位URL:', parsedJobInfo.url)
        setJobInfo(parsedJobInfo)
      } catch (e) {
        console.error('Failed to parse saved job info:', e)
      }
    }
    if (savedOptimizedResume) {
      setOptimizedResumeHtml(savedOptimizedResume)
    }
    if (savedCoverLetter) {
      setCoverLetterHtml(savedCoverLetter)
    }
  }, [])

  const handleGenerateCoverLetter = async () => {
    if (!jobInfo || !optimizedResumeHtml) {
      setSnackbar({
        open: true,
        message: '请先在简历生成页面完成简历优化',
        severity: 'warning'
      })
      return
    }

    // 调试日志：验证数据传递
    console.log('求职信生成 - jobInfo:', jobInfo)
    console.log('求职信生成 - 职位URL:', jobInfo.url)

    setLoading(true)
    try {
      const response = await axios.post('http://localhost:8003/api/cover-letter/generate', {
        job_info: jobInfo,
        optimized_resume_html: optimizedResumeHtml
      })

      if (response.data.status === 'success') {
        setCoverLetterHtml(response.data.cover_letter_html)
        sessionStorage.setItem('coverLetterHtml', response.data.cover_letter_html)
        setSnackbar({
          open: true,
          message: '求职信生成成功！',
          severity: 'success'
        })
      } else {
        throw new Error(response.data.message || '求职信生成失败')
      }
    } catch (error) {
      console.error('求职信生成错误:', error)
      setSnackbar({
        open: true,
        message: error.response?.data?.message || '求职信生成失败，请重试',
        severity: 'error'
      })
    } finally {
      setLoading(false)
    }
  }



  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h2" sx={{ color: 'primary.main' }}>
          求职信生成
        </Typography>
        <Button
          variant="outlined"
          color="secondary"
          onClick={clearPageContent}
          sx={{ minWidth: 120 }}
        >
          清除内容
        </Button>
      </Box>

      {/* 职位信息显示 */}
      {jobInfo ? (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
              <WorkIcon sx={{ mr: 1, color: 'secondary.main' }} />
              目标职位信息
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={3}>
                <Typography variant="body2" color="text.secondary">公司</Typography>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>{jobInfo.company}</Typography>
              </Grid>
              <Grid item xs={12} md={3}>
                <Typography variant="body2" color="text.secondary">职位</Typography>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>{jobInfo.role}</Typography>
              </Grid>
              <Grid item xs={12} md={3}>
                <Typography variant="body2" color="text.secondary">地点</Typography>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>{jobInfo.location}</Typography>
              </Grid>
              <Grid item xs={12} md={3}>
                <Typography variant="body2" color="text.secondary">职位链接</Typography>
                <a
                  href={jobInfo.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    color: 'white',
                    textDecoration: 'none',
                    fontWeight: 500,
                    display: 'inline-block',
                    fontSize: '0.875rem',
                    lineHeight: 1.4,
                    transition: 'color 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.color = '#90caf9'
                    e.target.style.textDecoration = 'underline'
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.color = 'white'
                    e.target.style.textDecoration = 'none'
                  }}
                  onClick={() => {
                    console.log('点击职位链接，URL:', jobInfo.url)
                  }}
                >
                  查看原职位 ↗
                </a>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      ) : (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Alert severity="info">
              请先在"简历生成"页面完成职位解析和简历优化，然后返回此页面生成求职信。
            </Alert>
          </CardContent>
        </Card>
      )}

      {/* 生成求职信按钮 */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Button
            variant="contained"
            color="secondary"
            size="large"
            onClick={handleGenerateCoverLetter}
            disabled={loading || !jobInfo || !optimizedResumeHtml}
            startIcon={loading ? <CircularProgress size={20} /> : <EmailIcon />}
            sx={{ minWidth: 200 }}
          >
            {loading ? '生成中...' : '生成该职位求职信'}
          </Button>
        </CardContent>
      </Card>

      {/* 求职信预览 */}
      {coverLetterHtml && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">求职信预览</Typography>
              <Box>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => {
                    const newWindow = window.open('', '_blank')
                    newWindow.document.write(coverLetterHtml)
                    newWindow.document.close()
                  }}
                  sx={{ mr: 1 }}
                >
                  新窗口预览
                </Button>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => {
                    const blob = new Blob([coverLetterHtml], { type: 'text/html' })
                    const url = URL.createObjectURL(blob)
                    const a = document.createElement('a')
                    a.href = url
                    a.download = 'cover-letter.html'
                    a.click()
                    URL.revokeObjectURL(url)
                  }}
                >
                  下载HTML
                </Button>
              </Box>
            </Box>
            <Box
              sx={{
                maxHeight: 600,
                overflow: 'auto',
                border: '1px solid #555',
                borderRadius: 1,
                bgcolor: 'white',
                '& iframe': {
                  width: '100%',
                  height: '600px',
                  border: 'none'
                }
              }}
            >
              <iframe
                srcDoc={coverLetterHtml}
                style={{ width: '100%', height: '600px', border: 'none' }}
                title="求职信预览"
              />
            </Box>
          </CardContent>
        </Card>
      )}

      {/* PDF生成区域 */}
      {coverLetterHtml && (
        <Card>
          <CardContent>
            <Typography variant="h6" sx={{ mb: 2 }}>生成PDF求职信</Typography>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                color="secondary"
                size="large"
                startIcon={pdfLoading ? <CircularProgress size={20} /> : <DownloadIcon />}
                disabled={pdfLoading}
                sx={{ minWidth: 150 }}
                onClick={async () => {
                  setPdfLoading(true)
                  try {
                    setSnackbar({
                      open: true,
                      message: '正在生成PDF求职信，请稍候...',
                      severity: 'info'
                    })

                    const response = await axios.post('http://localhost:8003/api/cover-letter/generate-pdf', {
                      cover_letter_html: coverLetterHtml
                    })

                    if (response.data.status === 'success') {
                      // 下载PDF文件
                      const pdfData = response.data.pdf_data

                      // 生成智能文件名
                      const generateFilename = () => {
                        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
                        const companyName = result?.job_info?.company_name || '公司'
                        const position = result?.job_info?.position_title || '职位'
                        return `求职信_${companyName}_${position}_${timestamp}.pdf`
                      }

                      const filename = response.data.filename || generateFilename()

                      // 将base64数据转换为Blob
                      const byteCharacters = atob(pdfData)
                      const byteNumbers = Array.from({ length: byteCharacters.length })
                      for (let i = 0; i < byteCharacters.length; i++) {
                        byteNumbers[i] = byteCharacters.charCodeAt(i)
                      }
                      const byteArray = new Uint8Array(byteNumbers)
                      const blob = new Blob([byteArray], { type: 'application/pdf' })

                      // 创建下载链接
                      const url = window.URL.createObjectURL(blob)
                      const link = document.createElement('a')
                      link.href = url
                      link.download = filename
                      document.body.appendChild(link)
                      link.click()
                      document.body.removeChild(link)
                      window.URL.revokeObjectURL(url)

                      setSnackbar({
                        open: true,
                        message: 'PDF求职信生成并下载成功！',
                        severity: 'success'
                      })
                    } else {
                      throw new Error(response.data.message || 'PDF生成失败')
                    }
                  } catch (error) {
                    console.error('PDF生成错误:', error)

                    // 提供用户友好的错误信息
                    let userMessage = 'PDF生成失败，请重试'
                    const serverMessage = error.response?.data?.message || error.message

                    if (serverMessage) {
                      if (serverMessage.includes('Chrome') || serverMessage.includes('浏览器')) {
                        userMessage = '浏览器检测失败，请确保已安装Chrome浏览器'
                      } else if (serverMessage.includes('超时') || serverMessage.includes('timeout')) {
                        userMessage = 'PDF生成超时，请检查网络连接后重试'
                      } else if (serverMessage.includes('重试')) {
                        userMessage = 'PDF生成失败，系统已自动重试，请稍后再试'
                      } else if (serverMessage.includes('HTML内容')) {
                        userMessage = '求职信内容有误，请重新生成求职信后再试'
                      } else {
                        userMessage = `PDF生成失败: ${serverMessage}`
                      }
                    }

                    setSnackbar({
                      open: true,
                      message: userMessage,
                      severity: 'error'
                    })
                  } finally {
                    setPdfLoading(false)
                  }
                }}
              >
                {pdfLoading ? '生成中...' : '生成PDF求职信'}
              </Button>
            </Box>
          </CardContent>
        </Card>
      )}

      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert severity={snackbar.severity} onClose={() => setSnackbar({ ...snackbar, open: false })}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Container>
  )
}



// Settings component is now imported from separate file

function App() {
  const [mobileOpen, setMobileOpen] = useState(false)
  const [drawerOpen, setDrawerOpen] = useState(false)

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen)
  }

  const toggleDrawer = () => {
    setDrawerOpen(!drawerOpen)
  }

  return (
    <ThemeProvider theme={darkTheme}>
      <CssBaseline />
      <SettingsProvider>
        <LinkedInProvider>
          <Router>
          <Box sx={{ display: 'flex' }}>
            <AppBar
              position="fixed"
              sx={{
                width: { md: `calc(100% - ${drawerOpen ? 210 : 70}px)` },
                ml: { md: drawerOpen ? '210px' : '70px' },
                bgcolor: 'background.paper',
                borderBottom: '1px solid #333',
                transition: 'margin-left 0.3s ease, width 0.3s ease'
              }}
              elevation={0}
            >
              <Toolbar sx={{ height: '64px', minHeight: '64px !important', display: 'flex', justifyContent: 'space-between' }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <IconButton
                    color="inherit"
                    aria-label="open drawer"
                    edge="start"
                    onClick={handleDrawerToggle}
                    sx={{ mr: 2, display: { md: 'none' } }}
                  >
                    <MenuIcon />
                  </IconButton>
                  <Typography variant="h6" noWrap component="div" sx={{ color: 'text.primary' }}>
                    Job🤖t in ✨
                  </Typography>
                </Box>
                {/* 搜索框和设置Tabs */}
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <DocumentSearchBox />
                  <SettingsTabsInAppBar />
                </Box>
              </Toolbar>
            </AppBar>

            <Navigation
              mobileOpen={mobileOpen}
              handleDrawerToggle={handleDrawerToggle}
              drawerOpen={drawerOpen}
              toggleDrawer={toggleDrawer}
            />

            <Box
              component="main"
              sx={{
                flexGrow: 1,
                width: { md: `calc(100% - ${drawerOpen ? 210 : 70}px)` },
                minHeight: '100vh',
                bgcolor: 'background.default',
                transition: 'width 0.3s ease'
              }}
            >
              <Toolbar />
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/resume" element={<ResumeGenerator />} />
                <Route path="/cover-letter" element={<CoverLetterGenerator />} />
                <Route path="/linkedin" element={
                  <ErrorBoundary>
                    <LinkedInAutomation />
                  </ErrorBoundary>
                } />
                <Route path="/guide" element={<GuidePage />} />
                <Route path="/settings" element={<Settings />} />
              </Routes>
            </Box>
          </Box>
          </Router>
        </LinkedInProvider>
      </SettingsProvider>
    </ThemeProvider>
  )
}

export default App