#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
面试准备模块 - 基于MCP的智能面试准备系统

此模块提供全面的面试准备功能，包括：
- 面试问题预测和生成
- 个性化答案建议
- 技术面试准备
- 行为面试准备
- 模拟面试功能
- 面试反馈和改进建议
- 公司文化匹配分析

作者: AI Assistant
创建时间: 2024
"""

import asyncio
import re
import json
import time
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
import logging
from pathlib import Path
import random

# 导入MCP管理器
try:
    from src.mcp.core.mcp_manager import get_mcp_manager, MCPToolResult
except ImportError:
    # 如果导入失败，创建模拟实现
    class MCPToolResult:
        def __init__(self, success=True, result=None, error=None):
            self.success = success
            self.result = result
            self.error = error
    
    def get_mcp_manager():
        return None

# 导入其他分析器
try:
    from src.mcp.analyzers.job_analyzer import JobAnalysisResult, SkillRequirement
    from src.mcp.analyzers.company_analyzer import CompanyAnalysisResult
except ImportError:
    # 创建模拟类
    @dataclass
    class JobAnalysisResult:
        job_title: str = ""
        required_skills: List = field(default_factory=list)
        preferred_skills: List = field(default_factory=list)
        responsibilities: List[str] = field(default_factory=list)
        company_name: str = ""
    
    @dataclass
    class SkillRequirement:
        name: str = ""
        importance: str = ""
    
    @dataclass
    class CompanyAnalysisResult:
        company_name: str = ""
        culture_values: List[str] = field(default_factory=list)
        interview_process: Dict[str, Any] = field(default_factory=dict)
        company_size: str = ""
        industry: str = ""

# 导入现有模块（保持兼容性）
try:
    from src.utils.logger import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)

try:
    from src.utils.config_loader import ConfigLoader
except ImportError:
    class ConfigLoader:
        @staticmethod
        def load_config(config_path: str) -> Dict[str, Any]:
            return {}


class InterviewType(Enum):
    """面试类型"""
    TECHNICAL = "technical"          # 技术面试
    BEHAVIORAL = "behavioral"        # 行为面试
    CASE_STUDY = "case_study"        # 案例研究
    SYSTEM_DESIGN = "system_design"  # 系统设计
    CODING = "coding"                # 编程面试
    HR_SCREENING = "hr_screening"    # HR筛选
    FINAL_ROUND = "final_round"      # 终面
    PANEL = "panel"                  # 小组面试


class QuestionCategory(Enum):
    """问题类别"""
    GENERAL = "general"                    # 通用问题
    EXPERIENCE = "experience"              # 经验相关
    TECHNICAL_SKILLS = "technical_skills"  # 技术技能
    PROBLEM_SOLVING = "problem_solving"    # 问题解决
    LEADERSHIP = "leadership"              # 领导力
    TEAMWORK = "teamwork"                  # 团队合作
    MOTIVATION = "motivation"              # 动机
    COMPANY_CULTURE = "company_culture"    # 公司文化
    CAREER_GOALS = "career_goals"          # 职业目标
    CHALLENGES = "challenges"              # 挑战处理
    ACHIEVEMENTS = "achievements"          # 成就展示
    WEAKNESSES = "weaknesses"              # 弱点分析


class DifficultyLevel(Enum):
    """难度级别"""
    EASY = "easy"
    MEDIUM = "medium"
    HARD = "hard"
    EXPERT = "expert"


@dataclass
class InterviewQuestion:
    """面试问题数据类"""
    question: str
    category: QuestionCategory
    interview_type: InterviewType
    difficulty: DifficultyLevel
    
    # 问题元数据
    tags: List[str] = field(default_factory=list)
    context: str = ""
    follow_up_questions: List[str] = field(default_factory=list)
    
    # 答案指导
    answer_framework: str = ""
    key_points: List[str] = field(default_factory=list)
    example_answer: str = ""
    
    # 评估标准
    evaluation_criteria: List[str] = field(default_factory=list)
    common_mistakes: List[str] = field(default_factory=list)
    
    # 个性化信息
    relevance_score: float = 0.0
    company_specific: bool = False
    role_specific: bool = False


@dataclass
class AnswerSuggestion:
    """答案建议数据类"""
    question_id: str
    suggested_answer: str
    framework_used: str
    
    # 答案结构
    situation: str = ""
    task: str = ""
    action: str = ""
    result: str = ""
    
    # 个性化元素
    personal_examples: List[str] = field(default_factory=list)
    relevant_skills: List[str] = field(default_factory=list)
    company_alignment: str = ""
    
    # 质量评估
    completeness_score: float = 0.0
    relevance_score: float = 0.0
    impact_score: float = 0.0
    
    # 改进建议
    improvement_suggestions: List[str] = field(default_factory=list)


@dataclass
class MockInterviewSession:
    """模拟面试会话数据类"""
    session_id: str
    interview_type: InterviewType
    duration_minutes: int
    
    # 面试配置
    questions: List[InterviewQuestion] = field(default_factory=list)
    current_question_index: int = 0
    
    # 会话状态
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    is_active: bool = False
    
    # 用户回答
    user_answers: Dict[str, str] = field(default_factory=dict)
    answer_times: Dict[str, float] = field(default_factory=dict)
    
    # 实时反馈
    feedback_notes: List[str] = field(default_factory=list)
    performance_scores: Dict[str, float] = field(default_factory=dict)
    
    # 最终评估
    overall_score: float = 0.0
    strengths: List[str] = field(default_factory=list)
    areas_for_improvement: List[str] = field(default_factory=list)
    detailed_feedback: str = ""


@dataclass
class InterviewPreparationPlan:
    """面试准备计划数据类"""
    plan_id: str
    target_job: JobAnalysisResult
    
    # 可选字段
    target_company: Optional[CompanyAnalysisResult] = None
    
    # 准备时间线
    preparation_days: int = 7
    daily_study_hours: float = 2.0
    
    # 学习内容
    technical_topics: List[str] = field(default_factory=list)
    behavioral_scenarios: List[str] = field(default_factory=list)
    company_research_points: List[str] = field(default_factory=list)
    
    # 练习计划
    practice_questions: List[InterviewQuestion] = field(default_factory=list)
    mock_interview_schedule: List[Dict[str, Any]] = field(default_factory=list)
    
    # 资源推荐
    recommended_resources: List[Dict[str, str]] = field(default_factory=list)
    study_materials: List[str] = field(default_factory=list)
    
    # 进度跟踪
    completed_topics: Set[str] = field(default_factory=set)
    practice_scores: Dict[str, float] = field(default_factory=dict)
    confidence_levels: Dict[str, float] = field(default_factory=dict)


@dataclass
class InterviewPreparationResult:
    """面试准备结果数据类"""
    preparation_id: str
    preparation_plan: Optional[InterviewPreparationPlan] = None
    timestamp: float = field(default_factory=time.time)
    
    # 目标信息
    target_job_title: str = ""
    target_company: str = ""
    interview_date: Optional[str] = None
    
    # 问题库
    predicted_questions: List[InterviewQuestion] = field(default_factory=list)
    personalized_answers: List[AnswerSuggestion] = field(default_factory=list)
    
    # 技能评估
    skill_readiness: Dict[str, float] = field(default_factory=dict)
    knowledge_gaps: List[str] = field(default_factory=list)
    
    # 公司匹配
    culture_fit_score: float = 0.0
    company_specific_prep: List[str] = field(default_factory=list)
    
    # 置信度评估
    overall_readiness: float = 0.0
    confidence_score: float = 0.0
    
    # 元数据
    preparation_duration: float = 0.0
    data_sources: List[str] = field(default_factory=list)


class InterviewPreparationEngine:
    """面试准备引擎主类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化面试准备引擎
        
        Args:
            config: 引擎配置
        """
        self.config = config or {}
        self.mcp_manager = get_mcp_manager()
        
        # 加载问题库和模板
        self.question_bank = self._load_question_bank()
        self.answer_frameworks = self._load_answer_frameworks()
        self.company_questions = self._load_company_specific_questions()
        
        # 活跃的模拟面试会话
        self.active_sessions: Dict[str, MockInterviewSession] = {}
        
        logger.info("面试准备引擎初始化完成")
    
    def _load_question_bank(self) -> Dict[str, List[InterviewQuestion]]:
        """加载面试问题库"""
        question_bank = {
            'general': [
                InterviewQuestion(
                    question="请简单介绍一下你自己。",
                    category=QuestionCategory.GENERAL,
                    interview_type=InterviewType.HR_SCREENING,
                    difficulty=DifficultyLevel.EASY,
                    answer_framework="简洁的个人背景 + 相关经验 + 为什么适合这个职位",
                    key_points=["教育背景", "工作经验", "核心技能", "职业目标"],
                    evaluation_criteria=["清晰度", "相关性", "简洁性", "自信度"]
                ),
                InterviewQuestion(
                    question="为什么想要这个职位？",
                    category=QuestionCategory.MOTIVATION,
                    interview_type=InterviewType.HR_SCREENING,
                    difficulty=DifficultyLevel.EASY,
                    answer_framework="职位吸引力 + 个人匹配度 + 职业发展",
                    key_points=["职位职责匹配", "公司文化认同", "成长机会", "技能发展"],
                    evaluation_criteria=["真诚度", "准备充分", "与职位匹配", "长期承诺"]
                ),
                InterviewQuestion(
                    question="你的优势和劣势是什么？",
                    category=QuestionCategory.WEAKNESSES,
                    interview_type=InterviewType.BEHAVIORAL,
                    difficulty=DifficultyLevel.MEDIUM,
                    answer_framework="优势：具体技能 + 实例证明；劣势：真实弱点 + 改进措施",
                    key_points=["具体优势", "证明实例", "真实劣势", "改进计划"],
                    evaluation_criteria=["自我认知", "诚实度", "改进意识", "平衡性"]
                )
            ],
            'technical': [
                InterviewQuestion(
                    question="请解释一下你最熟悉的编程语言的特点。",
                    category=QuestionCategory.TECHNICAL_SKILLS,
                    interview_type=InterviewType.TECHNICAL,
                    difficulty=DifficultyLevel.MEDIUM,
                    answer_framework="语言特性 + 适用场景 + 个人经验 + 项目实例",
                    key_points=["语言特性", "性能特点", "生态系统", "实际应用"],
                    evaluation_criteria=["技术深度", "实践经验", "理解准确性", "表达清晰"]
                ),
                InterviewQuestion(
                    question="描述一个你解决过的复杂技术问题。",
                    category=QuestionCategory.PROBLEM_SOLVING,
                    interview_type=InterviewType.TECHNICAL,
                    difficulty=DifficultyLevel.HARD,
                    answer_framework="STAR方法：情况 + 任务 + 行动 + 结果",
                    key_points=["问题复杂性", "解决思路", "技术方案", "最终效果"],
                    evaluation_criteria=["问题分析", "解决方案", "技术能力", "结果导向"]
                )
            ],
            'behavioral': [
                InterviewQuestion(
                    question="描述一次你在团队中遇到冲突的情况，你是如何处理的？",
                    category=QuestionCategory.TEAMWORK,
                    interview_type=InterviewType.BEHAVIORAL,
                    difficulty=DifficultyLevel.MEDIUM,
                    answer_framework="STAR方法：具体情况 + 你的角色 + 采取的行动 + 最终结果",
                    key_points=["冲突原因", "沟通技巧", "解决方案", "团队和谐"],
                    evaluation_criteria=["沟通能力", "冲突解决", "团队合作", "情商表现"]
                ),
                InterviewQuestion(
                    question="告诉我一个你领导项目或团队的经历。",
                    category=QuestionCategory.LEADERSHIP,
                    interview_type=InterviewType.BEHAVIORAL,
                    difficulty=DifficultyLevel.HARD,
                    answer_framework="项目背景 + 领导挑战 + 管理策略 + 成果展示",
                    key_points=["领导风格", "团队激励", "目标管理", "成果达成"],
                    evaluation_criteria=["领导能力", "团队管理", "目标达成", "影响力"]
                )
            ]
        }
        
        return question_bank
    
    def _load_answer_frameworks(self) -> Dict[str, Dict[str, Any]]:
        """加载答案框架"""
        return {
            'STAR': {
                'name': 'STAR方法',
                'description': '情况(Situation) + 任务(Task) + 行动(Action) + 结果(Result)',
                'structure': {
                    'situation': '描述具体情况和背景',
                    'task': '说明你的任务和目标',
                    'action': '详述你采取的具体行动',
                    'result': '展示最终结果和影响'
                },
                'best_for': ['behavioral', 'experience', 'achievements']
            },
            'CAR': {
                'name': 'CAR方法',
                'description': '挑战(Challenge) + 行动(Action) + 结果(Result)',
                'structure': {
                    'challenge': '描述面临的挑战或问题',
                    'action': '说明采取的解决行动',
                    'result': '展示取得的成果'
                },
                'best_for': ['problem_solving', 'technical_challenges']
            },
            'SOAR': {
                'name': 'SOAR方法',
                'description': '情况(Situation) + 目标(Objective) + 行动(Action) + 结果(Result)',
                'structure': {
                    'situation': '描述当时的情况',
                    'objective': '明确目标和期望',
                    'action': '详述具体行动',
                    'result': '展示达成的结果'
                },
                'best_for': ['leadership', 'project_management']
            }
        }
    
    def _load_company_specific_questions(self) -> Dict[str, List[str]]:
        """加载公司特定问题"""
        return {
            'startup': [
                "你如何适应快节奏的工作环境？",
                "在资源有限的情况下，你如何优先处理任务？",
                "你对创业公司的风险有什么看法？"
            ],
            'big_tech': [
                "你如何处理大规模系统的复杂性？",
                "描述你在大团队中的协作经验。",
                "你如何保持技术创新？"
            ],
            'consulting': [
                "如何向非技术客户解释复杂的技术概念？",
                "描述一次你需要快速学习新领域知识的经历。",
                "你如何处理客户的不合理要求？"
            ]
        }
    
    async def prepare_for_interview(self, target_job: JobAnalysisResult,
                                   target_company: Optional[CompanyAnalysisResult] = None,
                                   interview_date: Optional[str] = None,
                                   preparation_time_days: int = 7,
                                   user_background: Optional[Dict[str, Any]] = None) -> InterviewPreparationResult:
        """为面试做准备
        
        Args:
            target_job: 目标职位信息
            target_company: 目标公司信息
            interview_date: 面试日期
            preparation_time_days: 准备时间（天）
            user_background: 用户背景信息
        
        Returns:
            InterviewPreparationResult: 面试准备结果
        """
        start_time = time.time()
        
        logger.info(f"开始为 {target_job.job_title} 职位准备面试")
        
        # 创建准备结果对象
        result = InterviewPreparationResult(
            preparation_id=str(int(time.time())),
            target_job_title=target_job.job_title,
            target_company=target_company.company_name if target_company else "",
            interview_date=interview_date
        )
        
        try:
            # 1. 预测面试问题
            result.predicted_questions = await self._predict_interview_questions(
                target_job, target_company, user_background
            )
            
            # 2. 生成个性化答案建议
            result.personalized_answers = await self._generate_personalized_answers(
                result.predicted_questions, target_job, user_background
            )
            
            # 3. 评估技能准备度
            result.skill_readiness = await self._assess_skill_readiness(
                target_job, user_background
            )
            
            # 4. 识别知识缺口
            result.knowledge_gaps = await self._identify_knowledge_gaps(
                target_job, user_background
            )
            
            # 5. 公司文化匹配分析
            if target_company:
                result.culture_fit_score = await self._analyze_culture_fit(
                    target_company, user_background
                )
                result.company_specific_prep = await self._generate_company_specific_prep(
                    target_company
                )
            
            # 6. 创建准备计划
            result.preparation_plan = await self._create_preparation_plan(
                target_job, target_company, preparation_time_days, result
            )
            
            # 7. 计算整体准备度
            result.overall_readiness = self._calculate_overall_readiness(result)
            result.confidence_score = self._calculate_confidence_score(result)
            
            result.preparation_duration = time.time() - start_time
            result.data_sources.append('interview_preparation')
            
            logger.info(f"面试准备完成 (耗时: {result.preparation_duration:.2f}s)")
            
            return result
            
        except Exception as e:
            logger.error(f"面试准备失败: {e}")
            result.preparation_duration = time.time() - start_time
            return result
    
    async def _predict_interview_questions(self, target_job: JobAnalysisResult,
                                         target_company: Optional[CompanyAnalysisResult],
                                         user_background: Optional[Dict[str, Any]]) -> List[InterviewQuestion]:
        """预测面试问题"""
        predicted_questions = []
        
        # 1. 基础通用问题
        predicted_questions.extend(self.question_bank['general'])
        
        # 2. 技术相关问题
        if any(skill.name.lower() in ['python', 'java', 'javascript', 'react', 'node.js'] 
               for skill in target_job.required_skills):
            predicted_questions.extend(self.question_bank['technical'])
        
        # 3. 行为面试问题
        predicted_questions.extend(self.question_bank['behavioral'])
        
        # 4. 职位特定问题
        role_specific_questions = await self._generate_role_specific_questions(target_job)
        predicted_questions.extend(role_specific_questions)
        
        # 5. 公司特定问题
        if target_company:
            company_questions = await self._generate_company_questions(target_company)
            predicted_questions.extend(company_questions)
        
        # 6. 基于用户背景的个性化问题
        if user_background:
            personalized_questions = await self._generate_personalized_questions(
                user_background, target_job
            )
            predicted_questions.extend(personalized_questions)
        
        # 计算相关性评分并排序
        for question in predicted_questions:
            question.relevance_score = self._calculate_question_relevance(
                question, target_job, target_company
            )
        
        # 按相关性排序并限制数量
        predicted_questions.sort(key=lambda q: q.relevance_score, reverse=True)
        
        return predicted_questions[:30]  # 返回最相关的30个问题
    
    async def _generate_role_specific_questions(self, target_job: JobAnalysisResult) -> List[InterviewQuestion]:
        """生成职位特定问题"""
        questions = []
        job_title_lower = target_job.job_title.lower()
        
        # 根据职位类型生成问题
        if 'senior' in job_title_lower or 'lead' in job_title_lower:
            questions.append(InterviewQuestion(
                question="作为高级开发者，你如何指导初级团队成员？",
                category=QuestionCategory.LEADERSHIP,
                interview_type=InterviewType.BEHAVIORAL,
                difficulty=DifficultyLevel.HARD,
                role_specific=True
            ))
        
        if 'full stack' in job_title_lower or 'fullstack' in job_title_lower:
            questions.append(InterviewQuestion(
                question="在全栈开发中，你如何平衡前端和后端的开发时间？",
                category=QuestionCategory.TECHNICAL_SKILLS,
                interview_type=InterviewType.TECHNICAL,
                difficulty=DifficultyLevel.MEDIUM,
                role_specific=True
            ))
        
        if 'data' in job_title_lower:
            questions.append(InterviewQuestion(
                question="描述你处理过的最大数据集，以及你是如何优化性能的？",
                category=QuestionCategory.TECHNICAL_SKILLS,
                interview_type=InterviewType.TECHNICAL,
                difficulty=DifficultyLevel.HARD,
                role_specific=True
            ))
        
        return questions
    
    async def _generate_company_questions(self, target_company: CompanyAnalysisResult) -> List[InterviewQuestion]:
        """生成公司特定问题"""
        questions = []
        
        # 基于公司规模
        if target_company.company_size == 'startup':
            questions.extend([
                InterviewQuestion(
                    question="你为什么选择加入一家初创公司？",
                    category=QuestionCategory.MOTIVATION,
                    interview_type=InterviewType.HR_SCREENING,
                    difficulty=DifficultyLevel.MEDIUM,
                    company_specific=True
                )
            ])
        
        # 基于公司文化
        if 'innovation' in target_company.culture_values:
            questions.append(InterviewQuestion(
                question="描述一个你提出创新解决方案的例子。",
                category=QuestionCategory.PROBLEM_SOLVING,
                interview_type=InterviewType.BEHAVIORAL,
                difficulty=DifficultyLevel.MEDIUM,
                company_specific=True
            ))
        
        return questions
    
    async def _generate_personalized_questions(self, user_background: Dict[str, Any],
                                             target_job: JobAnalysisResult) -> List[InterviewQuestion]:
        """生成个性化问题"""
        questions = []
        
        # 基于工作经验
        years_experience = user_background.get('years_experience', 0)
        if years_experience < 2:
            questions.append(InterviewQuestion(
                question="作为相对新的开发者，你如何快速学习新技术？",
                category=QuestionCategory.EXPERIENCE,
                interview_type=InterviewType.BEHAVIORAL,
                difficulty=DifficultyLevel.MEDIUM
            ))
        elif years_experience > 5:
            questions.append(InterviewQuestion(
                question="在你的职业生涯中，技术栈是如何演进的？",
                category=QuestionCategory.EXPERIENCE,
                interview_type=InterviewType.TECHNICAL,
                difficulty=DifficultyLevel.MEDIUM
            ))
        
        # 基于教育背景
        if user_background.get('education_level') == 'self_taught':
            questions.append(InterviewQuestion(
                question="作为自学成才的开发者，你如何保持技术更新？",
                category=QuestionCategory.EXPERIENCE,
                interview_type=InterviewType.BEHAVIORAL,
                difficulty=DifficultyLevel.MEDIUM
            ))
        
        return questions
    
    def _calculate_question_relevance(self, question: InterviewQuestion,
                                    target_job: JobAnalysisResult,
                                    target_company: Optional[CompanyAnalysisResult]) -> float:
        """计算问题相关性"""
        relevance = 0.5  # 基础相关性
        
        # 职位特定加分
        if question.role_specific:
            relevance += 0.3
        
        # 公司特定加分
        if question.company_specific and target_company:
            relevance += 0.2
        
        # 技能匹配加分
        job_skills = [skill.name.lower() for skill in target_job.required_skills]
        question_tags = [tag.lower() for tag in question.tags]
        
        skill_overlap = len(set(job_skills) & set(question_tags))
        if skill_overlap > 0:
            relevance += skill_overlap * 0.1
        
        # 难度适配
        if question.difficulty in [DifficultyLevel.MEDIUM, DifficultyLevel.HARD]:
            relevance += 0.1
        
        return min(relevance, 1.0)
    
    async def _generate_personalized_answers(self, questions: List[InterviewQuestion],
                                           target_job: JobAnalysisResult,
                                           user_background: Optional[Dict[str, Any]]) -> List[AnswerSuggestion]:
        """生成个性化答案建议"""
        answer_suggestions = []
        
        for i, question in enumerate(questions[:15]):  # 为前15个问题生成答案
            suggestion = await self._create_answer_suggestion(
                question, target_job, user_background, str(i)
            )
            answer_suggestions.append(suggestion)
        
        return answer_suggestions
    
    async def _create_answer_suggestion(self, question: InterviewQuestion,
                                      target_job: JobAnalysisResult,
                                      user_background: Optional[Dict[str, Any]],
                                      question_id: str) -> AnswerSuggestion:
        """创建单个答案建议"""
        # 选择合适的答案框架
        framework = self._select_answer_framework(question)
        
        # 生成基础答案结构
        answer_structure = self._generate_answer_structure(question, framework, target_job)
        
        # 个性化答案内容
        personalized_content = self._personalize_answer_content(
            answer_structure, user_background, target_job
        )
        
        suggestion = AnswerSuggestion(
            question_id=question_id,
            suggested_answer=personalized_content['full_answer'],
            framework_used=framework['name'],
            situation=personalized_content.get('situation', ''),
            task=personalized_content.get('task', ''),
            action=personalized_content.get('action', ''),
            result=personalized_content.get('result', '')
        )
        
        # 评估答案质量
        suggestion.completeness_score = self._evaluate_answer_completeness(suggestion)
        suggestion.relevance_score = self._evaluate_answer_relevance(suggestion, question, target_job)
        suggestion.impact_score = self._evaluate_answer_impact(suggestion)
        
        # 生成改进建议
        suggestion.improvement_suggestions = self._generate_answer_improvements(suggestion, question)
        
        return suggestion
    
    def _select_answer_framework(self, question: InterviewQuestion) -> Dict[str, Any]:
        """选择合适的答案框架"""
        if question.category in [QuestionCategory.EXPERIENCE, QuestionCategory.ACHIEVEMENTS]:
            return self.answer_frameworks['STAR']
        elif question.category == QuestionCategory.PROBLEM_SOLVING:
            return self.answer_frameworks['CAR']
        elif question.category == QuestionCategory.LEADERSHIP:
            return self.answer_frameworks['SOAR']
        else:
            return self.answer_frameworks['STAR']  # 默认使用STAR
    
    def _generate_answer_structure(self, question: InterviewQuestion,
                                 framework: Dict[str, Any],
                                 target_job: JobAnalysisResult) -> Dict[str, str]:
        """生成答案结构"""
        structure = {}
        
        if framework['name'] == 'STAR方法':
            structure = {
                'situation': f"在我之前的{target_job.job_title}工作中...",
                'task': "我需要...",
                'action': "我采取了以下行动...",
                'result': "最终结果是..."
            }
        elif framework['name'] == 'CAR方法':
            structure = {
                'challenge': "我面临的挑战是...",
                'action': "我的解决方案是...",
                'result': "取得的成果是..."
            }
        
        return structure
    
    def _personalize_answer_content(self, answer_structure: Dict[str, str],
                                  user_background: Optional[Dict[str, Any]],
                                  target_job: JobAnalysisResult) -> Dict[str, str]:
        """个性化答案内容"""
        personalized = answer_structure.copy()
        
        if user_background:
            # 基于用户背景调整内容
            experience_level = user_background.get('years_experience', 0)
            if experience_level < 2:
                personalized['situation'] = "在我的学习项目/实习经历中..."
            elif experience_level > 5:
                personalized['situation'] = "在我担任高级开发者期间..."
        
        # 生成完整答案
        full_answer = "\n".join([
            f"**{key.title()}**: {value}"
            for key, value in personalized.items()
            if value and not value.endswith('...')
        ])
        
        personalized['full_answer'] = full_answer or "请根据您的具体经历来回答这个问题。"
        
        return personalized
    
    def _evaluate_answer_completeness(self, suggestion: AnswerSuggestion) -> float:
        """评估答案完整性"""
        score = 0.0
        
        # 检查STAR结构完整性
        if suggestion.situation:
            score += 0.25
        if suggestion.task:
            score += 0.25
        if suggestion.action:
            score += 0.25
        if suggestion.result:
            score += 0.25
        
        return score
    
    def _evaluate_answer_relevance(self, suggestion: AnswerSuggestion,
                                 question: InterviewQuestion,
                                 target_job: JobAnalysisResult) -> float:
        """评估答案相关性"""
        # 简化的相关性评估
        base_score = 0.7
        
        # 检查是否包含职位相关技能
        job_skills = [skill.name.lower() for skill in target_job.required_skills]
        answer_lower = suggestion.suggested_answer.lower()
        
        skill_mentions = sum(1 for skill in job_skills if skill in answer_lower)
        relevance_bonus = min(skill_mentions * 0.1, 0.3)
        
        return min(base_score + relevance_bonus, 1.0)
    
    def _evaluate_answer_impact(self, suggestion: AnswerSuggestion) -> float:
        """评估答案影响力"""
        # 检查是否包含量化结果
        answer = suggestion.suggested_answer
        
        # 查找数字、百分比等量化指标
        quantified_patterns = r'\d+[%$]?|\d+\s*(million|thousand|k\b)|提高了|增加了|减少了'
        quantified_matches = len(re.findall(quantified_patterns, answer, re.IGNORECASE))
        
        base_score = 0.6
        impact_bonus = min(quantified_matches * 0.2, 0.4)
        
        return min(base_score + impact_bonus, 1.0)
    
    def _generate_answer_improvements(self, suggestion: AnswerSuggestion,
                                    question: InterviewQuestion) -> List[str]:
        """生成答案改进建议"""
        improvements = []
        
        if suggestion.completeness_score < 0.8:
            improvements.append("建议使用完整的STAR结构来组织答案")
        
        if suggestion.relevance_score < 0.7:
            improvements.append("增加更多与目标职位相关的技能和经验描述")
        
        if suggestion.impact_score < 0.7:
            improvements.append("添加具体的数字和量化结果来增强说服力")
        
        if not improvements:
            improvements.append("答案结构良好，建议根据个人经历进一步完善细节")
        
        return improvements
    
    async def _assess_skill_readiness(self, target_job: JobAnalysisResult,
                                    user_background: Optional[Dict[str, Any]]) -> Dict[str, float]:
        """评估技能准备度"""
        skill_readiness = {}
        
        if not user_background:
            # 如果没有用户背景，返回默认评估
            for skill in target_job.required_skills:
                skill_readiness[skill.name] = 0.5  # 中等准备度
            return skill_readiness
        
        user_skills = user_background.get('skills', [])
        user_experience = user_background.get('years_experience', 0)
        
        for skill in target_job.required_skills:
            readiness = 0.3  # 基础分数
            
            # 检查用户是否有该技能
            if skill.name.lower() in [s.lower() for s in user_skills]:
                readiness += 0.4
                
                # 基于经验年限调整
                if user_experience >= 3:
                    readiness += 0.2
                elif user_experience >= 1:
                    readiness += 0.1
            
            # 基于技能重要性调整
            if skill.importance == 'high':
                readiness *= 0.9  # 高重要性技能要求更高
            
            skill_readiness[skill.name] = min(readiness, 1.0)
        
        return skill_readiness
    
    async def _identify_knowledge_gaps(self, target_job: JobAnalysisResult,
                                     user_background: Optional[Dict[str, Any]]) -> List[str]:
        """识别知识缺口"""
        gaps = []
        
        if not user_background:
            # 如果没有用户背景，返回所有必需技能作为潜在缺口
            return [skill.name for skill in target_job.required_skills if skill.importance == 'high']
        
        user_skills = set(s.lower() for s in user_background.get('skills', []))
        
        for skill in target_job.required_skills:
            if skill.name.lower() not in user_skills:
                gaps.append(skill.name)
        
        return gaps
    
    async def _analyze_culture_fit(self, target_company: CompanyAnalysisResult,
                                 user_background: Optional[Dict[str, Any]]) -> float:
        """分析文化匹配度"""
        if not user_background:
            return 0.5  # 默认中等匹配度
        
        # 简化的文化匹配分析
        base_score = 0.6
        
        user_values = user_background.get('values', [])
        company_values = target_company.culture_values
        
        # 计算价值观重叠
        if user_values and company_values:
            overlap = len(set(user_values) & set(company_values))
            max_possible = max(len(user_values), len(company_values))
            if max_possible > 0:
                overlap_score = overlap / max_possible
                base_score += overlap_score * 0.4
        
        return min(base_score, 1.0)
    
    async def _generate_company_specific_prep(self, target_company: CompanyAnalysisResult) -> List[str]:
        """生成公司特定准备建议"""
        prep_items = []
        
        # 基于公司规模的建议
        if target_company.company_size == 'startup':
            prep_items.extend([
                "了解创业公司的快节奏工作环境",
                "准备讨论如何在资源有限的情况下工作",
                "研究公司的产品和市场定位"
            ])
        elif target_company.company_size == 'large':
            prep_items.extend([
                "了解大公司的层级结构和流程",
                "准备讨论如何在大团队中协作",
                "研究公司的企业文化和价值观"
            ])
        
        # 基于行业的建议
        if target_company.industry:
            prep_items.append(f"深入了解{target_company.industry}行业的最新趋势")
        
        # 基于文化价值观的建议
        for value in target_company.culture_values:
            if value.lower() == 'innovation':
                prep_items.append("准备分享你的创新思维和解决方案")
            elif value.lower() == 'teamwork':
                prep_items.append("准备团队合作的具体例子")
        
        return prep_items
    
    async def _create_preparation_plan(self, target_job: JobAnalysisResult,
                                     target_company: Optional[CompanyAnalysisResult],
                                     preparation_days: int,
                                     result: InterviewPreparationResult) -> InterviewPreparationPlan:
        """创建准备计划"""
        plan = InterviewPreparationPlan(
            plan_id=f"plan_{int(time.time())}",
            target_job=target_job,
            target_company=target_company,
            preparation_days=preparation_days
        )
        
        # 技术主题
        plan.technical_topics = [skill.name for skill in target_job.required_skills]
        
        # 行为场景
        plan.behavioral_scenarios = [
            "团队合作经历",
            "解决冲突的例子",
            "领导项目的经验",
            "面对挑战的应对",
            "职业发展目标"
        ]
        
        # 公司研究要点
        if target_company:
            plan.company_research_points = [
                f"了解{target_company.company_name}的历史和发展",
                "研究公司的产品和服务",
                "了解公司文化和价值观",
                "查看最近的新闻和动态"
            ]
        
        # 练习问题（选择最相关的）
        plan.practice_questions = result.predicted_questions[:20]
        
        # 模拟面试安排
        plan.mock_interview_schedule = self._create_mock_interview_schedule(preparation_days)
        
        # 推荐资源
        plan.recommended_resources = [
            {"type": "website", "name": "LeetCode", "url": "https://leetcode.com", "description": "编程练习"},
            {"type": "website", "name": "Glassdoor", "url": "https://glassdoor.com", "description": "面试经验分享"},
            {"type": "book", "name": "Cracking the Coding Interview", "description": "技术面试指南"}
        ]
        
        return plan
    
    def _create_mock_interview_schedule(self, preparation_days: int) -> List[Dict[str, Any]]:
        """创建模拟面试安排"""
        schedule = []
        
        if preparation_days >= 7:
            schedule.extend([
                {"day": 2, "type": "behavioral", "duration": 30, "focus": "基础行为问题"},
                {"day": 4, "type": "technical", "duration": 45, "focus": "技术技能评估"},
                {"day": 6, "type": "comprehensive", "duration": 60, "focus": "综合模拟面试"}
            ])
        elif preparation_days >= 3:
            schedule.extend([
                {"day": 1, "type": "behavioral", "duration": 30, "focus": "行为问题练习"},
                {"day": 2, "type": "technical", "duration": 45, "focus": "技术问题练习"}
            ])
        
        return schedule
    
    def _calculate_overall_readiness(self, result: InterviewPreparationResult) -> float:
        """计算整体准备度"""
        readiness_factors = []
        
        # 技能准备度
        if result.skill_readiness:
            avg_skill_readiness = sum(result.skill_readiness.values()) / len(result.skill_readiness)
            readiness_factors.append(avg_skill_readiness)
        
        # 知识缺口影响
        if result.knowledge_gaps:
            gap_penalty = len(result.knowledge_gaps) * 0.1
            readiness_factors.append(max(0.8 - gap_penalty, 0.3))
        
        # 答案准备度
        if result.personalized_answers:
            avg_answer_quality = sum(
                (ans.completeness_score + ans.relevance_score + ans.impact_score) / 3
                for ans in result.personalized_answers
            ) / len(result.personalized_answers)
            readiness_factors.append(avg_answer_quality)
        
        # 公司匹配度
        if result.culture_fit_score > 0:
            readiness_factors.append(result.culture_fit_score)
        
        return sum(readiness_factors) / len(readiness_factors) if readiness_factors else 0.5
    
    def _calculate_confidence_score(self, result: InterviewPreparationResult) -> float:
        """计算置信度评分"""
        confidence_factors = []
        
        # 问题预测数量
        if result.predicted_questions:
            question_score = min(len(result.predicted_questions) / 20, 1.0)
            confidence_factors.append(question_score)
        
        # 答案准备质量
        if result.personalized_answers:
            answer_quality = sum(
                ans.relevance_score for ans in result.personalized_answers
            ) / len(result.personalized_answers)
            confidence_factors.append(answer_quality)
        
        # 准备计划完整性
        if result.preparation_plan:
            plan_completeness = 1.0 if len(result.preparation_plan.technical_topics) > 0 else 0.5
            confidence_factors.append(plan_completeness)
        
        return sum(confidence_factors) / len(confidence_factors) if confidence_factors else 0.5


# 便捷函数
async def prepare_interview(target_job: JobAnalysisResult,
                           target_company: Optional[CompanyAnalysisResult] = None,
                           interview_date: Optional[str] = None,
                           preparation_days: int = 7,
                           user_background: Optional[Dict[str, Any]] = None,
                           config: Optional[Dict[str, Any]] = None) -> InterviewPreparationResult:
    """准备面试的便捷函数
    
    Args:
        target_job: 目标职位信息
        target_company: 目标公司信息
        interview_date: 面试日期
        preparation_days: 准备天数
        user_background: 用户背景
        config: 配置信息
    
    Returns:
        InterviewPreparationResult: 面试准备结果
    """
    engine = InterviewPreparationEngine(config)
    return await engine.prepare_for_interview(
        target_job, target_company, interview_date, preparation_days, user_background
    )


if __name__ == "__main__":
    # 测试代码
    async def test_interview_preparation():
        """测试面试准备功能"""
        # 示例目标职位
        from src.mcp.analyzers.job_analyzer import JobAnalysisResult, SkillRequirement
        from src.mcp.analyzers.company_analyzer import CompanyAnalysisResult
        
        target_job = JobAnalysisResult(
            job_title="Senior Full Stack Developer",
            company_name="TechCorp",
            required_skills=[
                SkillRequirement(name="Python", importance="high"),
                SkillRequirement(name="React", importance="high"),
                SkillRequirement(name="Django", importance="medium"),
                SkillRequirement(name="PostgreSQL", importance="medium")
            ],
            preferred_skills=[
                SkillRequirement(name="AWS", importance="low"),
                SkillRequirement(name="Docker", importance="low")
            ],
            responsibilities=[
                "开发和维护Web应用程序",
                "与跨职能团队协作",
                "指导初级开发者"
            ]
        )
        
        target_company = CompanyAnalysisResult(
            company_name="TechCorp",
            culture_values=["innovation", "teamwork", "growth"],
            company_size="medium",
            industry="technology"
        )
        
        user_background = {
            "years_experience": 4,
            "skills": ["Python", "JavaScript", "React", "Django", "MySQL"],
            "education_level": "bachelor",
            "values": ["innovation", "learning"]
        }
        
        # 创建面试准备引擎
        engine = InterviewPreparationEngine()
        
        print("开始面试准备分析...")
        result = await engine.prepare_for_interview(
            target_job=target_job,
            target_company=target_company,
            interview_date="2024-02-15",
            preparation_time_days=7,
            user_background=user_background
        )
        
        print(f"\n=== 面试准备结果 ===")
        print(f"目标职位: {result.target_job_title}")
        print(f"目标公司: {result.target_company}")
        print(f"整体准备度: {result.overall_readiness:.2f}")
        print(f"置信度: {result.confidence_score:.2f}")
        print(f"文化匹配度: {result.culture_fit_score:.2f}")
        
        if result.predicted_questions:
            print(f"\n=== 预测面试问题 ({len(result.predicted_questions)}个) ===")
            for i, question in enumerate(result.predicted_questions[:5], 1):
                print(f"{i}. [{question.difficulty.value.upper()}] {question.question}")
                print(f"   类别: {question.category.value} | 类型: {question.interview_type.value}")
                print(f"   相关性: {question.relevance_score:.2f}")
                print()
        
        if result.skill_readiness:
            print(f"\n=== 技能准备度 ===")
            for skill, readiness in result.skill_readiness.items():
                status = "✓" if readiness >= 0.7 else "⚠" if readiness >= 0.5 else "✗"
                print(f"{status} {skill}: {readiness:.2f}")
        
        if result.knowledge_gaps:
            print(f"\n=== 知识缺口 ===")
            for gap in result.knowledge_gaps:
                print(f"- {gap}")
        
        if result.personalized_answers:
            print(f"\n=== 个性化答案建议 (前3个) ===")
            for i, answer in enumerate(result.personalized_answers[:3], 1):
                print(f"{i}. 问题ID: {answer.question_id}")
                print(f"   框架: {answer.framework_used}")
                print(f"   完整性: {answer.completeness_score:.2f} | 相关性: {answer.relevance_score:.2f}")
                if answer.improvement_suggestions:
                    print(f"   改进建议: {answer.improvement_suggestions[0]}")
                print()
        
        if result.preparation_plan:
            plan = result.preparation_plan
            print(f"\n=== 准备计划 ({plan.preparation_days}天) ===")
            print(f"技术主题: {', '.join(plan.technical_topics[:5])}")
            print(f"行为场景: {', '.join(plan.behavioral_scenarios[:3])}")
            
            if plan.mock_interview_schedule:
                print("\n模拟面试安排:")
                for session in plan.mock_interview_schedule:
                    print(f"  第{session['day']}天: {session['type']} ({session['duration']}分钟) - {session['focus']}")
        
        print("\n面试准备分析完成!")
    
    # 运行测试
    import asyncio
    asyncio.run(test_interview_preparation())