#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
职位分析增强模块 - 基于MCP的智能职位分析

此模块提供增强的职位分析功能，包括：
- 职位描述深度解析
- 技术栈要求分析
- 公司背景研究
- 薪资范围分析
- 技能匹配评估
- 兼容性评分

作者: AI Assistant
创建时间: 2024
"""

import asyncio
import re
import json
import time
from typing import Dict, List, Optional, Any, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
import logging
from urllib.parse import urlparse, urljoin

# 导入MCP管理器
try:
    from src.mcp.core.mcp_manager import get_mcp_manager, MCPToolResult
except ImportError:
    # 如果导入失败，创建模拟实现
    class MCPToolResult:
        def __init__(self, success=True, result=None, error=None):
            self.success = success
            self.result = result
            self.error = error
    
    def get_mcp_manager():
        return None

# 导入现有模块（保持兼容性）
try:
    from src.utils.logger import logger
except ImportError:
    import logging
    logger = logging.getLogger(__name__)

try:
    from src.utils.config_loader import ConfigLoader
except ImportError:
    class ConfigLoader:
        @staticmethod
        def load_config(config_path: str) -> Dict[str, Any]:
            return {}


class JobAnalysisLevel(Enum):
    """职位分析深度级别"""
    BASIC = "basic"          # 基础分析
    STANDARD = "standard"    # 标准分析
    COMPREHENSIVE = "comprehensive"  # 全面分析
    DEEP = "deep"           # 深度分析


class SkillCategory(Enum):
    """技能分类"""
    PROGRAMMING_LANGUAGE = "programming_language"
    FRAMEWORK = "framework"
    DATABASE = "database"
    CLOUD_PLATFORM = "cloud_platform"
    TOOL = "tool"
    METHODOLOGY = "methodology"
    SOFT_SKILL = "soft_skill"
    CERTIFICATION = "certification"
    OTHER = "other"


@dataclass
class SkillRequirement:
    """技能要求数据类"""
    name: str
    category: SkillCategory
    importance: str  # required, preferred, nice_to_have
    experience_years: Optional[int] = None
    proficiency_level: Optional[str] = None  # beginner, intermediate, advanced, expert
    context: str = ""  # 技能出现的上下文
    confidence: float = 0.0  # 识别置信度


@dataclass
class CompanyInfo:
    """公司信息数据类"""
    name: str
    industry: Optional[str] = None
    size: Optional[str] = None
    location: Optional[str] = None
    website: Optional[str] = None
    description: Optional[str] = None
    culture: Optional[str] = None
    benefits: List[str] = field(default_factory=list)
    tech_stack: List[str] = field(default_factory=list)
    funding_stage: Optional[str] = None
    employee_count: Optional[int] = None
    glassdoor_rating: Optional[float] = None
    linkedin_url: Optional[str] = None


@dataclass
class SalaryInfo:
    """薪资信息数据类"""
    min_salary: Optional[int] = None
    max_salary: Optional[int] = None
    currency: str = "USD"
    frequency: str = "annual"  # annual, monthly, hourly
    equity: Optional[str] = None
    benefits_value: Optional[int] = None
    total_compensation: Optional[int] = None
    market_percentile: Optional[int] = None
    source: Optional[str] = None


@dataclass
class JobAnalysisResult:
    """职位分析结果数据类"""
    job_id: str
    job_title: str
    company_name: str
    analysis_level: JobAnalysisLevel
    timestamp: float = field(default_factory=time.time)
    
    # 基础信息
    job_description: str = ""
    location: Optional[str] = None
    employment_type: Optional[str] = None  # full_time, part_time, contract, etc.
    remote_option: Optional[str] = None  # remote, hybrid, on_site
    
    # 技能分析
    required_skills: List[SkillRequirement] = field(default_factory=list)
    preferred_skills: List[SkillRequirement] = field(default_factory=list)
    nice_to_have_skills: List[SkillRequirement] = field(default_factory=list)
    
    # 经验要求
    min_experience_years: Optional[int] = None
    max_experience_years: Optional[int] = None
    education_requirements: List[str] = field(default_factory=list)
    
    # 公司信息
    company_info: Optional[CompanyInfo] = None
    
    # 薪资信息
    salary_info: Optional[SalaryInfo] = None
    
    # 分析评分
    compatibility_score: float = 0.0
    skill_match_score: float = 0.0
    experience_match_score: float = 0.0
    location_match_score: float = 0.0
    
    # 建议和洞察
    recommendations: List[str] = field(default_factory=list)
    missing_skills: List[str] = field(default_factory=list)
    strengths: List[str] = field(default_factory=list)
    
    # 元数据
    analysis_duration: float = 0.0
    data_sources: List[str] = field(default_factory=list)
    confidence_score: float = 0.0


class JobAnalyzer:
    """职位分析器主类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """初始化职位分析器
        
        Args:
            config: 分析器配置
        """
        self.config = config or {}
        self.mcp_manager = get_mcp_manager()
        
        # 技能关键词库
        self.skill_keywords = self._load_skill_keywords()
        
        # 公司信息缓存
        self.company_cache: Dict[str, CompanyInfo] = {}
        
        logger.info("职位分析器初始化完成")
    
    def _load_skill_keywords(self) -> Dict[SkillCategory, List[str]]:
        """加载技能关键词库"""
        return {
            SkillCategory.PROGRAMMING_LANGUAGE: [
                'python', 'java', 'javascript', 'typescript', 'c++', 'c#', 'go', 'rust',
                'php', 'ruby', 'swift', 'kotlin', 'scala', 'r', 'matlab', 'sql'
            ],
            SkillCategory.FRAMEWORK: [
                'react', 'angular', 'vue', 'django', 'flask', 'spring', 'express',
                'laravel', 'rails', 'asp.net', 'tensorflow', 'pytorch', 'keras'
            ],
            SkillCategory.DATABASE: [
                'mysql', 'postgresql', 'mongodb', 'redis', 'elasticsearch',
                'oracle', 'sql server', 'cassandra', 'dynamodb', 'sqlite'
            ],
            SkillCategory.CLOUD_PLATFORM: [
                'aws', 'azure', 'gcp', 'google cloud', 'alibaba cloud',
                'docker', 'kubernetes', 'terraform', 'ansible'
            ],
            SkillCategory.TOOL: [
                'git', 'jenkins', 'jira', 'confluence', 'slack', 'figma',
                'photoshop', 'illustrator', 'tableau', 'power bi'
            ],
            SkillCategory.METHODOLOGY: [
                'agile', 'scrum', 'kanban', 'devops', 'ci/cd', 'tdd', 'bdd',
                'microservices', 'rest api', 'graphql'
            ],
            SkillCategory.SOFT_SKILL: [
                'communication', 'leadership', 'teamwork', 'problem solving',
                'critical thinking', 'creativity', 'adaptability'
            ]
        }
    
    async def analyze_job(self, job_data: Dict[str, Any], 
                         analysis_level: JobAnalysisLevel = JobAnalysisLevel.STANDARD,
                         user_profile: Optional[Dict[str, Any]] = None) -> JobAnalysisResult:
        """分析职位
        
        Args:
            job_data: 职位数据
            analysis_level: 分析深度级别
            user_profile: 用户档案（用于匹配分析）
        
        Returns:
            JobAnalysisResult: 分析结果
        """
        start_time = time.time()
        
        logger.info(f"开始分析职位: {job_data.get('title', 'Unknown')} - {analysis_level.value}")
        
        # 创建分析结果对象
        result = JobAnalysisResult(
            job_id=job_data.get('id', str(int(time.time()))),
            job_title=job_data.get('title', ''),
            company_name=job_data.get('company', ''),
            analysis_level=analysis_level,
            job_description=job_data.get('description', ''),
            location=job_data.get('location', ''),
            employment_type=job_data.get('employment_type', ''),
            remote_option=job_data.get('remote_option', '')
        )
        
        try:
            # 基础分析
            await self._analyze_basic_info(result, job_data)
            
            # 技能分析
            await self._analyze_skills(result, job_data)
            
            # 经验要求分析
            await self._analyze_experience_requirements(result, job_data)
            
            if analysis_level in [JobAnalysisLevel.STANDARD, JobAnalysisLevel.COMPREHENSIVE, JobAnalysisLevel.DEEP]:
                # 公司信息分析
                await self._analyze_company_info(result, job_data)
                
                # 薪资分析
                await self._analyze_salary_info(result, job_data)
            
            if analysis_level in [JobAnalysisLevel.COMPREHENSIVE, JobAnalysisLevel.DEEP]:
                # 市场分析
                await self._analyze_market_context(result, job_data)
                
                # 竞争分析
                await self._analyze_competition(result, job_data)
            
            if analysis_level == JobAnalysisLevel.DEEP:
                # 深度公司研究
                await self._deep_company_research(result, job_data)
                
                # 行业趋势分析
                await self._analyze_industry_trends(result, job_data)
            
            # 如果提供了用户档案，进行匹配分析
            if user_profile:
                await self._analyze_user_match(result, user_profile)
            
            # 生成建议和洞察
            await self._generate_recommendations(result, user_profile)
            
            # 计算最终评分
            self._calculate_final_scores(result, user_profile)
            
            result.analysis_duration = time.time() - start_time
            result.confidence_score = self._calculate_confidence_score(result)
            
            logger.info(f"职位分析完成: {result.job_title} (耗时: {result.analysis_duration:.2f}s)")
            
            return result
            
        except Exception as e:
            logger.error(f"职位分析失败: {e}")
            result.analysis_duration = time.time() - start_time
            result.recommendations.append(f"分析过程中出现错误: {str(e)}")
            return result
    
    async def _analyze_basic_info(self, result: JobAnalysisResult, job_data: Dict[str, Any]) -> None:
        """分析基础信息"""
        description = job_data.get('description', '')
        
        # 提取就业类型
        if not result.employment_type:
            employment_patterns = {
                'full_time': r'full.?time|permanent|正职',
                'part_time': r'part.?time|兼职',
                'contract': r'contract|contractor|合同工',
                'internship': r'intern|实习',
                'freelance': r'freelance|自由职业'
            }
            
            for emp_type, pattern in employment_patterns.items():
                if re.search(pattern, description, re.IGNORECASE):
                    result.employment_type = emp_type
                    break
        
        # 提取远程工作选项
        if not result.remote_option:
            remote_patterns = {
                'remote': r'remote|远程|在家办公',
                'hybrid': r'hybrid|混合|部分远程',
                'on_site': r'on.?site|office|现场|办公室'
            }
            
            for remote_type, pattern in remote_patterns.items():
                if re.search(pattern, description, re.IGNORECASE):
                    result.remote_option = remote_type
                    break
        
        result.data_sources.append('job_description_parsing')
    
    async def _analyze_skills(self, result: JobAnalysisResult, job_data: Dict[str, Any]) -> None:
        """分析技能要求"""
        description = job_data.get('description', '').lower()
        
        # 分析技能要求的重要性
        required_patterns = [
            r'required?:?\s*([^.\n]+)',
            r'must have:?\s*([^.\n]+)',
            r'essential:?\s*([^.\n]+)',
            r'必须:?\s*([^.\n]+)',
            r'要求:?\s*([^.\n]+)'
        ]
        
        preferred_patterns = [
            r'preferred?:?\s*([^.\n]+)',
            r'nice to have:?\s*([^.\n]+)',
            r'bonus:?\s*([^.\n]+)',
            r'优先:?\s*([^.\n]+)',
            r'加分:?\s*([^.\n]+)'
        ]
        
        # 提取必需技能
        for pattern in required_patterns:
            matches = re.findall(pattern, description, re.IGNORECASE)
            for match in matches:
                skills = self._extract_skills_from_text(match)
                for skill in skills:
                    result.required_skills.append(skill)
        
        # 提取优选技能
        for pattern in preferred_patterns:
            matches = re.findall(pattern, description, re.IGNORECASE)
            for match in matches:
                skills = self._extract_skills_from_text(match)
                for skill in skills:
                    result.preferred_skills.append(skill)
        
        # 如果没有明确分类，从整个描述中提取技能
        if not result.required_skills and not result.preferred_skills:
            all_skills = self._extract_skills_from_text(description)
            # 将前70%作为必需技能，后30%作为优选技能
            split_point = int(len(all_skills) * 0.7)
            result.required_skills.extend(all_skills[:split_point])
            result.preferred_skills.extend(all_skills[split_point:])
        
        # 使用MCP获取技术栈信息
        if self.mcp_manager:
            try:
                tech_analysis = await self.mcp_manager.call_tool(
                    'context7',
                    'resolve-library-id',
                    {'libraryName': result.job_title}
                )
                
                if tech_analysis.success and tech_analysis.result:
                    # 处理技术栈分析结果
                    self._process_tech_stack_analysis(result, tech_analysis.result)
                    result.data_sources.append('context7_tech_analysis')
                    
            except Exception as e:
                logger.warning(f"技术栈分析失败: {e}")
        
        result.data_sources.append('skill_extraction')
    
    def _extract_skills_from_text(self, text: str) -> List[SkillRequirement]:
        """从文本中提取技能"""
        skills = []
        text_lower = text.lower()
        
        for category, keywords in self.skill_keywords.items():
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    # 计算置信度（基于上下文）
                    confidence = self._calculate_skill_confidence(keyword, text)
                    
                    # 提取经验年限
                    experience_years = self._extract_experience_years(keyword, text)
                    
                    skill = SkillRequirement(
                        name=keyword,
                        category=category,
                        importance='required',  # 默认为必需，后续可调整
                        experience_years=experience_years,
                        context=text[:100],  # 保存上下文
                        confidence=confidence
                    )
                    skills.append(skill)
        
        # 去重（基于技能名称）
        unique_skills = []
        seen_names = set()
        for skill in skills:
            if skill.name not in seen_names:
                unique_skills.append(skill)
                seen_names.add(skill.name)
        
        return unique_skills
    
    def _calculate_skill_confidence(self, skill: str, context: str) -> float:
        """计算技能识别置信度"""
        confidence = 0.5  # 基础置信度
        
        # 如果技能被明确提及，增加置信度
        if skill in context.split():
            confidence += 0.3
        
        # 如果有相关上下文，增加置信度
        context_keywords = ['experience', 'years', 'proficient', 'expert', 'knowledge']
        for keyword in context_keywords:
            if keyword in context.lower():
                confidence += 0.1
                break
        
        return min(confidence, 1.0)
    
    def _extract_experience_years(self, skill: str, context: str) -> Optional[int]:
        """提取技能经验年限要求"""
        # 查找年限模式
        patterns = [
            rf'{re.escape(skill)}[^.]*?(\d+)\s*(?:years?|年)',
            rf'(\d+)\s*(?:years?|年)[^.]*?{re.escape(skill)}',
            rf'{re.escape(skill)}[^.]*?(\d+)\+\s*(?:years?|年)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, context, re.IGNORECASE)
            if match:
                try:
                    return int(match.group(1))
                except ValueError:
                    continue
        
        return None
    
    def _process_tech_stack_analysis(self, result: JobAnalysisResult, analysis_data: Dict[str, Any]) -> None:
        """处理技术栈分析结果"""
        # 这里可以根据Context7返回的数据进行处理
        # 示例实现
        if 'matches' in analysis_data:
            for match in analysis_data['matches']:
                if 'name' in match:
                    skill = SkillRequirement(
                        name=match['name'],
                        category=SkillCategory.FRAMEWORK,  # 可以根据实际情况分类
                        importance='preferred',
                        confidence=0.8
                    )
                    result.preferred_skills.append(skill)
    
    async def _analyze_experience_requirements(self, result: JobAnalysisResult, job_data: Dict[str, Any]) -> None:
        """分析经验要求"""
        description = job_data.get('description', '')
        
        # 提取经验年限要求
        experience_patterns = [
            r'(\d+)\+?\s*(?:years?|年)\s*(?:of\s*)?(?:experience|经验)',
            r'(?:minimum|at least|最少)\s*(\d+)\s*(?:years?|年)',
            r'(\d+)\s*to\s*(\d+)\s*(?:years?|年)',
            r'(\d+)-(\d+)\s*(?:years?|年)'
        ]
        
        for pattern in experience_patterns:
            matches = re.findall(pattern, description, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    # 范围匹配
                    try:
                        result.min_experience_years = int(match[0])
                        result.max_experience_years = int(match[1])
                    except ValueError:
                        continue
                else:
                    # 单一值匹配
                    try:
                        result.min_experience_years = int(match)
                    except ValueError:
                        continue
                break
        
        # 提取教育要求
        education_patterns = [
            r'bachelor[^.]*?(?:degree|学位)',
            r'master[^.]*?(?:degree|学位)',
            r'phd|doctorate|博士',
            r'(?:computer science|cs|计算机科学)',
            r'(?:engineering|工程)',
            r'(?:mathematics|数学)',
            r'(?:statistics|统计)'
        ]
        
        for pattern in education_patterns:
            if re.search(pattern, description, re.IGNORECASE):
                result.education_requirements.append(pattern)
        
        result.data_sources.append('experience_analysis')
    
    async def _analyze_company_info(self, result: JobAnalysisResult, job_data: Dict[str, Any]) -> None:
        """分析公司信息"""
        company_name = result.company_name
        
        # 检查缓存
        if company_name in self.company_cache:
            result.company_info = self.company_cache[company_name]
            return
        
        company_info = CompanyInfo(name=company_name)
        
        # 使用MCP获取公司网页信息
        if self.mcp_manager:
            try:
                # 尝试获取公司官网
                search_query = f"{company_name} official website"
                
                # 这里可以使用搜索引擎API或直接构造URL
                company_url = job_data.get('company_url') or f"https://www.{company_name.lower().replace(' ', '')}.com"
                
                # 获取公司网页内容
                web_content = await self.mcp_manager.call_tool(
                    'mult-fetch',
                    'fetch_html',
                    {
                        'url': company_url,
                        'startCursor': 0,
                        'extractContent': True,
                        'includeMetadata': True
                    }
                )
                
                if web_content.success and web_content.result:
                    self._extract_company_info_from_web(company_info, web_content.result)
                    result.data_sources.append('company_website')
                
            except Exception as e:
                logger.warning(f"获取公司网页信息失败: {e}")
        
        # 缓存公司信息
        self.company_cache[company_name] = company_info
        result.company_info = company_info
    
    def _extract_company_info_from_web(self, company_info: CompanyInfo, web_data: Dict[str, Any]) -> None:
        """从网页数据中提取公司信息"""
        content = web_data.get('content', '')
        
        # 提取公司描述
        if not company_info.description:
            # 查找about页面或描述段落
            about_patterns = [
                r'about us[^.]*?([^.]{100,500})',
                r'company overview[^.]*?([^.]{100,500})',
                r'our mission[^.]*?([^.]{100,500})'
            ]
            
            for pattern in about_patterns:
                match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
                if match:
                    company_info.description = match.group(1).strip()
                    break
        
        # 提取行业信息
        industry_keywords = {
            'technology': ['software', 'tech', 'ai', 'machine learning', 'data'],
            'finance': ['financial', 'banking', 'fintech', 'investment'],
            'healthcare': ['health', 'medical', 'pharmaceutical', 'biotech'],
            'e-commerce': ['ecommerce', 'retail', 'marketplace', 'shopping'],
            'education': ['education', 'learning', 'university', 'school']
        }
        
        for industry, keywords in industry_keywords.items():
            for keyword in keywords:
                if keyword in content.lower():
                    company_info.industry = industry
                    break
            if company_info.industry:
                break
    
    async def _analyze_salary_info(self, result: JobAnalysisResult, job_data: Dict[str, Any]) -> None:
        """分析薪资信息"""
        description = job_data.get('description', '')
        salary_info = SalaryInfo()
        
        # 从职位描述中提取薪资信息
        salary_patterns = [
            r'\$([\d,]+)\s*-\s*\$([\d,]+)',  # $50,000 - $80,000
            r'([\d,]+)k\s*-\s*([\d,]+)k',    # 50k - 80k
            r'salary:?\s*\$?([\d,]+)',       # salary: $70000
            r'compensation:?\s*\$?([\d,]+)'  # compensation: $70000
        ]
        
        for pattern in salary_patterns:
            matches = re.findall(pattern, description, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple) and len(match) == 2:
                    try:
                        min_sal = int(match[0].replace(',', ''))
                        max_sal = int(match[1].replace(',', ''))
                        
                        # 如果是k格式，乘以1000
                        if 'k' in pattern:
                            min_sal *= 1000
                            max_sal *= 1000
                        
                        salary_info.min_salary = min_sal
                        salary_info.max_salary = max_sal
                        salary_info.source = 'job_description'
                        break
                    except ValueError:
                        continue
                else:
                    try:
                        salary = int(match.replace(',', ''))
                        salary_info.min_salary = salary
                        salary_info.source = 'job_description'
                        break
                    except ValueError:
                        continue
        
        # 使用外部API获取市场薪资数据（如果配置了）
        if self.config.get('salary_analysis', {}).get('enabled', True):
            market_salary = await self._get_market_salary_data(result.job_title, result.location)
            if market_salary:
                if not salary_info.min_salary:
                    salary_info.min_salary = market_salary.get('min_salary')
                if not salary_info.max_salary:
                    salary_info.max_salary = market_salary.get('max_salary')
                salary_info.market_percentile = market_salary.get('percentile')
                result.data_sources.append('market_salary_data')
        
        result.salary_info = salary_info
    
    async def _get_market_salary_data(self, job_title: str, location: Optional[str]) -> Optional[Dict[str, Any]]:
        """获取市场薪资数据"""
        # 这里可以集成Glassdoor、PayScale等API
        # 目前返回模拟数据
        return {
            'min_salary': 60000,
            'max_salary': 90000,
            'percentile': 50,
            'source': 'market_data'
        }
    
    async def _analyze_market_context(self, result: JobAnalysisResult, job_data: Dict[str, Any]) -> None:
        """分析市场背景"""
        # 分析职位在市场中的定位
        # 这里可以添加更多市场分析逻辑
        result.data_sources.append('market_analysis')
    
    async def _analyze_competition(self, result: JobAnalysisResult, job_data: Dict[str, Any]) -> None:
        """分析竞争情况"""
        # 分析类似职位的竞争情况
        # 这里可以添加竞争分析逻辑
        result.data_sources.append('competition_analysis')
    
    async def _deep_company_research(self, result: JobAnalysisResult, job_data: Dict[str, Any]) -> None:
        """深度公司研究"""
        # 进行更深入的公司研究
        # 包括财务状况、发展历史、文化等
        result.data_sources.append('deep_company_research')
    
    async def _analyze_industry_trends(self, result: JobAnalysisResult, job_data: Dict[str, Any]) -> None:
        """分析行业趋势"""
        # 分析相关行业的发展趋势
        result.data_sources.append('industry_trends')
    
    async def _analyze_user_match(self, result: JobAnalysisResult, user_profile: Dict[str, Any]) -> None:
        """分析用户匹配度"""
        user_skills = set(user_profile.get('skills', []))
        required_skills = set(skill.name for skill in result.required_skills)
        preferred_skills = set(skill.name for skill in result.preferred_skills)
        
        # 计算技能匹配度
        required_match = len(user_skills & required_skills) / max(len(required_skills), 1)
        preferred_match = len(user_skills & preferred_skills) / max(len(preferred_skills), 1)
        
        result.skill_match_score = (required_match * 0.7 + preferred_match * 0.3)
        
        # 计算经验匹配度
        user_experience = user_profile.get('experience_years', 0)
        if result.min_experience_years:
            if user_experience >= result.min_experience_years:
                result.experience_match_score = 1.0
            else:
                result.experience_match_score = user_experience / result.min_experience_years
        else:
            result.experience_match_score = 1.0
        
        # 计算地理位置匹配度
        user_location = user_profile.get('location', '')
        if result.location and user_location:
            if user_location.lower() in result.location.lower() or result.remote_option == 'remote':
                result.location_match_score = 1.0
            else:
                result.location_match_score = 0.5  # 部分匹配
        else:
            result.location_match_score = 1.0
        
        # 识别缺失技能
        missing_required = required_skills - user_skills
        missing_preferred = preferred_skills - user_skills
        
        result.missing_skills.extend(list(missing_required))
        result.missing_skills.extend(list(missing_preferred))
        
        # 识别优势
        matching_skills = user_skills & (required_skills | preferred_skills)
        result.strengths.extend([f"具备{skill}技能" for skill in matching_skills])
    
    async def _generate_recommendations(self, result: JobAnalysisResult, user_profile: Optional[Dict[str, Any]]) -> None:
        """生成建议和洞察"""
        recommendations = []
        
        # 基于技能匹配的建议
        if result.missing_skills:
            recommendations.append(f"建议学习以下技能以提高匹配度: {', '.join(result.missing_skills[:5])}")
        
        # 基于经验的建议
        if result.min_experience_years and user_profile:
            user_exp = user_profile.get('experience_years', 0)
            if user_exp < result.min_experience_years:
                recommendations.append(f"该职位要求{result.min_experience_years}年经验，您目前有{user_exp}年，建议突出相关项目经验")
        
        # 基于薪资的建议
        if result.salary_info and result.salary_info.min_salary:
            recommendations.append(f"该职位薪资范围: ${result.salary_info.min_salary:,} - ${result.salary_info.max_salary or result.salary_info.min_salary:,}")
        
        # 基于公司的建议
        if result.company_info and result.company_info.industry:
            recommendations.append(f"该公司属于{result.company_info.industry}行业，建议了解相关行业知识")
        
        result.recommendations.extend(recommendations)
    
    def _calculate_final_scores(self, result: JobAnalysisResult, user_profile: Optional[Dict[str, Any]]) -> None:
        """计算最终评分"""
        if user_profile:
            # 权重配置
            weights = self.config.get('compatibility_scoring', {}).get('weights', {
                'skills_match': 0.4,
                'experience_level': 0.2,
                'location_preference': 0.15,
                'salary_expectation': 0.15,
                'company_culture': 0.1
            })
            
            # 计算综合兼容性评分
            compatibility_score = (
                result.skill_match_score * weights['skills_match'] +
                result.experience_match_score * weights['experience_level'] +
                result.location_match_score * weights['location_preference']
                # 其他因素可以后续添加
            )
            
            result.compatibility_score = min(compatibility_score, 1.0)
        else:
            result.compatibility_score = 0.0
    
    def _calculate_confidence_score(self, result: JobAnalysisResult) -> float:
        """计算分析置信度"""
        confidence_factors = []
        
        # 数据源数量
        confidence_factors.append(min(len(result.data_sources) / 5, 1.0))
        
        # 技能识别置信度
        if result.required_skills:
            avg_skill_confidence = sum(skill.confidence for skill in result.required_skills) / len(result.required_skills)
            confidence_factors.append(avg_skill_confidence)
        
        # 信息完整性
        completeness = 0
        if result.job_description: completeness += 0.2
        if result.company_info: completeness += 0.2
        if result.salary_info and result.salary_info.min_salary: completeness += 0.2
        if result.required_skills: completeness += 0.2
        if result.location: completeness += 0.2
        
        confidence_factors.append(completeness)
        
        return sum(confidence_factors) / len(confidence_factors) if confidence_factors else 0.0


# 便捷函数
async def analyze_job_posting(job_data: Dict[str, Any], 
                             analysis_level: JobAnalysisLevel = JobAnalysisLevel.STANDARD,
                             user_profile: Optional[Dict[str, Any]] = None,
                             config: Optional[Dict[str, Any]] = None) -> JobAnalysisResult:
    """分析职位发布
    
    Args:
        job_data: 职位数据
        analysis_level: 分析深度级别
        user_profile: 用户档案
        config: 分析器配置
    
    Returns:
        JobAnalysisResult: 分析结果
    """
    analyzer = JobAnalyzer(config)
    return await analyzer.analyze_job(job_data, analysis_level, user_profile)


if __name__ == "__main__":
    # 测试代码
    async def test_job_analyzer():
        """测试职位分析器"""
        # 模拟职位数据
        job_data = {
            'id': 'test_job_001',
            'title': 'Senior Python Developer',
            'company': 'Tech Corp',
            'location': 'San Francisco, CA',
            'description': '''
            We are looking for a Senior Python Developer with 5+ years of experience.
            
            Required skills:
            - Python, Django, Flask
            - PostgreSQL, Redis
            - AWS, Docker
            - Git, CI/CD
            
            Preferred skills:
            - React, JavaScript
            - Machine Learning
            - Kubernetes
            
            Salary: $120,000 - $150,000
            Remote work available.
            ''',
            'employment_type': 'full_time'
        }
        
        # 模拟用户档案
        user_profile = {
            'skills': ['python', 'django', 'postgresql', 'aws', 'git'],
            'experience_years': 6,
            'location': 'San Francisco, CA'
        }
        
        # 执行分析
        analyzer = JobAnalyzer()
        result = await analyzer.analyze_job(job_data, JobAnalysisLevel.COMPREHENSIVE, user_profile)
        
        print(f"分析结果:")
        print(f"职位: {result.job_title}")
        print(f"公司: {result.company_name}")
        print(f"兼容性评分: {result.compatibility_score:.2f}")
        print(f"技能匹配度: {result.skill_match_score:.2f}")
        print(f"经验匹配度: {result.experience_match_score:.2f}")
        print(f"必需技能: {[skill.name for skill in result.required_skills]}")
        print(f"优选技能: {[skill.name for skill in result.preferred_skills]}")
        print(f"缺失技能: {result.missing_skills}")
        print(f"建议: {result.recommendations}")
        print(f"分析耗时: {result.analysis_duration:.2f}秒")
        print(f"置信度: {result.confidence_score:.2f}")
    
    # 运行测试
    asyncio.run(test_job_analyzer())