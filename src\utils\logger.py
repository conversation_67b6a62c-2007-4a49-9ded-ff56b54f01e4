# -*- coding: utf-8 -*-
"""
日志配置模块
提供统一的日志记录功能
"""

import logging
import sys
from pathlib import Path
from typing import Optional

# 尝试导入loguru，如果不可用则使用标准logging
try:
    from loguru import logger as loguru_logger
    LOGURU_AVAILABLE = True
except ImportError:
    LOGURU_AVAILABLE = False
    loguru_logger = None


class Logger:
    """统一的日志记录器"""
    
    def __init__(self, name: str = "AIHawk", level: str = "INFO"):
        self.name = name
        self.level = level
        
        if LOGURU_AVAILABLE:
            self._logger = loguru_logger
            self._setup_loguru()
        else:
            self._logger = logging.getLogger(name)
            self._setup_standard_logging()
    
    def _setup_loguru(self):
        """配置loguru日志"""
        # 移除默认处理器
        self._logger.remove()
        
        # 添加控制台输出
        self._logger.add(
            sys.stderr,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level=self.level
        )
        
        # 添加文件输出（如果需要）
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        self._logger.add(
            log_dir / "aihawk.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level=self.level,
            rotation="10 MB",
            retention="7 days"
        )
    
    def _setup_standard_logging(self):
        """配置标准logging"""
        # 设置日志级别
        self._logger.setLevel(getattr(logging, self.level.upper()))
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)-8s | %(name)s:%(funcName)s:%(lineno)d - %(message)s'
        )
        
        # 添加控制台处理器
        if not self._logger.handlers:
            console_handler = logging.StreamHandler(sys.stderr)
            console_handler.setFormatter(formatter)
            self._logger.addHandler(console_handler)
            
            # 添加文件处理器
            log_dir = Path("logs")
            log_dir.mkdir(exist_ok=True)
            
            file_handler = logging.FileHandler(log_dir / "aihawk.log")
            file_handler.setFormatter(formatter)
            self._logger.addHandler(file_handler)
    
    def debug(self, message: str, *args, **kwargs):
        """调试日志"""
        if LOGURU_AVAILABLE:
            self._logger.debug(message, *args, **kwargs)
        else:
            self._logger.debug(message, *args, **kwargs)
    
    def info(self, message: str, *args, **kwargs):
        """信息日志"""
        if LOGURU_AVAILABLE:
            self._logger.info(message, *args, **kwargs)
        else:
            self._logger.info(message, *args, **kwargs)
    
    def warning(self, message: str, *args, **kwargs):
        """警告日志"""
        if LOGURU_AVAILABLE:
            self._logger.warning(message, *args, **kwargs)
        else:
            self._logger.warning(message, *args, **kwargs)
    
    def error(self, message: str, *args, **kwargs):
        """错误日志"""
        if LOGURU_AVAILABLE:
            self._logger.error(message, *args, **kwargs)
        else:
            self._logger.error(message, *args, **kwargs)
    
    def critical(self, message: str, *args, **kwargs):
        """严重错误日志"""
        if LOGURU_AVAILABLE:
            self._logger.critical(message, *args, **kwargs)
        else:
            self._logger.critical(message, *args, **kwargs)
    
    def exception(self, message: str, *args, **kwargs):
        """异常日志"""
        if LOGURU_AVAILABLE:
            self._logger.exception(message, *args, **kwargs)
        else:
            self._logger.exception(message, *args, **kwargs)


# 创建全局日志实例
logger = Logger()

# 为了兼容性，也提供标准的logging接口
logging_logger = logging.getLogger("AIHawk")


def get_logger(name: Optional[str] = None, level: str = "INFO") -> Logger:
    """获取日志记录器实例"""
    if name:
        return Logger(name, level)
    return logger


def set_log_level(level: str):
    """设置全局日志级别"""
    global logger
    logger.level = level
    if LOGURU_AVAILABLE:
        logger._logger.remove()
        logger._setup_loguru()
    else:
        logger._logger.setLevel(getattr(logging, level.upper()))


# 兼容性别名
log = logger