#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP增强的LinkedIn自动化模块
集成mult-fetch-mcp-server和Context7-MCP工具，提升抓取能力和AI分析功能
保持与现有Selenium/Undetected ChromeDriver的兼容性
"""

import asyncio
import json
import time
import random
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from loguru import logger
from dataclasses import dataclass

try:
    import httpx
except ImportError:
    httpx = None
    logger.warning("httpx未安装，MCP功能将被禁用")

@dataclass
class MCPConfig:
    """MCP配置类"""
    enabled: bool = True
    fetch_server: str = "mcp.config.usrlocalmcp.mult-fetch-mcp-server"
    context7_server: str = "mcp.config.usrlocalmcp.github.com/upstash/context7-mcp"
    timeout: int = 30
    max_retries: int = 3
    fallback_to_selenium: bool = True

class MCPEnhancedAutomation:
    """MCP增强的自动化类 - 作为现有系统的增强层"""
    
    def __init__(self, base_automation=None, config: MCPConfig = None):
        """
        初始化MCP增强模块
        
        Args:
            base_automation: 现有的LinkedIn自动化实例（Selenium或Playwright）
            config: MCP配置
        """
        self.base_automation = base_automation
        self.config = config or MCPConfig()
        self.mcp_available = self._check_mcp_availability()
        self.session_cache = {}  # 会话缓存
        
        if not self.mcp_available:
            logger.warning("MCP功能不可用，将使用传统方式")
            self.config.enabled = False
    
    def _check_mcp_availability(self) -> bool:
        """检查MCP工具是否可用"""
        try:
            # 检查必要的依赖
            if httpx is None:
                return False
            
            # 检查MCP配置文件
            mcp_config_path = Path(__file__).parent.parent / "mcp-config.json"
            if not mcp_config_path.exists():
                logger.warning(f"MCP配置文件不存在: {mcp_config_path}")
                return False
            
            return True
        except Exception as e:
            logger.error(f"MCP可用性检查失败: {e}")
            return False
    
    async def run_mcp_tool(self, server_name: str, tool_name: str, args: Dict) -> Optional[Dict]:
        """运行MCP工具的通用方法"""
        if not self.config.enabled or not self.mcp_available:
            return None
        
        try:
            # 这里应该调用实际的MCP工具
            # 由于当前环境限制，我们模拟MCP调用
            logger.info(f"调用MCP工具: {server_name}.{tool_name}")
            
            # 模拟异步调用
            await asyncio.sleep(0.1)
            
            # 返回模拟结果
            return {
                "success": True,
                "server": server_name,
                "tool": tool_name,
                "args": args,
                "timestamp": time.time()
            }
            
        except Exception as e:
            logger.error(f"MCP工具调用失败 {server_name}.{tool_name}: {e}")
            return None
    
    async def enhanced_fetch_page(self, url: str, extract_content: bool = True) -> Optional[Dict]:
        """使用MCP工具增强页面抓取"""
        if not self.config.enabled:
            return await self._fallback_fetch(url)
        
        try:
            # 使用mult-fetch-mcp-server抓取页面
            result = await self.run_mcp_tool(
                server_name=self.config.fetch_server,
                tool_name="fetch_html",
                args={
                    "url": url,
                    "startCursor": 0,
                    "extractContent": extract_content,
                    "includeMetadata": True,
                    "useBrowser": False,  # 避免浏览器检测
                    "autoDetectMode": True,
                    "contentSizeLimit": 30000,
                    "enableContentSplitting": True
                }
            )
            
            if result and result.get("success"):
                logger.info(f"MCP抓取成功: {url}")
                return {
                    "content": result.get("content", ""),
                    "metadata": result.get("metadata", {}),
                    "method": "mcp",
                    "url": url
                }
            else:
                logger.warning(f"MCP抓取失败，回退到传统方式: {url}")
                return await self._fallback_fetch(url)
                
        except Exception as e:
            logger.error(f"MCP抓取异常: {e}")
            return await self._fallback_fetch(url)
    
    async def _fallback_fetch(self, url: str) -> Optional[Dict]:
        """回退到传统抓取方式"""
        if not self.config.fallback_to_selenium or not self.base_automation:
            return None
        
        try:
            # 使用现有的Selenium/Playwright方式
            logger.info(f"使用传统方式抓取: {url}")
            
            # 如果有base_automation，调用其方法
            if hasattr(self.base_automation, 'driver'):
                # Selenium方式
                self.base_automation.driver.get(url)
                time.sleep(2)
                content = self.base_automation.driver.page_source
                
                return {
                    "content": content,
                    "metadata": {"title": self.base_automation.driver.title},
                    "method": "selenium",
                    "url": url
                }
            elif hasattr(self.base_automation, 'page'):
                # Playwright方式
                await self.base_automation.page.goto(url)
                content = await self.base_automation.page.content()
                title = await self.base_automation.page.title()
                
                return {
                    "content": content,
                    "metadata": {"title": title},
                    "method": "playwright",
                    "url": url
                }
            
        except Exception as e:
            logger.error(f"传统抓取方式也失败: {e}")
            return None
    
    async def analyze_tech_stack(self, job_description: str) -> Dict:
        """使用Context7-MCP分析技术栈"""
        if not self.config.enabled:
            return self._basic_tech_analysis(job_description)
        
        try:
            # 提取技术关键词
            tech_keywords = self._extract_tech_keywords(job_description)
            analysis_results = {}
            
            for tech in tech_keywords[:5]:  # 限制分析数量
                try:
                    # 解析技术库ID
                    library_result = await self.run_mcp_tool(
                        server_name=self.config.context7_server,
                        tool_name="resolve-library-id",
                        args={"libraryName": tech}
                    )
                    
                    if library_result and library_result.get("success"):
                        # 获取技术文档
                        docs_result = await self.run_mcp_tool(
                            server_name=self.config.context7_server,
                            tool_name="get-library-docs",
                            args={
                                "context7CompatibleLibraryID": f"/{tech.lower()}",
                                "topic": "overview",
                                "tokens": 3000
                            }
                        )
                        
                        if docs_result and docs_result.get("success"):
                            analysis_results[tech] = {
                                "relevance": "high",
                                "documentation": docs_result.get("content", ""),
                                "analysis_method": "context7"
                            }
                        
                except Exception as e:
                    logger.warning(f"Context7分析失败 {tech}: {e}")
                    analysis_results[tech] = {
                        "relevance": "medium",
                        "analysis_method": "basic"
                    }
            
            return {
                "tech_stack": analysis_results,
                "total_technologies": len(tech_keywords),
                "analyzed_count": len(analysis_results),
                "method": "context7_enhanced"
            }
            
        except Exception as e:
            logger.error(f"技术栈分析失败: {e}")
            return self._basic_tech_analysis(job_description)
    
    def _extract_tech_keywords(self, text: str) -> List[str]:
        """提取技术关键词"""
        # 常见技术栈关键词
        tech_patterns = [
            'python', 'java', 'javascript', 'react', 'vue', 'angular',
            'node.js', 'django', 'flask', 'spring', 'docker', 'kubernetes',
            'aws', 'azure', 'gcp', 'mysql', 'postgresql', 'mongodb',
            'redis', 'elasticsearch', 'git', 'jenkins', 'terraform'
        ]
        
        text_lower = text.lower()
        found_techs = []
        
        for tech in tech_patterns:
            if tech in text_lower:
                found_techs.append(tech)
        
        return found_techs
    
    def _basic_tech_analysis(self, job_description: str) -> Dict:
        """基础技术栈分析（不使用MCP）"""
        tech_keywords = self._extract_tech_keywords(job_description)
        
        return {
            "tech_stack": {tech: {"relevance": "detected", "analysis_method": "basic"} 
                          for tech in tech_keywords},
            "total_technologies": len(tech_keywords),
            "analyzed_count": len(tech_keywords),
            "method": "basic"
        }
    
    async def enhanced_job_analysis(self, job_url: str, job_data: Dict = None) -> Dict:
        """增强的职位分析"""
        analysis_result = {
            "url": job_url,
            "timestamp": time.time(),
            "analysis_methods": []
        }
        
        try:
            # 1. 增强页面抓取
            if not job_data:
                fetch_result = await self.enhanced_fetch_page(job_url)
                if fetch_result:
                    analysis_result["content"] = fetch_result["content"]
                    analysis_result["metadata"] = fetch_result["metadata"]
                    analysis_result["fetch_method"] = fetch_result["method"]
                    analysis_result["analysis_methods"].append("enhanced_fetch")
            
            # 2. 技术栈分析
            job_description = job_data.get("description", "") if job_data else analysis_result.get("content", "")
            if job_description:
                tech_analysis = await self.analyze_tech_stack(job_description)
                analysis_result["tech_analysis"] = tech_analysis
                analysis_result["analysis_methods"].append("tech_stack_analysis")
            
            # 3. 兼容性评分
            if job_data:
                compatibility_score = self._calculate_compatibility(job_data, tech_analysis)
                analysis_result["compatibility_score"] = compatibility_score
                analysis_result["analysis_methods"].append("compatibility_scoring")
            
            logger.info(f"职位分析完成: {job_url}, 方法: {analysis_result['analysis_methods']}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"职位分析失败: {e}")
            analysis_result["error"] = str(e)
            return analysis_result
    
    def _calculate_compatibility(self, job_data: Dict, tech_analysis: Dict) -> float:
        """计算职位兼容性评分"""
        try:
            # 简单的兼容性评分算法
            tech_stack = tech_analysis.get("tech_stack", {})
            total_techs = len(tech_stack)
            
            if total_techs == 0:
                return 0.5  # 默认中等兼容性
            
            # 基于技术栈匹配度计算分数
            high_relevance_count = sum(1 for tech_info in tech_stack.values() 
                                     if tech_info.get("relevance") == "high")
            
            score = min(1.0, (high_relevance_count / total_techs) + 0.3)
            return round(score, 2)
            
        except Exception as e:
            logger.error(f"兼容性评分计算失败: {e}")
            return 0.5
    
    def get_enhancement_status(self) -> Dict:
        """获取增强功能状态"""
        return {
            "mcp_enabled": self.config.enabled,
            "mcp_available": self.mcp_available,
            "fetch_server": self.config.fetch_server,
            "context7_server": self.config.context7_server,
            "fallback_enabled": self.config.fallback_to_selenium,
            "base_automation_type": type(self.base_automation).__name__ if self.base_automation else None
        }

# 工厂函数，用于创建增强的自动化实例
def create_enhanced_automation(base_automation=None, enable_mcp: bool = True) -> MCPEnhancedAutomation:
    """创建MCP增强的自动化实例"""
    config = MCPConfig(enabled=enable_mcp)
    return MCPEnhancedAutomation(base_automation, config)

# 异步上下文管理器
class AsyncMCPEnhancedAutomation:
    """异步MCP增强自动化上下文管理器"""
    
    def __init__(self, base_automation=None, config: MCPConfig = None):
        self.enhanced_automation = MCPEnhancedAutomation(base_automation, config)
    
    async def __aenter__(self):
        return self.enhanced_automation
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # 清理资源
        if hasattr(self.enhanced_automation, 'session_cache'):
            self.enhanced_automation.session_cache.clear()
        logger.info("MCP增强自动化会话结束")