# LinkedIn自动化配置文件
linkedin:
  # 登录信息
  email: "<EMAIL>"  # 请替换为您的LinkedIn邮箱
  password: "Ajh20181102@@"         # 请替换为您的LinkedIn密码
  
  # 搜索参数
  search_keywords:
    - "python developer"
    - "software engineer"
    - "data scientist"
    - "full stack developer"
    - "backend developer"
  
  location: "Shanghai,China"  # 搜索地点
  
  # 筛选条件
  experience_level:
    - "Entry level"
    - "Associate"
    - "Mid-Senior level"
  
  job_type:
    - "Full-time"
    - "Part-time"
    - "Contract"
  
  remote_work:
    - "On-site"
    - "Remote"
    - "Hybrid"
  
  # 申请限制
  max_applications_per_day: 50
  delay_between_applications: [30, 60]  # 申请间隔时间（秒）
  
  # 自动回答问题
  auto_answer_questions: true
  default_answers:
    years_experience: "3"
    willing_to_relocate: "Yes"
    authorized_to_work: "Yes"
    require_sponsorship: "No"
    phone_number: "1234567890"
    cover_letter: "I am very interested in this position and believe my skills and experience make me a great fit for your team."

# Selenium配置
selenium:
  headless: false          # 是否无头模式
  implicit_wait: 10        # 隐式等待时间
  page_load_timeout: 30    # 页面加载超时
  window_size: [1920, 1080] # 浏览器窗口大小
  use_undetected: true     # 是否使用Undetected ChromeDriver（推荐）

# Playwright配置
playwright:
  headless: false
  timeout: 30000
  window_size: [1200, 800]

# 新增配置项，用于选择自动化工具
automation_tool: selenium  # 可选值：selenium, playwright, playwright_async

# 日志配置
logging:
  level: "INFO"
  file: "linkedin_automation.log"
  max_file_size: "10MB"
  backup_count: 5